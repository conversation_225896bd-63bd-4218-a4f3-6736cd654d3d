import React, { useState, useEffect } from 'react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Select from '../ui/Select';
import Icon from '../AppIcon';
import ProjectExportButton from '../ui/ProjectExportButton';

const EnhancedCreateAIProjectModal = ({ isOpen, onClose, onCreateAIProject, organizationId, organizationName }) => {
  const [formData, setFormData] = useState({
    name: '',
    projectType: 'general',
    teamSize: 5,
    teamExperience: 'intermediate'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [projectPreview, setProjectPreview] = useState(null);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);

  const projectTypes = [
    { value: 'general', label: 'General Project', description: 'Standard project with flexible workflow', icon: 'Folder', color: 'bg-gray-500' },
    { value: 'web_application', label: 'Web Application', description: 'Full-stack web development project', icon: 'Globe', color: 'bg-blue-500' },
    { value: 'mobile_app', label: 'Mobile App', description: 'iOS/Android mobile application', icon: 'Smartphone', color: 'bg-green-500' },
    { value: 'ecommerce_platform', label: 'E-commerce Platform', description: 'Online marketplace with payment processing', icon: 'ShoppingCart', color: 'bg-purple-500' },
    { value: 'saas_application', label: 'SaaS Application', description: 'Multi-tenant cloud-based software', icon: 'Cloud', color: 'bg-indigo-500' },
    { value: 'devops_infrastructure', label: 'DevOps/Infrastructure', description: 'CI/CD pipelines and infrastructure automation', icon: 'Server', color: 'bg-orange-500' }
  ];

  const teamSizeOptions = [
    { value: 2, label: '2 people (Small team)' },
    { value: 3, label: '3 people (Small team)' },
    { value: 5, label: '5 people (Optimal team)' },
    { value: 8, label: '8 people (Large team)' },
    { value: 12, label: '12+ people (Enterprise team)' }
  ];

  const experienceOptions = [
    { value: 'junior', label: 'Junior (0-2 years)' },
    { value: 'intermediate', label: 'Intermediate (2-5 years)' },
    { value: 'senior', label: 'Senior (5+ years)' },
    { value: 'expert', label: 'Expert (10+ years)' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError('');
  };

  const generatePreview = async () => {
    if (!formData.name.trim()) {
      setError('Project name is required for preview');
      return;
    }

    setIsGeneratingPreview(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const selectedType = projectTypes.find(type => type.value === formData.projectType);
      
      setProjectPreview({
        name: formData.name,
        type: selectedType,
        estimatedDuration: getEstimatedDuration(formData.projectType, formData.teamSize, formData.teamExperience),
        estimatedTasks: getEstimatedTaskCount(formData.projectType),
        phases: getProjectPhases(formData.projectType),
        teamRecommendations: getTeamRecommendations(formData.teamSize, formData.teamExperience),
        technologies: getTechnologies(formData.projectType)
      });
      
      setCurrentStep(2);
    } catch (err) {
      setError('Failed to generate preview');
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  const getEstimatedDuration = (type, teamSize, experience) => {
    const baseDurations = {
      'web_application': 50, 'mobile_app': 57, 'ecommerce_platform': 87,
      'saas_application': 97, 'devops_infrastructure': 52, 'general': 43
    };
    
    let duration = baseDurations[type] || 43;
    const experienceMultipliers = { junior: 1.3, intermediate: 1.0, senior: 0.8, expert: 0.7 };
    duration *= experienceMultipliers[experience] || 1.0;
    
    if (teamSize <= 2) duration *= 1.2;
    else if (teamSize >= 8) duration *= 0.9;
    
    return Math.round(duration);
  };

  const getEstimatedTaskCount = (type) => {
    const taskCounts = {
      'web_application': 25, 'mobile_app': 28, 'ecommerce_platform': 45,
      'saas_application': 52, 'devops_infrastructure': 22, 'general': 15
    };
    return taskCounts[type] || 15;
  };

  const getProjectPhases = (type) => {
    const phases = {
      'web_application': ['Planning & Analysis', 'Design & Architecture', 'Development', 'Testing & QA', 'Deployment & Launch'],
      'ecommerce_platform': ['Market Research & Planning', 'Core Platform Development', 'Payment & Security Integration', 'Advanced Features & Optimization', 'Testing & Launch']
    };
    return phases[type] || ['Initiation & Planning', 'Execution Phase 1', 'Execution Phase 2', 'Finalization & Review', 'Closure & Handover'];
  };

  const getTeamRecommendations = (size, experience) => {
    const recommendations = [];
    if (size <= 2) recommendations.push('Consider adding more team members for complex phases');
    if (size >= 8) recommendations.push('Break into smaller sub-teams to reduce coordination overhead');
    if (experience === 'junior') recommendations.push('Assign senior mentor for guidance');
    if (experience === 'expert') recommendations.push('Leverage team expertise for innovation opportunities');
    return recommendations;
  };

  const getTechnologies = (type) => {
    const tech = {
      'web_application': ['React/Vue.js', 'Node.js/Python', 'PostgreSQL', 'Docker', 'AWS/Azure'],
      'ecommerce_platform': ['React/Next.js', 'Node.js/Express', 'PostgreSQL', 'Stripe/PayPal', 'Redis', 'Docker']
    };
    return tech[type] || ['Modern Framework', 'Backend Technology', 'Database', 'Cloud Platform'];
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (currentStep === 1) {
      generatePreview();
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const projectData = {
        name: formData.name.trim(),
        project_type: formData.projectType,
        team_size: formData.teamSize,
        team_experience: formData.teamExperience,
        organization_id: organizationId
      };

      await onCreateAIProject(projectData);
      handleClose();
    } catch (err) {
      setError(err.message || 'Failed to create AI project');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading && !isGeneratingPreview) {
      setFormData({ name: '', projectType: 'general', teamSize: 5, teamExperience: 'intermediate' });
      setError('');
      setCurrentStep(1);
      setProjectPreview(null);
      onClose();
    }
  };

  const handleBack = () => {
    setCurrentStep(1);
    setProjectPreview(null);
    setError('');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Icon name="Sparkles" size={20} className="text-white" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-text-primary">Create AI Project</h2>
              <p className="text-sm text-text-secondary">Let AI generate your complete project structure</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            disabled={isLoading || isGeneratingPreview}
            iconName="X"
            className="text-text-secondary hover:text-text-primary"
          />
        </div>

        {/* Step Indicator */}
        <div className="px-6 py-4 bg-muted">
          <div className="flex items-center justify-center space-x-4">
            <div className={`flex items-center ${currentStep >= 1 ? 'text-purple-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 1 ? 'bg-purple-600 text-white' : 'bg-gray-200'}`}>
                1
              </div>
              <span className="ml-2 text-sm font-medium">Configure</span>
            </div>
            <div className={`w-16 h-0.5 ${currentStep >= 2 ? 'bg-purple-600' : 'bg-gray-200'}`}></div>
            <div className={`flex items-center ${currentStep >= 2 ? 'text-purple-600' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 2 ? 'bg-purple-600 text-white' : 'bg-gray-200'}`}>
                2
              </div>
              <span className="ml-2 text-sm font-medium">Preview & Create</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {currentStep === 1 ? (
            <ConfigurationStep 
              formData={formData}
              projectTypes={projectTypes}
              teamSizeOptions={teamSizeOptions}
              experienceOptions={experienceOptions}
              onInputChange={handleInputChange}
              onSubmit={handleSubmit}
              isGeneratingPreview={isGeneratingPreview}
              error={error}
              organizationName={organizationName}
            />
          ) : (
            <PreviewStep 
              projectPreview={projectPreview}
              onSubmit={handleSubmit}
              onBack={handleBack}
              isLoading={isLoading}
              error={error}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// Configuration Step Component
const ConfigurationStep = ({ formData, projectTypes, teamSizeOptions, experienceOptions, onInputChange, onSubmit, isGeneratingPreview, error, organizationName }) => (
  <form onSubmit={onSubmit} className="space-y-6">
    {/* Organization Info */}
    <div className="bg-muted rounded-lg p-4">
      <div className="flex items-center gap-2 text-sm text-text-secondary">
        <Icon name="Building2" size={16} />
        <span>Creating in: <strong className="text-text-primary">{organizationName}</strong></span>
      </div>
    </div>

    {/* Error Message */}
    {error && (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center gap-2 text-red-700">
          <Icon name="AlertCircle" size={16} />
          <span className="text-sm font-medium">{error}</span>
        </div>
      </div>
    )}

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Left Column */}
      <div className="space-y-6">
        {/* Project Name */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-text-primary">Project Name *</label>
          <Input
            type="text"
            value={formData.name}
            onChange={(e) => onInputChange('name', e.target.value)}
            placeholder="Enter your project name..."
            disabled={isGeneratingPreview}
            className="w-full"
            required
          />
        </div>

        {/* Team Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-text-primary">Team Configuration</h3>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">Team Size</label>
            <Select
              value={formData.teamSize}
              onChange={(value) => onInputChange('teamSize', value)}
              options={teamSizeOptions}
              disabled={isGeneratingPreview}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary">Team Experience Level</label>
            <Select
              value={formData.teamExperience}
              onChange={(value) => onInputChange('teamExperience', value)}
              options={experienceOptions}
              disabled={isGeneratingPreview}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Right Column - Project Type Selector */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-text-primary">Project Type</h3>
        <div className="grid grid-cols-1 gap-3 max-h-96 overflow-y-auto">
          {projectTypes.map((type) => (
            <div
              key={type.value}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                formData.projectType === type.value
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => onInputChange('projectType', type.value)}
            >
              <div className="flex items-start gap-3">
                <div className={`w-10 h-10 ${type.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
                  <Icon name={type.icon} size={20} className="text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-text-primary">{type.label}</h4>
                  <p className="text-sm text-text-secondary mt-1">{type.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* Actions */}
    <div className="flex items-center justify-end gap-3 pt-4 border-t border-border">
      <Button
        type="submit"
        variant="default"
        disabled={isGeneratingPreview || !formData.name.trim()}
        iconName={isGeneratingPreview ? "Loader2" : "ArrowRight"}
        iconPosition="right"
        className={`${isGeneratingPreview ? "animate-spin" : ""} bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 border-0`}
      >
        {isGeneratingPreview ? 'Generating Preview...' : 'Generate Preview'}
      </Button>
    </div>
  </form>
);

// Preview Step Component
const PreviewStep = ({ projectPreview, onSubmit, onBack, isLoading, error }) => (
  <div className="space-y-6">
    {/* Error Message */}
    {error && (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center gap-2 text-red-700">
          <Icon name="AlertCircle" size={16} />
          <span className="text-sm font-medium">{error}</span>
        </div>
      </div>
    )}

    {/* Project Overview */}
    <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
      <div className="flex items-center gap-3 mb-4">
        <div className={`w-12 h-12 ${projectPreview?.type?.color || 'bg-gray-500'} rounded-lg flex items-center justify-center`}>
          <Icon name={projectPreview?.type?.icon || 'Folder'} size={24} className="text-white" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-purple-900">{projectPreview?.name}</h2>
          <p className="text-purple-700">{projectPreview?.type?.label}</p>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-900">{projectPreview?.estimatedDuration}</div>
          <div className="text-sm text-purple-700">Days</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-900">{projectPreview?.estimatedTasks}</div>
          <div className="text-sm text-purple-700">Tasks</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-900">{projectPreview?.phases?.length || 0}</div>
          <div className="text-sm text-purple-700">Phases</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-900">{projectPreview?.technologies?.length || 0}</div>
          <div className="text-sm text-purple-700">Technologies</div>
        </div>
      </div>
    </div>

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Project Phases */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-text-primary flex items-center gap-2">
          <Icon name="GitBranch" size={20} />
          Project Phases
        </h3>
        <div className="space-y-3">
          {projectPreview?.phases?.map((phase, index) => (
            <div key={index} className="flex items-center gap-3 p-3 bg-muted rounded-lg">
              <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                {index + 1}
              </div>
              <span className="font-medium text-text-primary">{phase}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Technologies */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-text-primary flex items-center gap-2">
          <Icon name="Code" size={20} />
          Recommended Technologies
        </h3>
        <div className="flex flex-wrap gap-2">
          {projectPreview?.technologies?.map((tech, index) => (
            <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
              {tech}
            </span>
          ))}
        </div>

        {/* Team Recommendations */}
        {projectPreview?.teamRecommendations?.length > 0 && (
          <div className="mt-6">
            <h4 className="text-md font-medium text-text-primary flex items-center gap-2 mb-3">
              <Icon name="Users" size={18} />
              Team Recommendations
            </h4>
            <div className="space-y-2">
              {projectPreview.teamRecommendations.map((rec, index) => (
                <div key={index} className="flex items-start gap-2 text-sm text-text-secondary">
                  <Icon name="CheckCircle" size={16} className="text-green-500 mt-0.5 flex-shrink-0" />
                  <span>{rec}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>

    {/* AI Features Info */}
    <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
      <h3 className="text-sm font-medium text-green-900 mb-2 flex items-center gap-2">
        <Icon name="Sparkles" size={16} />
        What AI will generate for you:
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-green-800">
        <div>• Comprehensive project description and objectives</div>
        <div>• Detailed workflow with phases and milestones</div>
        <div>• Complete task breakdown with priorities and estimates</div>
        <div>• Kanban board with organized task categories</div>
        <div>• Project timeline and resource recommendations</div>
        <div>• Risk assessment and mitigation strategies</div>
      </div>
    </div>

    <div className="flex items-center justify-between pt-4 border-t border-border">
      <div className="flex items-center gap-3">
        <Button variant="outline" onClick={onBack} disabled={isLoading}>
          <Icon name="ArrowLeft" size={16} className="mr-2" />
          Back to Configuration
        </Button>
        <ProjectExportButton projectData={projectPreview} />
      </div>
      <Button
        onClick={onSubmit}
        disabled={isLoading}
        iconName={isLoading ? "Loader2" : "Sparkles"}
        iconPosition="left"
        className={`${isLoading ? "animate-spin" : ""} bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 border-0`}
      >
        {isLoading ? 'Creating Project...' : 'Create AI Project'}
      </Button>
    </div>
  </div>
);

export default EnhancedCreateAIProjectModal;
