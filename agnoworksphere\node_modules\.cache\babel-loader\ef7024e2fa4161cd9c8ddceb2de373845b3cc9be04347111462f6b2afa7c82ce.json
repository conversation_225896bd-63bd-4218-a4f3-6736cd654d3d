{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\project\\\\TechStackDisplay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from '../ui/Button';\nimport Modal from '../ui/Modal';\nimport Icon from '../AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TechStackDisplay = ({\n  onNext,\n  onBack,\n  initialData = {},\n  className\n}) => {\n  _s();\n  const [selectedTechnologies, setSelectedTechnologies] = useState({\n    frontend: [],\n    backend: [],\n    database: [],\n    cloud: [],\n    tools: [],\n    ...initialData\n  });\n  const [selectedTech, setSelectedTech] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const techCategories = {\n    frontend: {\n      title: 'Frontend Technologies',\n      icon: 'Monitor',\n      description: 'User interface and client-side technologies',\n      technologies: [{\n        id: 'react',\n        name: 'React',\n        description: 'A JavaScript library for building user interfaces',\n        icon: '⚛️',\n        popularity: 95,\n        learningCurve: 'Medium',\n        documentation: 'https://reactjs.org/docs',\n        pros: ['Component-based', 'Large ecosystem', 'Strong community'],\n        cons: ['Steep learning curve', 'Frequent updates'],\n        useCases: ['SPAs', 'Web applications', 'Mobile apps with React Native']\n      }, {\n        id: 'vue',\n        name: 'Vue.js',\n        description: 'Progressive JavaScript framework for building UIs',\n        icon: '🟢',\n        popularity: 85,\n        learningCurve: 'Easy',\n        documentation: 'https://vuejs.org/guide/',\n        pros: ['Easy to learn', 'Flexible', 'Good performance'],\n        cons: ['Smaller ecosystem', 'Less job market'],\n        useCases: ['Progressive enhancement', 'SPAs', 'Prototyping']\n      }, {\n        id: 'angular',\n        name: 'Angular',\n        description: 'Platform for building mobile and desktop web applications',\n        icon: '🅰️',\n        popularity: 75,\n        learningCurve: 'Hard',\n        documentation: 'https://angular.io/docs',\n        pros: ['Full framework', 'TypeScript support', 'Enterprise-ready'],\n        cons: ['Complex', 'Heavy', 'Steep learning curve'],\n        useCases: ['Enterprise applications', 'Large-scale projects']\n      }, {\n        id: 'svelte',\n        name: 'Svelte',\n        description: 'Cybernetically enhanced web apps',\n        icon: '🔥',\n        popularity: 70,\n        learningCurve: 'Easy',\n        documentation: 'https://svelte.dev/docs',\n        pros: ['No virtual DOM', 'Small bundle size', 'Easy to learn'],\n        cons: ['Smaller ecosystem', 'Less tooling'],\n        useCases: ['Performance-critical apps', 'Small to medium projects']\n      }]\n    },\n    backend: {\n      title: 'Backend Technologies',\n      icon: 'Server',\n      description: 'Server-side technologies and frameworks',\n      technologies: [{\n        id: 'nodejs',\n        name: 'Node.js',\n        description: 'JavaScript runtime built on Chrome\\'s V8 JavaScript engine',\n        icon: '🟢',\n        popularity: 90,\n        learningCurve: 'Medium',\n        documentation: 'https://nodejs.org/en/docs/',\n        pros: ['JavaScript everywhere', 'Fast development', 'Large ecosystem'],\n        cons: ['Single-threaded', 'Not ideal for CPU-intensive tasks'],\n        useCases: ['APIs', 'Real-time applications', 'Microservices']\n      }, {\n        id: 'python',\n        name: 'Python/Django',\n        description: 'High-level Python web framework',\n        icon: '🐍',\n        popularity: 85,\n        learningCurve: 'Easy',\n        documentation: 'https://docs.djangoproject.com/',\n        pros: ['Rapid development', 'Batteries included', 'Great for AI/ML'],\n        cons: ['Performance limitations', 'GIL limitations'],\n        useCases: ['Web applications', 'APIs', 'Data science applications']\n      }, {\n        id: 'java',\n        name: 'Java/Spring',\n        description: 'Enterprise Java application framework',\n        icon: '☕',\n        popularity: 80,\n        learningCurve: 'Hard',\n        documentation: 'https://spring.io/guides',\n        pros: ['Enterprise-ready', 'Strong typing', 'Mature ecosystem'],\n        cons: ['Verbose', 'Complex configuration', 'Slower development'],\n        useCases: ['Enterprise applications', 'Microservices', 'Large systems']\n      }, {\n        id: 'dotnet',\n        name: '.NET Core',\n        description: 'Cross-platform framework for building modern applications',\n        icon: '🔷',\n        popularity: 75,\n        learningCurve: 'Medium',\n        documentation: 'https://docs.microsoft.com/en-us/dotnet/',\n        pros: ['Cross-platform', 'High performance', 'Strong typing'],\n        cons: ['Microsoft ecosystem', 'Learning curve'],\n        useCases: ['Enterprise applications', 'APIs', 'Desktop applications']\n      }]\n    },\n    database: {\n      title: 'Database Technologies',\n      icon: 'Database',\n      description: 'Data storage and management solutions',\n      technologies: [{\n        id: 'postgresql',\n        name: 'PostgreSQL',\n        description: 'Advanced open source relational database',\n        icon: '🐘',\n        popularity: 90,\n        learningCurve: 'Medium',\n        documentation: 'https://www.postgresql.org/docs/',\n        pros: ['ACID compliant', 'Extensible', 'JSON support'],\n        cons: ['Complex for simple use cases', 'Memory usage'],\n        useCases: ['Complex queries', 'Data integrity', 'Analytics']\n      }, {\n        id: 'mongodb',\n        name: 'MongoDB',\n        description: 'Document-oriented NoSQL database',\n        icon: '🍃',\n        popularity: 85,\n        learningCurve: 'Easy',\n        documentation: 'https://docs.mongodb.com/',\n        pros: ['Flexible schema', 'Horizontal scaling', 'JSON-like documents'],\n        cons: ['No ACID transactions', 'Memory usage', 'Consistency issues'],\n        useCases: ['Rapid prototyping', 'Content management', 'Real-time analytics']\n      }, {\n        id: 'mysql',\n        name: 'MySQL',\n        description: 'Popular open source relational database',\n        icon: '🐬',\n        popularity: 80,\n        learningCurve: 'Easy',\n        documentation: 'https://dev.mysql.com/doc/',\n        pros: ['Easy to use', 'Fast', 'Widely supported'],\n        cons: ['Limited features', 'Licensing concerns'],\n        useCases: ['Web applications', 'E-commerce', 'Content management']\n      }, {\n        id: 'redis',\n        name: 'Redis',\n        description: 'In-memory data structure store',\n        icon: '🔴',\n        popularity: 85,\n        learningCurve: 'Easy',\n        documentation: 'https://redis.io/documentation',\n        pros: ['Very fast', 'Multiple data types', 'Pub/Sub'],\n        cons: ['Memory-based', 'Data persistence complexity'],\n        useCases: ['Caching', 'Session storage', 'Real-time analytics']\n      }]\n    },\n    cloud: {\n      title: 'Cloud & Infrastructure',\n      icon: 'Cloud',\n      description: 'Cloud platforms and infrastructure services',\n      technologies: [{\n        id: 'aws',\n        name: 'Amazon AWS',\n        description: 'Comprehensive cloud computing platform',\n        icon: '☁️',\n        popularity: 95,\n        learningCurve: 'Hard',\n        documentation: 'https://docs.aws.amazon.com/',\n        pros: ['Comprehensive services', 'Global infrastructure', 'Market leader'],\n        cons: ['Complex pricing', 'Steep learning curve', 'Vendor lock-in'],\n        useCases: ['Enterprise applications', 'Scalable systems', 'Global deployment']\n      }, {\n        id: 'gcp',\n        name: 'Google Cloud',\n        description: 'Google\\'s cloud computing services',\n        icon: '🌐',\n        popularity: 80,\n        learningCurve: 'Medium',\n        documentation: 'https://cloud.google.com/docs',\n        pros: ['AI/ML services', 'Competitive pricing', 'Kubernetes native'],\n        cons: ['Smaller market share', 'Less enterprise features'],\n        useCases: ['AI/ML applications', 'Data analytics', 'Modern applications']\n      }, {\n        id: 'azure',\n        name: 'Microsoft Azure',\n        description: 'Microsoft\\'s cloud computing platform',\n        icon: '🔷',\n        popularity: 85,\n        learningCurve: 'Medium',\n        documentation: 'https://docs.microsoft.com/en-us/azure/',\n        pros: ['Enterprise integration', 'Hybrid cloud', '.NET ecosystem'],\n        cons: ['Complex pricing', 'Windows-centric'],\n        useCases: ['Enterprise applications', 'Hybrid cloud', '.NET applications']\n      }, {\n        id: 'docker',\n        name: 'Docker',\n        description: 'Platform for developing, shipping, and running applications',\n        icon: '🐳',\n        popularity: 90,\n        learningCurve: 'Medium',\n        documentation: 'https://docs.docker.com/',\n        pros: ['Consistent environments', 'Easy deployment', 'Microservices'],\n        cons: ['Learning curve', 'Security considerations'],\n        useCases: ['Containerization', 'Microservices', 'CI/CD']\n      }]\n    },\n    tools: {\n      title: 'Development Tools',\n      icon: 'Wrench',\n      description: 'Development and productivity tools',\n      technologies: [{\n        id: 'git',\n        name: 'Git',\n        description: 'Distributed version control system',\n        icon: '📝',\n        popularity: 98,\n        learningCurve: 'Medium',\n        documentation: 'https://git-scm.com/doc',\n        pros: ['Distributed', 'Branching', 'Industry standard'],\n        cons: ['Learning curve', 'Complex for beginners'],\n        useCases: ['Version control', 'Collaboration', 'Code history']\n      }, {\n        id: 'webpack',\n        name: 'Webpack',\n        description: 'Static module bundler for modern JavaScript applications',\n        icon: '📦',\n        popularity: 85,\n        learningCurve: 'Hard',\n        documentation: 'https://webpack.js.org/concepts/',\n        pros: ['Powerful bundling', 'Plugin ecosystem', 'Code splitting'],\n        cons: ['Complex configuration', 'Learning curve'],\n        useCases: ['Module bundling', 'Asset optimization', 'Build processes']\n      }, {\n        id: 'jest',\n        name: 'Jest',\n        description: 'JavaScript testing framework',\n        icon: '🧪',\n        popularity: 90,\n        learningCurve: 'Easy',\n        documentation: 'https://jestjs.io/docs/getting-started',\n        pros: ['Zero configuration', 'Snapshot testing', 'Mocking'],\n        cons: ['JavaScript only', 'Large bundle size'],\n        useCases: ['Unit testing', 'Integration testing', 'Snapshot testing']\n      }, {\n        id: 'eslint',\n        name: 'ESLint',\n        description: 'Pluggable JavaScript linter',\n        icon: '🔍',\n        popularity: 95,\n        learningCurve: 'Easy',\n        documentation: 'https://eslint.org/docs/user-guide/',\n        pros: ['Customizable', 'Plugin ecosystem', 'Auto-fixing'],\n        cons: ['Configuration complexity', 'Performance impact'],\n        useCases: ['Code quality', 'Style enforcement', 'Error prevention']\n      }]\n    }\n  };\n  const toggleTechnology = (categoryId, techId) => {\n    setSelectedTechnologies(prev => {\n      const category = prev[categoryId] || [];\n      const isSelected = category.includes(techId);\n      return {\n        ...prev,\n        [categoryId]: isSelected ? category.filter(id => id !== techId) : [...category, techId]\n      };\n    });\n  };\n  const openTechModal = tech => {\n    setSelectedTech(tech);\n    setIsModalOpen(true);\n  };\n  const getTotalSelected = () => {\n    return Object.values(selectedTechnologies).reduce((total, category) => total + category.length, 0);\n  };\n  const handleNext = () => {\n    onNext === null || onNext === void 0 ? void 0 : onNext(selectedTechnologies);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn(\"max-w-6xl mx-auto p-6 space-y-8\", className),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-3xl font-bold text-foreground\",\n        children: \"Technology Stack\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-muted-foreground\",\n        children: \"Select the technologies and tools for your project\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-muted-foreground\",\n        children: [getTotalSelected(), \" technologies selected\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-8\",\n      children: Object.entries(techCategories).map(([categoryId, category]) => {\n        var _selectedTechnologies;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-card rounded-lg p-6 border border-border\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: category.icon,\n              className: \"h-6 w-6 text-primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-foreground\",\n                children: category.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-muted-foreground\",\n                children: category.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-auto text-sm text-muted-foreground\",\n              children: [((_selectedTechnologies = selectedTechnologies[categoryId]) === null || _selectedTechnologies === void 0 ? void 0 : _selectedTechnologies.length) || 0, \" selected\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: category.technologies.map(tech => {\n              var _selectedTechnologies2;\n              const isSelected = (_selectedTechnologies2 = selectedTechnologies[categoryId]) === null || _selectedTechnologies2 === void 0 ? void 0 : _selectedTechnologies2.includes(tech.id);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: cn(\"relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200\", \"hover:shadow-md hover:scale-105\", isSelected ? \"border-primary bg-primary/10 shadow-md\" : \"border-border bg-background hover:border-primary/50\"),\n                onClick: () => toggleTechnology(categoryId, tech.id),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl\",\n                    children: tech.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"ghost\",\n                      size: \"sm\",\n                      onClick: e => {\n                        e.stopPropagation();\n                        openTechModal(tech);\n                      },\n                      className: \"h-6 w-6 p-0\",\n                      children: /*#__PURE__*/_jsxDEV(Icon, {\n                        name: \"Info\",\n                        className: \"h-3 w-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 386,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 25\n                    }, this), isSelected && /*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Check\",\n                      className: \"h-4 w-4 text-primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-foreground mb-2\",\n                  children: tech.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-muted-foreground mb-3 line-clamp-2\",\n                  children: tech.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between text-xs\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"TrendingUp\",\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [tech.popularity, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: cn(\"px-2 py-1 rounded text-xs font-medium\", tech.learningCurve === 'Easy' && \"bg-green-100 text-green-800\", tech.learningCurve === 'Medium' && \"bg-yellow-100 text-yellow-800\", tech.learningCurve === 'Hard' && \"bg-red-100 text-red-800\"),\n                    children: tech.learningCurve\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this)]\n              }, tech.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)]\n        }, categoryId, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this), getTotalSelected() > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6 border border-border\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-foreground mb-4 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Package\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 13\n        }, this), \"Selected Technology Stack\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n        children: Object.entries(selectedTechnologies).map(([categoryId, techIds]) => {\n          if (techIds.length === 0) return null;\n          const category = techCategories[categoryId];\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-foreground\",\n              children: category.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1\",\n              children: techIds.map(techId => {\n                const tech = category.technologies.find(t => t.id === techId);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: tech.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: tech.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 27\n                  }, this)]\n                }, techId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 19\n            }, this)]\n          }, categoryId, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center pt-6\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        onClick: onBack,\n        iconName: \"ArrowLeft\",\n        iconPosition: \"left\",\n        children: \"Back to Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-muted-foreground\",\n          children: \"Step 3 of 6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          iconName: \"ArrowRight\",\n          iconPosition: \"right\",\n          children: \"Continue to Workflow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      isOpen: isModalOpen,\n      onClose: () => setIsModalOpen(false),\n      title: selectedTech === null || selectedTech === void 0 ? void 0 : selectedTech.name,\n      size: \"lg\",\n      children: selectedTech && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl\",\n            children: selectedTech.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted-foreground mb-4\",\n              children: selectedTech.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-foreground\",\n                  children: \"Popularity\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 bg-secondary rounded-full h-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-primary h-2 rounded-full transition-all duration-300\",\n                      style: {\n                        width: `${selectedTech.popularity}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 499,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-muted-foreground\",\n                    children: [selectedTech.popularity, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-foreground\",\n                  children: \"Learning Curve\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: cn(\"inline-block px-2 py-1 rounded text-xs font-medium\", selectedTech.learningCurve === 'Easy' && \"bg-green-100 text-green-800\", selectedTech.learningCurve === 'Medium' && \"bg-yellow-100 text-yellow-800\", selectedTech.learningCurve === 'Hard' && \"bg-red-100 text-red-800\"),\n                  children: selectedTech.learningCurve\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-semibold text-foreground mb-2 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"ThumbsUp\",\n                className: \"h-4 w-4 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this), \"Pros\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-1\",\n              children: selectedTech.pros.map((pro, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-sm text-muted-foreground flex items-start gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"Check\",\n                  className: \"h-3 w-3 text-green-600 mt-0.5 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 23\n                }, this), pro]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-semibold text-foreground mb-2 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"ThumbsDown\",\n                className: \"h-4 w-4 text-red-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this), \"Cons\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-1\",\n              children: selectedTech.cons.map((con, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-sm text-muted-foreground flex items-start gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"X\",\n                  className: \"h-3 w-3 text-red-600 mt-0.5 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 23\n                }, this), con]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-semibold text-foreground mb-2 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Target\",\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this), \"Common Use Cases\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: selectedTech.useCases.map((useCase, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-3 py-1 bg-secondary text-secondary-foreground rounded-full text-sm\",\n              children: useCase\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            onClick: () => window.open(selectedTech.documentation, '_blank'),\n            iconName: \"ExternalLink\",\n            iconPosition: \"right\",\n            children: \"View Documentation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => {\n              // Find the category this tech belongs to\n              const categoryId = Object.keys(techCategories).find(catId => techCategories[catId].technologies.some(t => t.id === selectedTech.id));\n              if (categoryId) {\n                toggleTechnology(categoryId, selectedTech.id);\n              }\n              setIsModalOpen(false);\n            },\n            iconName: Object.values(selectedTechnologies).some(category => category.includes(selectedTech.id)) ? \"Minus\" : \"Plus\",\n            iconPosition: \"left\",\n            children: Object.values(selectedTechnologies).some(category => category.includes(selectedTech.id)) ? \"Remove from Stack\" : \"Add to Stack\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 331,\n    columnNumber: 5\n  }, this);\n};\n_s(TechStackDisplay, \"Vt0O77aqlel5aSINgoU15PWqWD8=\");\n_c = TechStackDisplay;\nexport default TechStackDisplay;\nvar _c;\n$RefreshReg$(_c, \"TechStackDisplay\");", "map": {"version": 3, "names": ["React", "useState", "cn", "<PERSON><PERSON>", "Modal", "Icon", "jsxDEV", "_jsxDEV", "TechStackDisplay", "onNext", "onBack", "initialData", "className", "_s", "selectedTechnologies", "setSelectedTechnologies", "frontend", "backend", "database", "cloud", "tools", "selectedTech", "setSelectedTech", "isModalOpen", "setIsModalOpen", "techCategories", "title", "icon", "description", "technologies", "id", "name", "popularity", "learningCurve", "documentation", "pros", "cons", "useCases", "toggleTechnology", "categoryId", "techId", "prev", "category", "isSelected", "includes", "filter", "openTechModal", "tech", "getTotalSelected", "Object", "values", "reduce", "total", "length", "handleNext", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entries", "map", "_selectedTechnologies", "_selectedTechnologies2", "onClick", "variant", "size", "e", "stopPropagation", "techIds", "find", "t", "iconName", "iconPosition", "isOpen", "onClose", "style", "width", "pro", "index", "con", "useCase", "window", "open", "keys", "catId", "some", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/project/TechStackDisplay.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from '../ui/Button';\nimport Modal from '../ui/Modal';\nimport Icon from '../AppIcon';\n\nconst TechStackDisplay = ({ \n  onNext, \n  onBack, \n  initialData = {}, \n  className \n}) => {\n  const [selectedTechnologies, setSelectedTechnologies] = useState({\n    frontend: [],\n    backend: [],\n    database: [],\n    cloud: [],\n    tools: [],\n    ...initialData\n  });\n\n  const [selectedTech, setSelectedTech] = useState(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n\n  const techCategories = {\n    frontend: {\n      title: 'Frontend Technologies',\n      icon: 'Monitor',\n      description: 'User interface and client-side technologies',\n      technologies: [\n        {\n          id: 'react',\n          name: 'React',\n          description: 'A JavaScript library for building user interfaces',\n          icon: '⚛️',\n          popularity: 95,\n          learningCurve: 'Medium',\n          documentation: 'https://reactjs.org/docs',\n          pros: ['Component-based', 'Large ecosystem', 'Strong community'],\n          cons: ['Steep learning curve', 'Frequent updates'],\n          useCases: ['SPAs', 'Web applications', 'Mobile apps with React Native']\n        },\n        {\n          id: 'vue',\n          name: 'Vue.js',\n          description: 'Progressive JavaScript framework for building UIs',\n          icon: '🟢',\n          popularity: 85,\n          learningCurve: 'Easy',\n          documentation: 'https://vuejs.org/guide/',\n          pros: ['Easy to learn', 'Flexible', 'Good performance'],\n          cons: ['Smaller ecosystem', 'Less job market'],\n          useCases: ['Progressive enhancement', 'SPAs', 'Prototyping']\n        },\n        {\n          id: 'angular',\n          name: 'Angular',\n          description: 'Platform for building mobile and desktop web applications',\n          icon: '🅰️',\n          popularity: 75,\n          learningCurve: 'Hard',\n          documentation: 'https://angular.io/docs',\n          pros: ['Full framework', 'TypeScript support', 'Enterprise-ready'],\n          cons: ['Complex', 'Heavy', 'Steep learning curve'],\n          useCases: ['Enterprise applications', 'Large-scale projects']\n        },\n        {\n          id: 'svelte',\n          name: 'Svelte',\n          description: 'Cybernetically enhanced web apps',\n          icon: '🔥',\n          popularity: 70,\n          learningCurve: 'Easy',\n          documentation: 'https://svelte.dev/docs',\n          pros: ['No virtual DOM', 'Small bundle size', 'Easy to learn'],\n          cons: ['Smaller ecosystem', 'Less tooling'],\n          useCases: ['Performance-critical apps', 'Small to medium projects']\n        }\n      ]\n    },\n    backend: {\n      title: 'Backend Technologies',\n      icon: 'Server',\n      description: 'Server-side technologies and frameworks',\n      technologies: [\n        {\n          id: 'nodejs',\n          name: 'Node.js',\n          description: 'JavaScript runtime built on Chrome\\'s V8 JavaScript engine',\n          icon: '🟢',\n          popularity: 90,\n          learningCurve: 'Medium',\n          documentation: 'https://nodejs.org/en/docs/',\n          pros: ['JavaScript everywhere', 'Fast development', 'Large ecosystem'],\n          cons: ['Single-threaded', 'Not ideal for CPU-intensive tasks'],\n          useCases: ['APIs', 'Real-time applications', 'Microservices']\n        },\n        {\n          id: 'python',\n          name: 'Python/Django',\n          description: 'High-level Python web framework',\n          icon: '🐍',\n          popularity: 85,\n          learningCurve: 'Easy',\n          documentation: 'https://docs.djangoproject.com/',\n          pros: ['Rapid development', 'Batteries included', 'Great for AI/ML'],\n          cons: ['Performance limitations', 'GIL limitations'],\n          useCases: ['Web applications', 'APIs', 'Data science applications']\n        },\n        {\n          id: 'java',\n          name: 'Java/Spring',\n          description: 'Enterprise Java application framework',\n          icon: '☕',\n          popularity: 80,\n          learningCurve: 'Hard',\n          documentation: 'https://spring.io/guides',\n          pros: ['Enterprise-ready', 'Strong typing', 'Mature ecosystem'],\n          cons: ['Verbose', 'Complex configuration', 'Slower development'],\n          useCases: ['Enterprise applications', 'Microservices', 'Large systems']\n        },\n        {\n          id: 'dotnet',\n          name: '.NET Core',\n          description: 'Cross-platform framework for building modern applications',\n          icon: '🔷',\n          popularity: 75,\n          learningCurve: 'Medium',\n          documentation: 'https://docs.microsoft.com/en-us/dotnet/',\n          pros: ['Cross-platform', 'High performance', 'Strong typing'],\n          cons: ['Microsoft ecosystem', 'Learning curve'],\n          useCases: ['Enterprise applications', 'APIs', 'Desktop applications']\n        }\n      ]\n    },\n    database: {\n      title: 'Database Technologies',\n      icon: 'Database',\n      description: 'Data storage and management solutions',\n      technologies: [\n        {\n          id: 'postgresql',\n          name: 'PostgreSQL',\n          description: 'Advanced open source relational database',\n          icon: '🐘',\n          popularity: 90,\n          learningCurve: 'Medium',\n          documentation: 'https://www.postgresql.org/docs/',\n          pros: ['ACID compliant', 'Extensible', 'JSON support'],\n          cons: ['Complex for simple use cases', 'Memory usage'],\n          useCases: ['Complex queries', 'Data integrity', 'Analytics']\n        },\n        {\n          id: 'mongodb',\n          name: 'MongoDB',\n          description: 'Document-oriented NoSQL database',\n          icon: '🍃',\n          popularity: 85,\n          learningCurve: 'Easy',\n          documentation: 'https://docs.mongodb.com/',\n          pros: ['Flexible schema', 'Horizontal scaling', 'JSON-like documents'],\n          cons: ['No ACID transactions', 'Memory usage', 'Consistency issues'],\n          useCases: ['Rapid prototyping', 'Content management', 'Real-time analytics']\n        },\n        {\n          id: 'mysql',\n          name: 'MySQL',\n          description: 'Popular open source relational database',\n          icon: '🐬',\n          popularity: 80,\n          learningCurve: 'Easy',\n          documentation: 'https://dev.mysql.com/doc/',\n          pros: ['Easy to use', 'Fast', 'Widely supported'],\n          cons: ['Limited features', 'Licensing concerns'],\n          useCases: ['Web applications', 'E-commerce', 'Content management']\n        },\n        {\n          id: 'redis',\n          name: 'Redis',\n          description: 'In-memory data structure store',\n          icon: '🔴',\n          popularity: 85,\n          learningCurve: 'Easy',\n          documentation: 'https://redis.io/documentation',\n          pros: ['Very fast', 'Multiple data types', 'Pub/Sub'],\n          cons: ['Memory-based', 'Data persistence complexity'],\n          useCases: ['Caching', 'Session storage', 'Real-time analytics']\n        }\n      ]\n    },\n    cloud: {\n      title: 'Cloud & Infrastructure',\n      icon: 'Cloud',\n      description: 'Cloud platforms and infrastructure services',\n      technologies: [\n        {\n          id: 'aws',\n          name: 'Amazon AWS',\n          description: 'Comprehensive cloud computing platform',\n          icon: '☁️',\n          popularity: 95,\n          learningCurve: 'Hard',\n          documentation: 'https://docs.aws.amazon.com/',\n          pros: ['Comprehensive services', 'Global infrastructure', 'Market leader'],\n          cons: ['Complex pricing', 'Steep learning curve', 'Vendor lock-in'],\n          useCases: ['Enterprise applications', 'Scalable systems', 'Global deployment']\n        },\n        {\n          id: 'gcp',\n          name: 'Google Cloud',\n          description: 'Google\\'s cloud computing services',\n          icon: '🌐',\n          popularity: 80,\n          learningCurve: 'Medium',\n          documentation: 'https://cloud.google.com/docs',\n          pros: ['AI/ML services', 'Competitive pricing', 'Kubernetes native'],\n          cons: ['Smaller market share', 'Less enterprise features'],\n          useCases: ['AI/ML applications', 'Data analytics', 'Modern applications']\n        },\n        {\n          id: 'azure',\n          name: 'Microsoft Azure',\n          description: 'Microsoft\\'s cloud computing platform',\n          icon: '🔷',\n          popularity: 85,\n          learningCurve: 'Medium',\n          documentation: 'https://docs.microsoft.com/en-us/azure/',\n          pros: ['Enterprise integration', 'Hybrid cloud', '.NET ecosystem'],\n          cons: ['Complex pricing', 'Windows-centric'],\n          useCases: ['Enterprise applications', 'Hybrid cloud', '.NET applications']\n        },\n        {\n          id: 'docker',\n          name: 'Docker',\n          description: 'Platform for developing, shipping, and running applications',\n          icon: '🐳',\n          popularity: 90,\n          learningCurve: 'Medium',\n          documentation: 'https://docs.docker.com/',\n          pros: ['Consistent environments', 'Easy deployment', 'Microservices'],\n          cons: ['Learning curve', 'Security considerations'],\n          useCases: ['Containerization', 'Microservices', 'CI/CD']\n        }\n      ]\n    },\n    tools: {\n      title: 'Development Tools',\n      icon: 'Wrench',\n      description: 'Development and productivity tools',\n      technologies: [\n        {\n          id: 'git',\n          name: 'Git',\n          description: 'Distributed version control system',\n          icon: '📝',\n          popularity: 98,\n          learningCurve: 'Medium',\n          documentation: 'https://git-scm.com/doc',\n          pros: ['Distributed', 'Branching', 'Industry standard'],\n          cons: ['Learning curve', 'Complex for beginners'],\n          useCases: ['Version control', 'Collaboration', 'Code history']\n        },\n        {\n          id: 'webpack',\n          name: 'Webpack',\n          description: 'Static module bundler for modern JavaScript applications',\n          icon: '📦',\n          popularity: 85,\n          learningCurve: 'Hard',\n          documentation: 'https://webpack.js.org/concepts/',\n          pros: ['Powerful bundling', 'Plugin ecosystem', 'Code splitting'],\n          cons: ['Complex configuration', 'Learning curve'],\n          useCases: ['Module bundling', 'Asset optimization', 'Build processes']\n        },\n        {\n          id: 'jest',\n          name: 'Jest',\n          description: 'JavaScript testing framework',\n          icon: '🧪',\n          popularity: 90,\n          learningCurve: 'Easy',\n          documentation: 'https://jestjs.io/docs/getting-started',\n          pros: ['Zero configuration', 'Snapshot testing', 'Mocking'],\n          cons: ['JavaScript only', 'Large bundle size'],\n          useCases: ['Unit testing', 'Integration testing', 'Snapshot testing']\n        },\n        {\n          id: 'eslint',\n          name: 'ESLint',\n          description: 'Pluggable JavaScript linter',\n          icon: '🔍',\n          popularity: 95,\n          learningCurve: 'Easy',\n          documentation: 'https://eslint.org/docs/user-guide/',\n          pros: ['Customizable', 'Plugin ecosystem', 'Auto-fixing'],\n          cons: ['Configuration complexity', 'Performance impact'],\n          useCases: ['Code quality', 'Style enforcement', 'Error prevention']\n        }\n      ]\n    }\n  };\n\n  const toggleTechnology = (categoryId, techId) => {\n    setSelectedTechnologies(prev => {\n      const category = prev[categoryId] || [];\n      const isSelected = category.includes(techId);\n      \n      return {\n        ...prev,\n        [categoryId]: isSelected \n          ? category.filter(id => id !== techId)\n          : [...category, techId]\n      };\n    });\n  };\n\n  const openTechModal = (tech) => {\n    setSelectedTech(tech);\n    setIsModalOpen(true);\n  };\n\n  const getTotalSelected = () => {\n    return Object.values(selectedTechnologies).reduce((total, category) => total + category.length, 0);\n  };\n\n  const handleNext = () => {\n    onNext?.(selectedTechnologies);\n  };\n\n  return (\n    <div className={cn(\"max-w-6xl mx-auto p-6 space-y-8\", className)}>\n      {/* Header */}\n      <div className=\"text-center space-y-2\">\n        <h2 className=\"text-3xl font-bold text-foreground\">Technology Stack</h2>\n        <p className=\"text-muted-foreground\">\n          Select the technologies and tools for your project\n        </p>\n        <div className=\"text-sm text-muted-foreground\">\n          {getTotalSelected()} technologies selected\n        </div>\n      </div>\n\n      {/* Technology Categories */}\n      <div className=\"space-y-8\">\n        {Object.entries(techCategories).map(([categoryId, category]) => (\n          <div key={categoryId} className=\"bg-card rounded-lg p-6 border border-border\">\n            <div className=\"flex items-center gap-3 mb-6\">\n              <Icon name={category.icon} className=\"h-6 w-6 text-primary\" />\n              <div>\n                <h3 className=\"text-xl font-semibold text-foreground\">{category.title}</h3>\n                <p className=\"text-sm text-muted-foreground\">{category.description}</p>\n              </div>\n              <div className=\"ml-auto text-sm text-muted-foreground\">\n                {selectedTechnologies[categoryId]?.length || 0} selected\n              </div>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              {category.technologies.map((tech) => {\n                const isSelected = selectedTechnologies[categoryId]?.includes(tech.id);\n                \n                return (\n                  <div\n                    key={tech.id}\n                    className={cn(\n                      \"relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200\",\n                      \"hover:shadow-md hover:scale-105\",\n                      isSelected \n                        ? \"border-primary bg-primary/10 shadow-md\" \n                        : \"border-border bg-background hover:border-primary/50\"\n                    )}\n                    onClick={() => toggleTechnology(categoryId, tech.id)}\n                  >\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <div className=\"text-2xl\">{tech.icon}</div>\n                      <div className=\"flex gap-1\">\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            openTechModal(tech);\n                          }}\n                          className=\"h-6 w-6 p-0\"\n                        >\n                          <Icon name=\"Info\" className=\"h-3 w-3\" />\n                        </Button>\n                        {isSelected && (\n                          <Icon name=\"Check\" className=\"h-4 w-4 text-primary\" />\n                        )}\n                      </div>\n                    </div>\n                    \n                    <h4 className=\"font-semibold text-foreground mb-2\">{tech.name}</h4>\n                    <p className=\"text-xs text-muted-foreground mb-3 line-clamp-2\">\n                      {tech.description}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between text-xs\">\n                      <div className=\"flex items-center gap-1\">\n                        <Icon name=\"TrendingUp\" className=\"h-3 w-3\" />\n                        <span>{tech.popularity}%</span>\n                      </div>\n                      <div className={cn(\n                        \"px-2 py-1 rounded text-xs font-medium\",\n                        tech.learningCurve === 'Easy' && \"bg-green-100 text-green-800\",\n                        tech.learningCurve === 'Medium' && \"bg-yellow-100 text-yellow-800\",\n                        tech.learningCurve === 'Hard' && \"bg-red-100 text-red-800\"\n                      )}>\n                        {tech.learningCurve}\n                      </div>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Selected Technologies Summary */}\n      {getTotalSelected() > 0 && (\n        <div className=\"bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6 border border-border\">\n          <h3 className=\"text-lg font-semibold text-foreground mb-4 flex items-center gap-2\">\n            <Icon name=\"Package\" className=\"h-5 w-5\" />\n            Selected Technology Stack\n          </h3>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {Object.entries(selectedTechnologies).map(([categoryId, techIds]) => {\n              if (techIds.length === 0) return null;\n\n              const category = techCategories[categoryId];\n              return (\n                <div key={categoryId} className=\"space-y-2\">\n                  <h4 className=\"font-medium text-foreground\">{category.title}</h4>\n                  <div className=\"space-y-1\">\n                    {techIds.map(techId => {\n                      const tech = category.technologies.find(t => t.id === techId);\n                      return (\n                        <div key={techId} className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n                          <span>{tech.icon}</span>\n                          <span>{tech.name}</span>\n                        </div>\n                      );\n                    })}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      )}\n\n      {/* Navigation */}\n      <div className=\"flex justify-between items-center pt-6\">\n        <Button\n          variant=\"outline\"\n          onClick={onBack}\n          iconName=\"ArrowLeft\"\n          iconPosition=\"left\"\n        >\n          Back to Overview\n        </Button>\n\n        <div className=\"flex items-center gap-2\">\n          <div className=\"text-sm text-muted-foreground\">\n            Step 3 of 6\n          </div>\n          <Button\n            onClick={handleNext}\n            iconName=\"ArrowRight\"\n            iconPosition=\"right\"\n          >\n            Continue to Workflow\n          </Button>\n        </div>\n      </div>\n\n      {/* Technology Details Modal */}\n      <Modal\n        isOpen={isModalOpen}\n        onClose={() => setIsModalOpen(false)}\n        title={selectedTech?.name}\n        size=\"lg\"\n      >\n        {selectedTech && (\n          <div className=\"space-y-6\">\n            <div className=\"flex items-start gap-4\">\n              <div className=\"text-4xl\">{selectedTech.icon}</div>\n              <div className=\"flex-1\">\n                <p className=\"text-muted-foreground mb-4\">{selectedTech.description}</p>\n\n                <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                  <div>\n                    <div className=\"text-sm font-medium text-foreground\">Popularity</div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"flex-1 bg-secondary rounded-full h-2\">\n                        <div\n                          className=\"bg-primary h-2 rounded-full transition-all duration-300\"\n                          style={{ width: `${selectedTech.popularity}%` }}\n                        />\n                      </div>\n                      <span className=\"text-sm text-muted-foreground\">{selectedTech.popularity}%</span>\n                    </div>\n                  </div>\n\n                  <div>\n                    <div className=\"text-sm font-medium text-foreground\">Learning Curve</div>\n                    <div className={cn(\n                      \"inline-block px-2 py-1 rounded text-xs font-medium\",\n                      selectedTech.learningCurve === 'Easy' && \"bg-green-100 text-green-800\",\n                      selectedTech.learningCurve === 'Medium' && \"bg-yellow-100 text-yellow-800\",\n                      selectedTech.learningCurve === 'Hard' && \"bg-red-100 text-red-800\"\n                    )}>\n                      {selectedTech.learningCurve}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <h4 className=\"font-semibold text-foreground mb-2 flex items-center gap-2\">\n                  <Icon name=\"ThumbsUp\" className=\"h-4 w-4 text-green-600\" />\n                  Pros\n                </h4>\n                <ul className=\"space-y-1\">\n                  {selectedTech.pros.map((pro, index) => (\n                    <li key={index} className=\"text-sm text-muted-foreground flex items-start gap-2\">\n                      <Icon name=\"Check\" className=\"h-3 w-3 text-green-600 mt-0.5 flex-shrink-0\" />\n                      {pro}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n\n              <div>\n                <h4 className=\"font-semibold text-foreground mb-2 flex items-center gap-2\">\n                  <Icon name=\"ThumbsDown\" className=\"h-4 w-4 text-red-600\" />\n                  Cons\n                </h4>\n                <ul className=\"space-y-1\">\n                  {selectedTech.cons.map((con, index) => (\n                    <li key={index} className=\"text-sm text-muted-foreground flex items-start gap-2\">\n                      <Icon name=\"X\" className=\"h-3 w-3 text-red-600 mt-0.5 flex-shrink-0\" />\n                      {con}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n\n            <div>\n              <h4 className=\"font-semibold text-foreground mb-2 flex items-center gap-2\">\n                <Icon name=\"Target\" className=\"h-4 w-4\" />\n                Common Use Cases\n              </h4>\n              <div className=\"flex flex-wrap gap-2\">\n                {selectedTech.useCases.map((useCase, index) => (\n                  <span\n                    key={index}\n                    className=\"px-3 py-1 bg-secondary text-secondary-foreground rounded-full text-sm\"\n                  >\n                    {useCase}\n                  </span>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"flex gap-3\">\n              <Button\n                variant=\"outline\"\n                onClick={() => window.open(selectedTech.documentation, '_blank')}\n                iconName=\"ExternalLink\"\n                iconPosition=\"right\"\n              >\n                View Documentation\n              </Button>\n              <Button\n                onClick={() => {\n                  // Find the category this tech belongs to\n                  const categoryId = Object.keys(techCategories).find(catId =>\n                    techCategories[catId].technologies.some(t => t.id === selectedTech.id)\n                  );\n                  if (categoryId) {\n                    toggleTechnology(categoryId, selectedTech.id);\n                  }\n                  setIsModalOpen(false);\n                }}\n                iconName={\n                  Object.values(selectedTechnologies).some(category =>\n                    category.includes(selectedTech.id)\n                  ) ? \"Minus\" : \"Plus\"\n                }\n                iconPosition=\"left\"\n              >\n                {Object.values(selectedTechnologies).some(category =>\n                  category.includes(selectedTech.id)\n                ) ? \"Remove from Stack\" : \"Add to Stack\"}\n              </Button>\n            </div>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default TechStackDisplay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,EAAE,QAAQ,gBAAgB;AACnC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,gBAAgB,GAAGA,CAAC;EACxBC,MAAM;EACNC,MAAM;EACNC,WAAW,GAAG,CAAC,CAAC;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGd,QAAQ,CAAC;IAC/De,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACT,GAAGT;EACL,CAAC,CAAC;EAEF,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMwB,cAAc,GAAG;IACrBT,QAAQ,EAAE;MACRU,KAAK,EAAE,uBAAuB;MAC9BC,IAAI,EAAE,SAAS;MACfC,WAAW,EAAE,6CAA6C;MAC1DC,YAAY,EAAE,CACZ;QACEC,EAAE,EAAE,OAAO;QACXC,IAAI,EAAE,OAAO;QACbH,WAAW,EAAE,mDAAmD;QAChED,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,QAAQ;QACvBC,aAAa,EAAE,0BAA0B;QACzCC,IAAI,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;QAChEC,IAAI,EAAE,CAAC,sBAAsB,EAAE,kBAAkB,CAAC;QAClDC,QAAQ,EAAE,CAAC,MAAM,EAAE,kBAAkB,EAAE,+BAA+B;MACxE,CAAC,EACD;QACEP,EAAE,EAAE,KAAK;QACTC,IAAI,EAAE,QAAQ;QACdH,WAAW,EAAE,mDAAmD;QAChED,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,0BAA0B;QACzCC,IAAI,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,kBAAkB,CAAC;QACvDC,IAAI,EAAE,CAAC,mBAAmB,EAAE,iBAAiB,CAAC;QAC9CC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,MAAM,EAAE,aAAa;MAC7D,CAAC,EACD;QACEP,EAAE,EAAE,SAAS;QACbC,IAAI,EAAE,SAAS;QACfH,WAAW,EAAE,2DAA2D;QACxED,IAAI,EAAE,KAAK;QACXK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,yBAAyB;QACxCC,IAAI,EAAE,CAAC,gBAAgB,EAAE,oBAAoB,EAAE,kBAAkB,CAAC;QAClEC,IAAI,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,sBAAsB,CAAC;QAClDC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,sBAAsB;MAC9D,CAAC,EACD;QACEP,EAAE,EAAE,QAAQ;QACZC,IAAI,EAAE,QAAQ;QACdH,WAAW,EAAE,kCAAkC;QAC/CD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,yBAAyB;QACxCC,IAAI,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,eAAe,CAAC;QAC9DC,IAAI,EAAE,CAAC,mBAAmB,EAAE,cAAc,CAAC;QAC3CC,QAAQ,EAAE,CAAC,2BAA2B,EAAE,0BAA0B;MACpE,CAAC;IAEL,CAAC;IACDpB,OAAO,EAAE;MACPS,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,EAAE,QAAQ;MACdC,WAAW,EAAE,yCAAyC;MACtDC,YAAY,EAAE,CACZ;QACEC,EAAE,EAAE,QAAQ;QACZC,IAAI,EAAE,SAAS;QACfH,WAAW,EAAE,4DAA4D;QACzED,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,QAAQ;QACvBC,aAAa,EAAE,6BAA6B;QAC5CC,IAAI,EAAE,CAAC,uBAAuB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;QACtEC,IAAI,EAAE,CAAC,iBAAiB,EAAE,mCAAmC,CAAC;QAC9DC,QAAQ,EAAE,CAAC,MAAM,EAAE,wBAAwB,EAAE,eAAe;MAC9D,CAAC,EACD;QACEP,EAAE,EAAE,QAAQ;QACZC,IAAI,EAAE,eAAe;QACrBH,WAAW,EAAE,iCAAiC;QAC9CD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,iCAAiC;QAChDC,IAAI,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;QACpEC,IAAI,EAAE,CAAC,yBAAyB,EAAE,iBAAiB,CAAC;QACpDC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,MAAM,EAAE,2BAA2B;MACpE,CAAC,EACD;QACEP,EAAE,EAAE,MAAM;QACVC,IAAI,EAAE,aAAa;QACnBH,WAAW,EAAE,uCAAuC;QACpDD,IAAI,EAAE,GAAG;QACTK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,0BAA0B;QACzCC,IAAI,EAAE,CAAC,kBAAkB,EAAE,eAAe,EAAE,kBAAkB,CAAC;QAC/DC,IAAI,EAAE,CAAC,SAAS,EAAE,uBAAuB,EAAE,oBAAoB,CAAC;QAChEC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,eAAe,EAAE,eAAe;MACxE,CAAC,EACD;QACEP,EAAE,EAAE,QAAQ;QACZC,IAAI,EAAE,WAAW;QACjBH,WAAW,EAAE,2DAA2D;QACxED,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,QAAQ;QACvBC,aAAa,EAAE,0CAA0C;QACzDC,IAAI,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,eAAe,CAAC;QAC7DC,IAAI,EAAE,CAAC,qBAAqB,EAAE,gBAAgB,CAAC;QAC/CC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,MAAM,EAAE,sBAAsB;MACtE,CAAC;IAEL,CAAC;IACDnB,QAAQ,EAAE;MACRQ,KAAK,EAAE,uBAAuB;MAC9BC,IAAI,EAAE,UAAU;MAChBC,WAAW,EAAE,uCAAuC;MACpDC,YAAY,EAAE,CACZ;QACEC,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,YAAY;QAClBH,WAAW,EAAE,0CAA0C;QACvDD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,QAAQ;QACvBC,aAAa,EAAE,kCAAkC;QACjDC,IAAI,EAAE,CAAC,gBAAgB,EAAE,YAAY,EAAE,cAAc,CAAC;QACtDC,IAAI,EAAE,CAAC,8BAA8B,EAAE,cAAc,CAAC;QACtDC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,WAAW;MAC7D,CAAC,EACD;QACEP,EAAE,EAAE,SAAS;QACbC,IAAI,EAAE,SAAS;QACfH,WAAW,EAAE,kCAAkC;QAC/CD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,2BAA2B;QAC1CC,IAAI,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,qBAAqB,CAAC;QACtEC,IAAI,EAAE,CAAC,sBAAsB,EAAE,cAAc,EAAE,oBAAoB,CAAC;QACpEC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,qBAAqB;MAC7E,CAAC,EACD;QACEP,EAAE,EAAE,OAAO;QACXC,IAAI,EAAE,OAAO;QACbH,WAAW,EAAE,yCAAyC;QACtDD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,4BAA4B;QAC3CC,IAAI,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,kBAAkB,CAAC;QACjDC,IAAI,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,CAAC;QAChDC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,YAAY,EAAE,oBAAoB;MACnE,CAAC,EACD;QACEP,EAAE,EAAE,OAAO;QACXC,IAAI,EAAE,OAAO;QACbH,WAAW,EAAE,gCAAgC;QAC7CD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,gCAAgC;QAC/CC,IAAI,EAAE,CAAC,WAAW,EAAE,qBAAqB,EAAE,SAAS,CAAC;QACrDC,IAAI,EAAE,CAAC,cAAc,EAAE,6BAA6B,CAAC;QACrDC,QAAQ,EAAE,CAAC,SAAS,EAAE,iBAAiB,EAAE,qBAAqB;MAChE,CAAC;IAEL,CAAC;IACDlB,KAAK,EAAE;MACLO,KAAK,EAAE,wBAAwB;MAC/BC,IAAI,EAAE,OAAO;MACbC,WAAW,EAAE,6CAA6C;MAC1DC,YAAY,EAAE,CACZ;QACEC,EAAE,EAAE,KAAK;QACTC,IAAI,EAAE,YAAY;QAClBH,WAAW,EAAE,wCAAwC;QACrDD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,8BAA8B;QAC7CC,IAAI,EAAE,CAAC,wBAAwB,EAAE,uBAAuB,EAAE,eAAe,CAAC;QAC1EC,IAAI,EAAE,CAAC,iBAAiB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC;QACnEC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,kBAAkB,EAAE,mBAAmB;MAC/E,CAAC,EACD;QACEP,EAAE,EAAE,KAAK;QACTC,IAAI,EAAE,cAAc;QACpBH,WAAW,EAAE,oCAAoC;QACjDD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,QAAQ;QACvBC,aAAa,EAAE,+BAA+B;QAC9CC,IAAI,EAAE,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,mBAAmB,CAAC;QACpEC,IAAI,EAAE,CAAC,sBAAsB,EAAE,0BAA0B,CAAC;QAC1DC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,qBAAqB;MAC1E,CAAC,EACD;QACEP,EAAE,EAAE,OAAO;QACXC,IAAI,EAAE,iBAAiB;QACvBH,WAAW,EAAE,uCAAuC;QACpDD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,QAAQ;QACvBC,aAAa,EAAE,yCAAyC;QACxDC,IAAI,EAAE,CAAC,wBAAwB,EAAE,cAAc,EAAE,gBAAgB,CAAC;QAClEC,IAAI,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;QAC5CC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,cAAc,EAAE,mBAAmB;MAC3E,CAAC,EACD;QACEP,EAAE,EAAE,QAAQ;QACZC,IAAI,EAAE,QAAQ;QACdH,WAAW,EAAE,6DAA6D;QAC1ED,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,QAAQ;QACvBC,aAAa,EAAE,0BAA0B;QACzCC,IAAI,EAAE,CAAC,yBAAyB,EAAE,iBAAiB,EAAE,eAAe,CAAC;QACrEC,IAAI,EAAE,CAAC,gBAAgB,EAAE,yBAAyB,CAAC;QACnDC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,eAAe,EAAE,OAAO;MACzD,CAAC;IAEL,CAAC;IACDjB,KAAK,EAAE;MACLM,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE,QAAQ;MACdC,WAAW,EAAE,oCAAoC;MACjDC,YAAY,EAAE,CACZ;QACEC,EAAE,EAAE,KAAK;QACTC,IAAI,EAAE,KAAK;QACXH,WAAW,EAAE,oCAAoC;QACjDD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,QAAQ;QACvBC,aAAa,EAAE,yBAAyB;QACxCC,IAAI,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,mBAAmB,CAAC;QACvDC,IAAI,EAAE,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACjDC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,eAAe,EAAE,cAAc;MAC/D,CAAC,EACD;QACEP,EAAE,EAAE,SAAS;QACbC,IAAI,EAAE,SAAS;QACfH,WAAW,EAAE,0DAA0D;QACvED,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,kCAAkC;QACjDC,IAAI,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;QACjEC,IAAI,EAAE,CAAC,uBAAuB,EAAE,gBAAgB,CAAC;QACjDC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,iBAAiB;MACvE,CAAC,EACD;QACEP,EAAE,EAAE,MAAM;QACVC,IAAI,EAAE,MAAM;QACZH,WAAW,EAAE,8BAA8B;QAC3CD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,wCAAwC;QACvDC,IAAI,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,EAAE,SAAS,CAAC;QAC3DC,IAAI,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,CAAC;QAC9CC,QAAQ,EAAE,CAAC,cAAc,EAAE,qBAAqB,EAAE,kBAAkB;MACtE,CAAC,EACD;QACEP,EAAE,EAAE,QAAQ;QACZC,IAAI,EAAE,QAAQ;QACdH,WAAW,EAAE,6BAA6B;QAC1CD,IAAI,EAAE,IAAI;QACVK,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,MAAM;QACrBC,aAAa,EAAE,qCAAqC;QACpDC,IAAI,EAAE,CAAC,cAAc,EAAE,kBAAkB,EAAE,aAAa,CAAC;QACzDC,IAAI,EAAE,CAAC,0BAA0B,EAAE,oBAAoB,CAAC;QACxDC,QAAQ,EAAE,CAAC,cAAc,EAAE,mBAAmB,EAAE,kBAAkB;MACpE,CAAC;IAEL;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACC,UAAU,EAAEC,MAAM,KAAK;IAC/CzB,uBAAuB,CAAC0B,IAAI,IAAI;MAC9B,MAAMC,QAAQ,GAAGD,IAAI,CAACF,UAAU,CAAC,IAAI,EAAE;MACvC,MAAMI,UAAU,GAAGD,QAAQ,CAACE,QAAQ,CAACJ,MAAM,CAAC;MAE5C,OAAO;QACL,GAAGC,IAAI;QACP,CAACF,UAAU,GAAGI,UAAU,GACpBD,QAAQ,CAACG,MAAM,CAACf,EAAE,IAAIA,EAAE,KAAKU,MAAM,CAAC,GACpC,CAAC,GAAGE,QAAQ,EAAEF,MAAM;MAC1B,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,MAAMM,aAAa,GAAIC,IAAI,IAAK;IAC9BzB,eAAe,CAACyB,IAAI,CAAC;IACrBvB,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMwB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAOC,MAAM,CAACC,MAAM,CAACpC,oBAAoB,CAAC,CAACqC,MAAM,CAAC,CAACC,KAAK,EAAEV,QAAQ,KAAKU,KAAK,GAAGV,QAAQ,CAACW,MAAM,EAAE,CAAC,CAAC;EACpG,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB7C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGK,oBAAoB,CAAC;EAChC,CAAC;EAED,oBACEP,OAAA;IAAKK,SAAS,EAAEV,EAAE,CAAC,iCAAiC,EAAEU,SAAS,CAAE;IAAA2C,QAAA,gBAE/DhD,OAAA;MAAKK,SAAS,EAAC,uBAAuB;MAAA2C,QAAA,gBACpChD,OAAA;QAAIK,SAAS,EAAC,oCAAoC;QAAA2C,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxEpD,OAAA;QAAGK,SAAS,EAAC,uBAAuB;QAAA2C,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJpD,OAAA;QAAKK,SAAS,EAAC,+BAA+B;QAAA2C,QAAA,GAC3CP,gBAAgB,CAAC,CAAC,EAAC,wBACtB;MAAA;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAA2C,QAAA,EACvBN,MAAM,CAACW,OAAO,CAACnC,cAAc,CAAC,CAACoC,GAAG,CAAC,CAAC,CAACtB,UAAU,EAAEG,QAAQ,CAAC;QAAA,IAAAoB,qBAAA;QAAA,oBACzDvD,OAAA;UAAsBK,SAAS,EAAC,6CAA6C;UAAA2C,QAAA,gBAC3EhD,OAAA;YAAKK,SAAS,EAAC,8BAA8B;YAAA2C,QAAA,gBAC3ChD,OAAA,CAACF,IAAI;cAAC0B,IAAI,EAAEW,QAAQ,CAACf,IAAK;cAACf,SAAS,EAAC;YAAsB;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DpD,OAAA;cAAAgD,QAAA,gBACEhD,OAAA;gBAAIK,SAAS,EAAC,uCAAuC;gBAAA2C,QAAA,EAAEb,QAAQ,CAAChB;cAAK;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3EpD,OAAA;gBAAGK,SAAS,EAAC,+BAA+B;gBAAA2C,QAAA,EAAEb,QAAQ,CAACd;cAAW;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACNpD,OAAA;cAAKK,SAAS,EAAC,uCAAuC;cAAA2C,QAAA,GACnD,EAAAO,qBAAA,GAAAhD,oBAAoB,CAACyB,UAAU,CAAC,cAAAuB,qBAAA,uBAAhCA,qBAAA,CAAkCT,MAAM,KAAI,CAAC,EAAC,WACjD;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpD,OAAA;YAAKK,SAAS,EAAC,sDAAsD;YAAA2C,QAAA,EAClEb,QAAQ,CAACb,YAAY,CAACgC,GAAG,CAAEd,IAAI,IAAK;cAAA,IAAAgB,sBAAA;cACnC,MAAMpB,UAAU,IAAAoB,sBAAA,GAAGjD,oBAAoB,CAACyB,UAAU,CAAC,cAAAwB,sBAAA,uBAAhCA,sBAAA,CAAkCnB,QAAQ,CAACG,IAAI,CAACjB,EAAE,CAAC;cAEtE,oBACEvB,OAAA;gBAEEK,SAAS,EAAEV,EAAE,CACX,6EAA6E,EAC7E,iCAAiC,EACjCyC,UAAU,GACN,wCAAwC,GACxC,qDACN,CAAE;gBACFqB,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAACC,UAAU,EAAEQ,IAAI,CAACjB,EAAE,CAAE;gBAAAyB,QAAA,gBAErDhD,OAAA;kBAAKK,SAAS,EAAC,uCAAuC;kBAAA2C,QAAA,gBACpDhD,OAAA;oBAAKK,SAAS,EAAC,UAAU;oBAAA2C,QAAA,EAAER,IAAI,CAACpB;kBAAI;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3CpD,OAAA;oBAAKK,SAAS,EAAC,YAAY;oBAAA2C,QAAA,gBACzBhD,OAAA,CAACJ,MAAM;sBACL8D,OAAO,EAAC,OAAO;sBACfC,IAAI,EAAC,IAAI;sBACTF,OAAO,EAAGG,CAAC,IAAK;wBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;wBACnBtB,aAAa,CAACC,IAAI,CAAC;sBACrB,CAAE;sBACFnC,SAAS,EAAC,aAAa;sBAAA2C,QAAA,eAEvBhD,OAAA,CAACF,IAAI;wBAAC0B,IAAI,EAAC,MAAM;wBAACnB,SAAS,EAAC;sBAAS;wBAAA4C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,EACRhB,UAAU,iBACTpC,OAAA,CAACF,IAAI;sBAAC0B,IAAI,EAAC,OAAO;sBAACnB,SAAS,EAAC;oBAAsB;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CACtD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENpD,OAAA;kBAAIK,SAAS,EAAC,oCAAoC;kBAAA2C,QAAA,EAAER,IAAI,CAAChB;gBAAI;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnEpD,OAAA;kBAAGK,SAAS,EAAC,iDAAiD;kBAAA2C,QAAA,EAC3DR,IAAI,CAACnB;gBAAW;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eAEJpD,OAAA;kBAAKK,SAAS,EAAC,2CAA2C;kBAAA2C,QAAA,gBACxDhD,OAAA;oBAAKK,SAAS,EAAC,yBAAyB;oBAAA2C,QAAA,gBACtChD,OAAA,CAACF,IAAI;sBAAC0B,IAAI,EAAC,YAAY;sBAACnB,SAAS,EAAC;oBAAS;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9CpD,OAAA;sBAAAgD,QAAA,GAAOR,IAAI,CAACf,UAAU,EAAC,GAAC;oBAAA;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNpD,OAAA;oBAAKK,SAAS,EAAEV,EAAE,CAChB,uCAAuC,EACvC6C,IAAI,CAACd,aAAa,KAAK,MAAM,IAAI,6BAA6B,EAC9Dc,IAAI,CAACd,aAAa,KAAK,QAAQ,IAAI,+BAA+B,EAClEc,IAAI,CAACd,aAAa,KAAK,MAAM,IAAI,yBACnC,CAAE;oBAAAsB,QAAA,EACCR,IAAI,CAACd;kBAAa;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,GAhDDZ,IAAI,CAACjB,EAAE;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiDT,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GAtEEpB,UAAU;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuEf,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLX,gBAAgB,CAAC,CAAC,GAAG,CAAC,iBACrBzC,OAAA;MAAKK,SAAS,EAAC,sFAAsF;MAAA2C,QAAA,gBACnGhD,OAAA;QAAIK,SAAS,EAAC,oEAAoE;QAAA2C,QAAA,gBAChFhD,OAAA,CAACF,IAAI;UAAC0B,IAAI,EAAC,SAAS;UAACnB,SAAS,EAAC;QAAS;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,6BAE7C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELpD,OAAA;QAAKK,SAAS,EAAC,sDAAsD;QAAA2C,QAAA,EAClEN,MAAM,CAACW,OAAO,CAAC9C,oBAAoB,CAAC,CAAC+C,GAAG,CAAC,CAAC,CAACtB,UAAU,EAAE8B,OAAO,CAAC,KAAK;UACnE,IAAIA,OAAO,CAAChB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;UAErC,MAAMX,QAAQ,GAAGjB,cAAc,CAACc,UAAU,CAAC;UAC3C,oBACEhC,OAAA;YAAsBK,SAAS,EAAC,WAAW;YAAA2C,QAAA,gBACzChD,OAAA;cAAIK,SAAS,EAAC,6BAA6B;cAAA2C,QAAA,EAAEb,QAAQ,CAAChB;YAAK;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjEpD,OAAA;cAAKK,SAAS,EAAC,WAAW;cAAA2C,QAAA,EACvBc,OAAO,CAACR,GAAG,CAACrB,MAAM,IAAI;gBACrB,MAAMO,IAAI,GAAGL,QAAQ,CAACb,YAAY,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAKU,MAAM,CAAC;gBAC7D,oBACEjC,OAAA;kBAAkBK,SAAS,EAAC,uDAAuD;kBAAA2C,QAAA,gBACjFhD,OAAA;oBAAAgD,QAAA,EAAOR,IAAI,CAACpB;kBAAI;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxBpD,OAAA;oBAAAgD,QAAA,EAAOR,IAAI,CAAChB;kBAAI;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFhBnB,MAAM;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGX,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAZEpB,UAAU;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaf,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDpD,OAAA;MAAKK,SAAS,EAAC,wCAAwC;MAAA2C,QAAA,gBACrDhD,OAAA,CAACJ,MAAM;QACL8D,OAAO,EAAC,SAAS;QACjBD,OAAO,EAAEtD,MAAO;QAChB8D,QAAQ,EAAC,WAAW;QACpBC,YAAY,EAAC,MAAM;QAAAlB,QAAA,EACpB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETpD,OAAA;QAAKK,SAAS,EAAC,yBAAyB;QAAA2C,QAAA,gBACtChD,OAAA;UAAKK,SAAS,EAAC,+BAA+B;UAAA2C,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpD,OAAA,CAACJ,MAAM;UACL6D,OAAO,EAAEV,UAAW;UACpBkB,QAAQ,EAAC,YAAY;UACrBC,YAAY,EAAC,OAAO;UAAAlB,QAAA,EACrB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA,CAACH,KAAK;MACJsE,MAAM,EAAEnD,WAAY;MACpBoD,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC,KAAK,CAAE;MACrCE,KAAK,EAAEL,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEU,IAAK;MAC1BmC,IAAI,EAAC,IAAI;MAAAX,QAAA,EAERlC,YAAY,iBACXd,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAA2C,QAAA,gBACxBhD,OAAA;UAAKK,SAAS,EAAC,wBAAwB;UAAA2C,QAAA,gBACrChD,OAAA;YAAKK,SAAS,EAAC,UAAU;YAAA2C,QAAA,EAAElC,YAAY,CAACM;UAAI;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnDpD,OAAA;YAAKK,SAAS,EAAC,QAAQ;YAAA2C,QAAA,gBACrBhD,OAAA;cAAGK,SAAS,EAAC,4BAA4B;cAAA2C,QAAA,EAAElC,YAAY,CAACO;YAAW;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAExEpD,OAAA;cAAKK,SAAS,EAAC,6BAA6B;cAAA2C,QAAA,gBAC1ChD,OAAA;gBAAAgD,QAAA,gBACEhD,OAAA;kBAAKK,SAAS,EAAC,qCAAqC;kBAAA2C,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrEpD,OAAA;kBAAKK,SAAS,EAAC,yBAAyB;kBAAA2C,QAAA,gBACtChD,OAAA;oBAAKK,SAAS,EAAC,sCAAsC;oBAAA2C,QAAA,eACnDhD,OAAA;sBACEK,SAAS,EAAC,yDAAyD;sBACnEgE,KAAK,EAAE;wBAAEC,KAAK,EAAE,GAAGxD,YAAY,CAACW,UAAU;sBAAI;oBAAE;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNpD,OAAA;oBAAMK,SAAS,EAAC,+BAA+B;oBAAA2C,QAAA,GAAElC,YAAY,CAACW,UAAU,EAAC,GAAC;kBAAA;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENpD,OAAA;gBAAAgD,QAAA,gBACEhD,OAAA;kBAAKK,SAAS,EAAC,qCAAqC;kBAAA2C,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzEpD,OAAA;kBAAKK,SAAS,EAAEV,EAAE,CAChB,oDAAoD,EACpDmB,YAAY,CAACY,aAAa,KAAK,MAAM,IAAI,6BAA6B,EACtEZ,YAAY,CAACY,aAAa,KAAK,QAAQ,IAAI,+BAA+B,EAC1EZ,YAAY,CAACY,aAAa,KAAK,MAAM,IAAI,yBAC3C,CAAE;kBAAAsB,QAAA,EACClC,YAAY,CAACY;gBAAa;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpD,OAAA;UAAKK,SAAS,EAAC,uCAAuC;UAAA2C,QAAA,gBACpDhD,OAAA;YAAAgD,QAAA,gBACEhD,OAAA;cAAIK,SAAS,EAAC,4DAA4D;cAAA2C,QAAA,gBACxEhD,OAAA,CAACF,IAAI;gBAAC0B,IAAI,EAAC,UAAU;gBAACnB,SAAS,EAAC;cAAwB;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAE7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpD,OAAA;cAAIK,SAAS,EAAC,WAAW;cAAA2C,QAAA,EACtBlC,YAAY,CAACc,IAAI,CAAC0B,GAAG,CAAC,CAACiB,GAAG,EAAEC,KAAK,kBAChCxE,OAAA;gBAAgBK,SAAS,EAAC,sDAAsD;gBAAA2C,QAAA,gBAC9EhD,OAAA,CAACF,IAAI;kBAAC0B,IAAI,EAAC,OAAO;kBAACnB,SAAS,EAAC;gBAA6C;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC5EmB,GAAG;cAAA,GAFGC,KAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENpD,OAAA;YAAAgD,QAAA,gBACEhD,OAAA;cAAIK,SAAS,EAAC,4DAA4D;cAAA2C,QAAA,gBACxEhD,OAAA,CAACF,IAAI;gBAAC0B,IAAI,EAAC,YAAY;gBAACnB,SAAS,EAAC;cAAsB;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAE7D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpD,OAAA;cAAIK,SAAS,EAAC,WAAW;cAAA2C,QAAA,EACtBlC,YAAY,CAACe,IAAI,CAACyB,GAAG,CAAC,CAACmB,GAAG,EAAED,KAAK,kBAChCxE,OAAA;gBAAgBK,SAAS,EAAC,sDAAsD;gBAAA2C,QAAA,gBAC9EhD,OAAA,CAACF,IAAI;kBAAC0B,IAAI,EAAC,GAAG;kBAACnB,SAAS,EAAC;gBAA2C;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACtEqB,GAAG;cAAA,GAFGD,KAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpD,OAAA;UAAAgD,QAAA,gBACEhD,OAAA;YAAIK,SAAS,EAAC,4DAA4D;YAAA2C,QAAA,gBACxEhD,OAAA,CAACF,IAAI;cAAC0B,IAAI,EAAC,QAAQ;cAACnB,SAAS,EAAC;YAAS;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBAE5C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpD,OAAA;YAAKK,SAAS,EAAC,sBAAsB;YAAA2C,QAAA,EAClClC,YAAY,CAACgB,QAAQ,CAACwB,GAAG,CAAC,CAACoB,OAAO,EAAEF,KAAK,kBACxCxE,OAAA;cAEEK,SAAS,EAAC,uEAAuE;cAAA2C,QAAA,EAEhF0B;YAAO,GAHHF,KAAK;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIN,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpD,OAAA;UAAKK,SAAS,EAAC,YAAY;UAAA2C,QAAA,gBACzBhD,OAAA,CAACJ,MAAM;YACL8D,OAAO,EAAC,SAAS;YACjBD,OAAO,EAAEA,CAAA,KAAMkB,MAAM,CAACC,IAAI,CAAC9D,YAAY,CAACa,aAAa,EAAE,QAAQ,CAAE;YACjEsC,QAAQ,EAAC,cAAc;YACvBC,YAAY,EAAC,OAAO;YAAAlB,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpD,OAAA,CAACJ,MAAM;YACL6D,OAAO,EAAEA,CAAA,KAAM;cACb;cACA,MAAMzB,UAAU,GAAGU,MAAM,CAACmC,IAAI,CAAC3D,cAAc,CAAC,CAAC6C,IAAI,CAACe,KAAK,IACvD5D,cAAc,CAAC4D,KAAK,CAAC,CAACxD,YAAY,CAACyD,IAAI,CAACf,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAKT,YAAY,CAACS,EAAE,CACvE,CAAC;cACD,IAAIS,UAAU,EAAE;gBACdD,gBAAgB,CAACC,UAAU,EAAElB,YAAY,CAACS,EAAE,CAAC;cAC/C;cACAN,cAAc,CAAC,KAAK,CAAC;YACvB,CAAE;YACFgD,QAAQ,EACNvB,MAAM,CAACC,MAAM,CAACpC,oBAAoB,CAAC,CAACwE,IAAI,CAAC5C,QAAQ,IAC/CA,QAAQ,CAACE,QAAQ,CAACvB,YAAY,CAACS,EAAE,CACnC,CAAC,GAAG,OAAO,GAAG,MACf;YACD2C,YAAY,EAAC,MAAM;YAAAlB,QAAA,EAElBN,MAAM,CAACC,MAAM,CAACpC,oBAAoB,CAAC,CAACwE,IAAI,CAAC5C,QAAQ,IAChDA,QAAQ,CAACE,QAAQ,CAACvB,YAAY,CAACS,EAAE,CACnC,CAAC,GAAG,mBAAmB,GAAG;UAAc;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA1lBIL,gBAAgB;AAAA+E,EAAA,GAAhB/E,gBAAgB;AA4lBtB,eAAeA,gBAAgB;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}