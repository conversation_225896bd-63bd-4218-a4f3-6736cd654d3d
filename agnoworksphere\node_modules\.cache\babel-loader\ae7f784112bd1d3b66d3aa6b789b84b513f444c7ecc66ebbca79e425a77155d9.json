{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\ui\\\\Slider.jsx\";\nimport React from 'react';\nimport { cn } from '../../utils/cn';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Slider = /*#__PURE__*/React.forwardRef(_c = ({\n  className,\n  min = 0,\n  max = 100,\n  step = 1,\n  value = 0,\n  onChange,\n  label,\n  description,\n  showValue = true,\n  formatValue,\n  disabled = false,\n  ...props\n}, ref) => {\n  const handleChange = e => {\n    const newValue = parseFloat(e.target.value);\n    onChange === null || onChange === void 0 ? void 0 : onChange(newValue);\n  };\n  const displayValue = formatValue ? formatValue(value) : value;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn(\"space-y-2\", className),\n    children: [label && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"text-sm font-medium text-foreground\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), showValue && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm text-muted-foreground font-mono\",\n        children: displayValue\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ref: ref,\n        type: \"range\",\n        min: min,\n        max: max,\n        step: step,\n        value: value,\n        onChange: handleChange,\n        disabled: disabled,\n        className: cn(\"w-full h-2 bg-secondary rounded-lg appearance-none cursor-pointer\", \"slider-thumb:appearance-none slider-thumb:h-4 slider-thumb:w-4 slider-thumb:rounded-full\", \"slider-thumb:bg-primary slider-thumb:cursor-pointer slider-thumb:border-0\", \"slider-thumb:shadow-md slider-thumb:transition-all slider-thumb:duration-200\", \"hover:slider-thumb:scale-110 focus:slider-thumb:scale-110\", \"focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", \"disabled:opacity-50 disabled:cursor-not-allowed\", className),\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 h-2 bg-primary rounded-lg pointer-events-none\",\n        style: {\n          width: `${(value - min) / (max - min) * 100}%`,\n          transition: 'width 0.2s ease'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), description && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-xs text-muted-foreground\",\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n});\n_c2 = Slider;\nSlider.displayName = \"Slider\";\nexport default Slider;\nvar _c, _c2;\n$RefreshReg$(_c, \"Slider$React.forwardRef\");\n$RefreshReg$(_c2, \"Slider\");", "map": {"version": 3, "names": ["React", "cn", "jsxDEV", "_jsxDEV", "Slide<PERSON>", "forwardRef", "_c", "className", "min", "max", "step", "value", "onChange", "label", "description", "showValue", "formatValue", "disabled", "props", "ref", "handleChange", "e", "newValue", "parseFloat", "target", "displayValue", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "style", "width", "transition", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/ui/Slider.jsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '../../utils/cn';\n\nconst Slider = React.forwardRef(({\n  className,\n  min = 0,\n  max = 100,\n  step = 1,\n  value = 0,\n  onChange,\n  label,\n  description,\n  showValue = true,\n  formatValue,\n  disabled = false,\n  ...props\n}, ref) => {\n  const handleChange = (e) => {\n    const newValue = parseFloat(e.target.value);\n    onChange?.(newValue);\n  };\n\n  const displayValue = formatValue ? formatValue(value) : value;\n\n  return (\n    <div className={cn(\"space-y-2\", className)}>\n      {label && (\n        <div className=\"flex items-center justify-between\">\n          <label className=\"text-sm font-medium text-foreground\">\n            {label}\n          </label>\n          {showValue && (\n            <span className=\"text-sm text-muted-foreground font-mono\">\n              {displayValue}\n            </span>\n          )}\n        </div>\n      )}\n      \n      <div className=\"relative\">\n        <input\n          ref={ref}\n          type=\"range\"\n          min={min}\n          max={max}\n          step={step}\n          value={value}\n          onChange={handleChange}\n          disabled={disabled}\n          className={cn(\n            \"w-full h-2 bg-secondary rounded-lg appearance-none cursor-pointer\",\n            \"slider-thumb:appearance-none slider-thumb:h-4 slider-thumb:w-4 slider-thumb:rounded-full\",\n            \"slider-thumb:bg-primary slider-thumb:cursor-pointer slider-thumb:border-0\",\n            \"slider-thumb:shadow-md slider-thumb:transition-all slider-thumb:duration-200\",\n            \"hover:slider-thumb:scale-110 focus:slider-thumb:scale-110\",\n            \"focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n            \"disabled:opacity-50 disabled:cursor-not-allowed\",\n            className\n          )}\n          {...props}\n        />\n        \n        {/* Progress track */}\n        <div \n          className=\"absolute top-0 h-2 bg-primary rounded-lg pointer-events-none\"\n          style={{ \n            width: `${((value - min) / (max - min)) * 100}%`,\n            transition: 'width 0.2s ease'\n          }}\n        />\n      </div>\n      \n      {description && (\n        <p className=\"text-xs text-muted-foreground\">\n          {description}\n        </p>\n      )}\n    </div>\n  );\n});\n\nSlider.displayName = \"Slider\";\n\nexport default Slider;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,EAAE,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,MAAM,gBAAGJ,KAAK,CAACK,UAAU,CAAAC,EAAA,GAACA,CAAC;EAC/BC,SAAS;EACTC,GAAG,GAAG,CAAC;EACPC,GAAG,GAAG,GAAG;EACTC,IAAI,GAAG,CAAC;EACRC,KAAK,GAAG,CAAC;EACTC,QAAQ;EACRC,KAAK;EACLC,WAAW;EACXC,SAAS,GAAG,IAAI;EAChBC,WAAW;EACXC,QAAQ,GAAG,KAAK;EAChB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAMC,QAAQ,GAAGC,UAAU,CAACF,CAAC,CAACG,MAAM,CAACb,KAAK,CAAC;IAC3CC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGU,QAAQ,CAAC;EACtB,CAAC;EAED,MAAMG,YAAY,GAAGT,WAAW,GAAGA,WAAW,CAACL,KAAK,CAAC,GAAGA,KAAK;EAE7D,oBACER,OAAA;IAAKI,SAAS,EAAEN,EAAE,CAAC,WAAW,EAAEM,SAAS,CAAE;IAAAmB,QAAA,GACxCb,KAAK,iBACJV,OAAA;MAAKI,SAAS,EAAC,mCAAmC;MAAAmB,QAAA,gBAChDvB,OAAA;QAAOI,SAAS,EAAC,qCAAqC;QAAAmB,QAAA,EACnDb;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EACPf,SAAS,iBACRZ,OAAA;QAAMI,SAAS,EAAC,yCAAyC;QAAAmB,QAAA,EACtDD;MAAY;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAED3B,OAAA;MAAKI,SAAS,EAAC,UAAU;MAAAmB,QAAA,gBACvBvB,OAAA;QACEgB,GAAG,EAAEA,GAAI;QACTY,IAAI,EAAC,OAAO;QACZvB,GAAG,EAAEA,GAAI;QACTC,GAAG,EAAEA,GAAI;QACTC,IAAI,EAAEA,IAAK;QACXC,KAAK,EAAEA,KAAM;QACbC,QAAQ,EAAEQ,YAAa;QACvBH,QAAQ,EAAEA,QAAS;QACnBV,SAAS,EAAEN,EAAE,CACX,mEAAmE,EACnE,0FAA0F,EAC1F,2EAA2E,EAC3E,8EAA8E,EAC9E,2DAA2D,EAC3D,qEAAqE,EACrE,iDAAiD,EACjDM,SACF,CAAE;QAAA,GACEW;MAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGF3B,OAAA;QACEI,SAAS,EAAC,8DAA8D;QACxEyB,KAAK,EAAE;UACLC,KAAK,EAAE,GAAI,CAACtB,KAAK,GAAGH,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAI,GAAG,GAAG;UAChD0B,UAAU,EAAE;QACd;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELhB,WAAW,iBACVX,OAAA;MAAGI,SAAS,EAAC,+BAA+B;MAAAmB,QAAA,EACzCZ;IAAW;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC,CAAC;AAACK,GAAA,GA5EG/B,MAAM;AA8EZA,MAAM,CAACgC,WAAW,GAAG,QAAQ;AAE7B,eAAehC,MAAM;AAAC,IAAAE,EAAA,EAAA6B,GAAA;AAAAE,YAAA,CAAA/B,EAAA;AAAA+B,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}