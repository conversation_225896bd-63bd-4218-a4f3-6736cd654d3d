{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\ui\\\\Modal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from './Button';\nimport Icon from '../AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Modal = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'default',\n  showCloseButton = true,\n  closeOnOverlayClick = true,\n  className,\n  ...props\n}) => {\n  _s();\n  const sizeClasses = {\n    sm: 'max-w-md',\n    default: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl',\n    full: 'max-w-7xl'\n  };\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n  useEffect(() => {\n    const handleEscape = e => {\n      if (e.key === 'Escape' && isOpen) {\n        onClose === null || onClose === void 0 ? void 0 : onClose();\n      }\n    };\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  const handleOverlayClick = e => {\n    if (e.target === e.currentTarget && closeOnOverlayClick) {\n      onClose === null || onClose === void 0 ? void 0 : onClose();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\",\n    onClick: handleOverlayClick,\n    ...props,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: cn(\"relative w-full bg-background rounded-lg shadow-lg border border-border\", \"animate-in fade-in-0 zoom-in-95 duration-200\", sizeClasses[size], className),\n      onClick: e => e.stopPropagation(),\n      children: [(title || showCloseButton) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b border-border\",\n        children: [title && /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-foreground\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 15\n        }, this), showCloseButton && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"ghost\",\n          size: \"sm\",\n          onClick: onClose,\n          className: \"h-8 w-8 p-0\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            name: \"X\",\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_s(Modal, \"3ubReDTFssvu4DHeldAg55cW/CI=\");\n_c = Modal;\nexport default Modal;\nvar _c;\n$RefreshReg$(_c, \"Modal\");", "map": {"version": 3, "names": ["React", "useEffect", "cn", "<PERSON><PERSON>", "Icon", "jsxDEV", "_jsxDEV", "Modal", "isOpen", "onClose", "title", "children", "size", "showCloseButton", "closeOnOverlayClick", "className", "props", "_s", "sizeClasses", "sm", "default", "lg", "xl", "full", "document", "body", "style", "overflow", "handleEscape", "e", "key", "addEventListener", "removeEventListener", "handleOverlayClick", "target", "currentTarget", "onClick", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/ui/Modal.jsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from './Button';\nimport Icon from '../AppIcon';\n\nconst Modal = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'default',\n  showCloseButton = true,\n  closeOnOverlayClick = true,\n  className,\n  ...props\n}) => {\n  const sizeClasses = {\n    sm: 'max-w-md',\n    default: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl',\n    full: 'max-w-7xl'\n  };\n\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape' && isOpen) {\n        onClose?.();\n      }\n    };\n\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const handleOverlayClick = (e) => {\n    if (e.target === e.currentTarget && closeOnOverlayClick) {\n      onClose?.();\n    }\n  };\n\n  return (\n    <div\n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n      onClick={handleOverlayClick}\n      {...props}\n    >\n      <div\n        className={cn(\n          \"relative w-full bg-background rounded-lg shadow-lg border border-border\",\n          \"animate-in fade-in-0 zoom-in-95 duration-200\",\n          sizeClasses[size],\n          className\n        )}\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        {(title || showCloseButton) && (\n          <div className=\"flex items-center justify-between p-6 border-b border-border\">\n            {title && (\n              <h2 className=\"text-lg font-semibold text-foreground\">\n                {title}\n              </h2>\n            )}\n            {showCloseButton && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={onClose}\n                className=\"h-8 w-8 p-0\"\n              >\n                <Icon name=\"X\" className=\"h-4 w-4\" />\n              </Button>\n            )}\n          </div>\n        )}\n\n        {/* Content */}\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,EAAE,QAAQ,gBAAgB;AACnC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,KAAK,GAAGA,CAAC;EACbC,MAAM;EACNC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC,IAAI,GAAG,SAAS;EAChBC,eAAe,GAAG,IAAI;EACtBC,mBAAmB,GAAG,IAAI;EAC1BC,SAAS;EACT,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,UAAU;IACdC,OAAO,EAAE,UAAU;IACnBC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,WAAW;IACfC,IAAI,EAAE;EACR,CAAC;EAEDtB,SAAS,CAAC,MAAM;IACd,IAAIO,MAAM,EAAE;MACVgB,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACnB,MAAM,CAAC,CAAC;EAEZP,SAAS,CAAC,MAAM;IACd,MAAM2B,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,IAAItB,MAAM,EAAE;QAChCC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,CAAC;MACb;IACF,CAAC;IAEDe,QAAQ,CAACO,gBAAgB,CAAC,SAAS,EAAEH,YAAY,CAAC;IAClD,OAAO,MAAMJ,QAAQ,CAACQ,mBAAmB,CAAC,SAAS,EAAEJ,YAAY,CAAC;EACpE,CAAC,EAAE,CAACpB,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMyB,kBAAkB,GAAIJ,CAAC,IAAK;IAChC,IAAIA,CAAC,CAACK,MAAM,KAAKL,CAAC,CAACM,aAAa,IAAIrB,mBAAmB,EAAE;MACvDL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,CAAC;IACb;EACF,CAAC;EAED,oBACEH,OAAA;IACES,SAAS,EAAC,sFAAsF;IAChGqB,OAAO,EAAEH,kBAAmB;IAAA,GACxBjB,KAAK;IAAAL,QAAA,eAETL,OAAA;MACES,SAAS,EAAEb,EAAE,CACX,yEAAyE,EACzE,8CAA8C,EAC9CgB,WAAW,CAACN,IAAI,CAAC,EACjBG,SACF,CAAE;MACFqB,OAAO,EAAGP,CAAC,IAAKA,CAAC,CAACQ,eAAe,CAAC,CAAE;MAAA1B,QAAA,GAGnC,CAACD,KAAK,IAAIG,eAAe,kBACxBP,OAAA;QAAKS,SAAS,EAAC,8DAA8D;QAAAJ,QAAA,GAC1ED,KAAK,iBACJJ,OAAA;UAAIS,SAAS,EAAC,uCAAuC;UAAAJ,QAAA,EAClDD;QAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACL,EACA5B,eAAe,iBACdP,OAAA,CAACH,MAAM;UACLuC,OAAO,EAAC,OAAO;UACf9B,IAAI,EAAC,IAAI;UACTwB,OAAO,EAAE3B,OAAQ;UACjBM,SAAS,EAAC,aAAa;UAAAJ,QAAA,eAEvBL,OAAA,CAACF,IAAI;YAACuC,IAAI,EAAC,GAAG;YAAC5B,SAAS,EAAC;UAAS;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAGDnC,OAAA;QAAKS,SAAS,EAAC,KAAK;QAAAJ,QAAA,EACjBA;MAAQ;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CA7FIV,KAAK;AAAAqC,EAAA,GAALrC,KAAK;AA+FX,eAAeA,KAAK;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}