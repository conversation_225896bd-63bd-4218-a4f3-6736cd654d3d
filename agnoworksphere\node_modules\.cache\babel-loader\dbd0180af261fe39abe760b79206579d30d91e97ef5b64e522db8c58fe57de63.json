{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\modals\\\\CreateAIProjectModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport Select from '../ui/Select';\nimport Icon from '../AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateAIProjectModal = ({\n  isOpen,\n  onClose,\n  onCreateAIProject,\n  organizationId,\n  organizationName\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    projectType: 'general',\n    teamSize: 5,\n    teamExperience: 'intermediate'\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [currentStep, setCurrentStep] = useState(1);\n  const [projectPreview, setProjectPreview] = useState(null);\n  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);\n  const projectTypes = [{\n    value: 'general',\n    label: 'General Project',\n    description: 'Standard project with flexible workflow',\n    icon: 'Folder',\n    color: 'bg-gray-500'\n  }, {\n    value: 'web_application',\n    label: 'Web Application',\n    description: 'Full-stack web development project',\n    icon: 'Globe',\n    color: 'bg-blue-500'\n  }, {\n    value: 'mobile_app',\n    label: 'Mobile App',\n    description: 'iOS/Android mobile application',\n    icon: 'Smartphone',\n    color: 'bg-green-500'\n  }, {\n    value: 'ecommerce_platform',\n    label: 'E-commerce Platform',\n    description: 'Online marketplace with payment processing',\n    icon: 'ShoppingCart',\n    color: 'bg-purple-500'\n  }, {\n    value: 'saas_application',\n    label: 'SaaS Application',\n    description: 'Multi-tenant cloud-based software',\n    icon: 'Cloud',\n    color: 'bg-indigo-500'\n  }, {\n    value: 'devops_infrastructure',\n    label: 'DevOps/Infrastructure',\n    description: 'CI/CD pipelines and infrastructure automation',\n    icon: 'Server',\n    color: 'bg-orange-500'\n  }, {\n    value: 'research_development',\n    label: 'Research & Development',\n    description: 'Scientific research and innovation project',\n    icon: 'Microscope',\n    color: 'bg-teal-500'\n  }, {\n    value: 'event_management',\n    label: 'Event Management',\n    description: 'Comprehensive event planning and execution',\n    icon: 'Calendar',\n    color: 'bg-pink-500'\n  }, {\n    value: 'data_analysis',\n    label: 'Data Analysis',\n    description: 'Data science and analytics project',\n    icon: 'BarChart3',\n    color: 'bg-cyan-500'\n  }, {\n    value: 'marketing_campaign',\n    label: 'Marketing Campaign',\n    description: 'Strategic marketing and promotion campaign',\n    icon: 'Megaphone',\n    color: 'bg-red-500'\n  }];\n  const teamSizeOptions = [{\n    value: 2,\n    label: '2 people (Small team)'\n  }, {\n    value: 3,\n    label: '3 people (Small team)'\n  }, {\n    value: 5,\n    label: '5 people (Optimal team)'\n  }, {\n    value: 8,\n    label: '8 people (Large team)'\n  }, {\n    value: 12,\n    label: '12+ people (Enterprise team)'\n  }];\n  const experienceOptions = [{\n    value: 'junior',\n    label: 'Junior (0-2 years)'\n  }, {\n    value: 'intermediate',\n    label: 'Intermediate (2-5 years)'\n  }, {\n    value: 'senior',\n    label: 'Senior (5+ years)'\n  }, {\n    value: 'expert',\n    label: 'Expert (10+ years)'\n  }];\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n  const generatePreview = async () => {\n    if (!formData.name.trim()) {\n      setError('Project name is required for preview');\n      return;\n    }\n    setIsGeneratingPreview(true);\n    try {\n      // Simulate AI preview generation (in real implementation, call a preview API)\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      const selectedType = projectTypes.find(type => type.value === formData.projectType);\n      setProjectPreview({\n        name: formData.name,\n        type: selectedType,\n        estimatedDuration: getEstimatedDuration(formData.projectType, formData.teamSize, formData.teamExperience),\n        estimatedTasks: getEstimatedTaskCount(formData.projectType),\n        phases: getProjectPhases(formData.projectType),\n        teamRecommendations: getTeamRecommendations(formData.teamSize, formData.teamExperience),\n        technologies: getTechnologies(formData.projectType)\n      });\n      setCurrentStep(2);\n    } catch (err) {\n      setError('Failed to generate preview');\n    } finally {\n      setIsGeneratingPreview(false);\n    }\n  };\n  const getEstimatedDuration = (type, teamSize, experience) => {\n    const baseDurations = {\n      'web_application': 50,\n      'mobile_app': 57,\n      'ecommerce_platform': 87,\n      'saas_application': 97,\n      'devops_infrastructure': 52,\n      'research_development': 89,\n      'event_management': 65,\n      'data_analysis': 45,\n      'marketing_campaign': 35,\n      'general': 43\n    };\n    let duration = baseDurations[type] || 43;\n\n    // Adjust for team experience\n    const experienceMultipliers = {\n      junior: 1.3,\n      intermediate: 1.0,\n      senior: 0.8,\n      expert: 0.7\n    };\n    duration *= experienceMultipliers[experience] || 1.0;\n\n    // Adjust for team size\n    if (teamSize <= 2) duration *= 1.2;else if (teamSize >= 8) duration *= 0.9;\n    return Math.round(duration);\n  };\n  const getEstimatedTaskCount = type => {\n    const taskCounts = {\n      'web_application': 25,\n      'mobile_app': 28,\n      'ecommerce_platform': 45,\n      'saas_application': 52,\n      'devops_infrastructure': 22,\n      'research_development': 35,\n      'event_management': 30,\n      'data_analysis': 20,\n      'marketing_campaign': 18,\n      'general': 15\n    };\n    return taskCounts[type] || 15;\n  };\n  const getProjectPhases = type => {\n    const phases = {\n      'web_application': ['Planning & Analysis', 'Design & Architecture', 'Development', 'Testing & QA', 'Deployment & Launch'],\n      'ecommerce_platform': ['Market Research & Planning', 'Core Platform Development', 'Payment & Security Integration', 'Advanced Features & Optimization', 'Testing & Launch'],\n      'saas_application': ['Architecture & Infrastructure Design', 'Core Application Development', 'Subscription & Billing System', 'Enterprise Features & Security', 'Deployment & Monitoring']\n    };\n    return phases[type] || ['Initiation & Planning', 'Execution Phase 1', 'Execution Phase 2', 'Finalization & Review', 'Closure & Handover'];\n  };\n  const getTeamRecommendations = (size, experience) => {\n    const recommendations = [];\n    if (size <= 2) recommendations.push('Consider adding more team members for complex phases');\n    if (size >= 8) recommendations.push('Break into smaller sub-teams to reduce coordination overhead');\n    if (experience === 'junior') recommendations.push('Assign senior mentor for guidance');\n    if (experience === 'expert') recommendations.push('Leverage team expertise for innovation opportunities');\n    return recommendations;\n  };\n  const getTechnologies = type => {\n    const tech = {\n      'web_application': ['React/Vue.js', 'Node.js/Python', 'PostgreSQL', 'Docker', 'AWS/Azure'],\n      'ecommerce_platform': ['React/Next.js', 'Node.js/Express', 'PostgreSQL', 'Stripe/PayPal', 'Redis', 'Docker'],\n      'saas_application': ['React/Angular', 'Node.js/Python', 'PostgreSQL', 'Docker', 'Kubernetes', 'AWS/GCP']\n    };\n    return tech[type] || ['Modern Framework', 'Backend Technology', 'Database', 'Cloud Platform'];\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!formData.name.trim()) {\n      setError('Project name is required');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const projectData = {\n        name: formData.name.trim(),\n        project_type: formData.projectType,\n        organization_id: organizationId\n      };\n      await onCreateAIProject(projectData);\n\n      // Reset form and close modal\n      setFormData({\n        name: '',\n        projectType: 'general'\n      });\n      onClose();\n    } catch (err) {\n      setError(err.message || 'Failed to create AI project');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleClose = () => {\n    if (!isLoading) {\n      setFormData({\n        name: '',\n        projectType: 'general'\n      });\n      setError('');\n      onClose();\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Sparkles\",\n              size: 20,\n              className: \"text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-text-primary\",\n              children: \"Create AI Project\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-text-secondary\",\n              children: \"Let AI generate your complete project structure\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"ghost\",\n          size: \"sm\",\n          onClick: handleClose,\n          disabled: isLoading,\n          iconName: \"X\",\n          className: \"text-text-secondary hover:text-text-primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"p-6 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-muted rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 text-sm text-text-secondary\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Building2\",\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Creating in: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"text-text-primary\",\n                children: organizationName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 text-red-700\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"AlertCircle\",\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-text-primary\",\n            children: \"Project Name *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"text\",\n            value: formData.name,\n            onChange: e => handleInputChange('name', e.target.value),\n            placeholder: \"Enter your project name...\",\n            disabled: isLoading,\n            className: \"w-full\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-text-secondary\",\n            children: \"AI will generate a complete project structure based on this name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-text-primary\",\n            children: \"Project Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: formData.projectType,\n            onChange: value => handleInputChange('projectType', value),\n            options: projectTypes,\n            disabled: isLoading,\n            className: \"w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-text-secondary\",\n            children: \"Choose the type that best matches your project for optimized AI generation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-purple-900 mb-2 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Sparkles\",\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), \"What AI will generate for you:\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"text-xs text-purple-800 space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Comprehensive project description and objectives\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Detailed workflow with phases and milestones\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Complete task breakdown with priorities and estimates\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Kanban board with organized task categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"\\u2022 Project timeline and resource recommendations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-end gap-3 pt-4 border-t border-border\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"button\",\n            variant: \"outline\",\n            onClick: handleClose,\n            disabled: isLoading,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"default\",\n            disabled: isLoading || !formData.name.trim(),\n            iconName: isLoading ? \"Loader2\" : \"Sparkles\",\n            iconPosition: \"left\",\n            className: `${isLoading ? \"animate-spin\" : \"\"} bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 border-0`,\n            children: isLoading ? 'Generating Project...' : 'Create AI Project'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateAIProjectModal, \"5yHcF44uvLOBV3juB5sYYlNeEsw=\");\n_c = CreateAIProjectModal;\nexport default CreateAIProjectModal;\nvar _c;\n$RefreshReg$(_c, \"CreateAIProjectModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "Input", "Select", "Icon", "jsxDEV", "_jsxDEV", "CreateAIProjectModal", "isOpen", "onClose", "onCreateAIProject", "organizationId", "organizationName", "_s", "formData", "setFormData", "name", "projectType", "teamSize", "teamExperience", "isLoading", "setIsLoading", "error", "setError", "currentStep", "setCurrentStep", "projectPreview", "setProjectPreview", "isGeneratingPreview", "setIsGeneratingPreview", "projectTypes", "value", "label", "description", "icon", "color", "teamSizeOptions", "experienceOptions", "handleInputChange", "field", "prev", "generatePreview", "trim", "Promise", "resolve", "setTimeout", "selectedType", "find", "type", "estimatedDuration", "getEstimatedDuration", "estimatedTasks", "getEstimatedTaskCount", "phases", "getProjectPhases", "teamRecommendations", "getTeamRecommendations", "technologies", "getTechnologies", "err", "experience", "baseDurations", "duration", "experienceMultipliers", "junior", "intermediate", "senior", "expert", "Math", "round", "taskCounts", "size", "recommendations", "push", "tech", "handleSubmit", "e", "preventDefault", "projectData", "project_type", "organization_id", "message", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "disabled", "iconName", "onSubmit", "onChange", "target", "placeholder", "required", "options", "iconPosition", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/modals/CreateAIProjectModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport Select from '../ui/Select';\nimport Icon from '../AppIcon';\n\nconst CreateAIProjectModal = ({ isOpen, onClose, onCreateAIProject, organizationId, organizationName }) => {\n  const [formData, setFormData] = useState({\n    name: '',\n    projectType: 'general',\n    teamSize: 5,\n    teamExperience: 'intermediate'\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [currentStep, setCurrentStep] = useState(1);\n  const [projectPreview, setProjectPreview] = useState(null);\n  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);\n\n  const projectTypes = [\n    { value: 'general', label: 'General Project', description: 'Standard project with flexible workflow', icon: 'Folder', color: 'bg-gray-500' },\n    { value: 'web_application', label: 'Web Application', description: 'Full-stack web development project', icon: 'Globe', color: 'bg-blue-500' },\n    { value: 'mobile_app', label: 'Mobile App', description: 'iOS/Android mobile application', icon: 'Smartphone', color: 'bg-green-500' },\n    { value: 'ecommerce_platform', label: 'E-commerce Platform', description: 'Online marketplace with payment processing', icon: 'ShoppingCart', color: 'bg-purple-500' },\n    { value: 'saas_application', label: 'SaaS Application', description: 'Multi-tenant cloud-based software', icon: 'Cloud', color: 'bg-indigo-500' },\n    { value: 'devops_infrastructure', label: 'DevOps/Infrastructure', description: 'CI/CD pipelines and infrastructure automation', icon: 'Server', color: 'bg-orange-500' },\n    { value: 'research_development', label: 'Research & Development', description: 'Scientific research and innovation project', icon: 'Microscope', color: 'bg-teal-500' },\n    { value: 'event_management', label: 'Event Management', description: 'Comprehensive event planning and execution', icon: 'Calendar', color: 'bg-pink-500' },\n    { value: 'data_analysis', label: 'Data Analysis', description: 'Data science and analytics project', icon: 'BarChart3', color: 'bg-cyan-500' },\n    { value: 'marketing_campaign', label: 'Marketing Campaign', description: 'Strategic marketing and promotion campaign', icon: 'Megaphone', color: 'bg-red-500' }\n  ];\n\n  const teamSizeOptions = [\n    { value: 2, label: '2 people (Small team)' },\n    { value: 3, label: '3 people (Small team)' },\n    { value: 5, label: '5 people (Optimal team)' },\n    { value: 8, label: '8 people (Large team)' },\n    { value: 12, label: '12+ people (Enterprise team)' }\n  ];\n\n  const experienceOptions = [\n    { value: 'junior', label: 'Junior (0-2 years)' },\n    { value: 'intermediate', label: 'Intermediate (2-5 years)' },\n    { value: 'senior', label: 'Senior (5+ years)' },\n    { value: 'expert', label: 'Expert (10+ years)' }\n  ];\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n\n  const generatePreview = async () => {\n    if (!formData.name.trim()) {\n      setError('Project name is required for preview');\n      return;\n    }\n\n    setIsGeneratingPreview(true);\n    try {\n      // Simulate AI preview generation (in real implementation, call a preview API)\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      const selectedType = projectTypes.find(type => type.value === formData.projectType);\n\n      setProjectPreview({\n        name: formData.name,\n        type: selectedType,\n        estimatedDuration: getEstimatedDuration(formData.projectType, formData.teamSize, formData.teamExperience),\n        estimatedTasks: getEstimatedTaskCount(formData.projectType),\n        phases: getProjectPhases(formData.projectType),\n        teamRecommendations: getTeamRecommendations(formData.teamSize, formData.teamExperience),\n        technologies: getTechnologies(formData.projectType)\n      });\n\n      setCurrentStep(2);\n    } catch (err) {\n      setError('Failed to generate preview');\n    } finally {\n      setIsGeneratingPreview(false);\n    }\n  };\n\n  const getEstimatedDuration = (type, teamSize, experience) => {\n    const baseDurations = {\n      'web_application': 50,\n      'mobile_app': 57,\n      'ecommerce_platform': 87,\n      'saas_application': 97,\n      'devops_infrastructure': 52,\n      'research_development': 89,\n      'event_management': 65,\n      'data_analysis': 45,\n      'marketing_campaign': 35,\n      'general': 43\n    };\n\n    let duration = baseDurations[type] || 43;\n\n    // Adjust for team experience\n    const experienceMultipliers = { junior: 1.3, intermediate: 1.0, senior: 0.8, expert: 0.7 };\n    duration *= experienceMultipliers[experience] || 1.0;\n\n    // Adjust for team size\n    if (teamSize <= 2) duration *= 1.2;\n    else if (teamSize >= 8) duration *= 0.9;\n\n    return Math.round(duration);\n  };\n\n  const getEstimatedTaskCount = (type) => {\n    const taskCounts = {\n      'web_application': 25,\n      'mobile_app': 28,\n      'ecommerce_platform': 45,\n      'saas_application': 52,\n      'devops_infrastructure': 22,\n      'research_development': 35,\n      'event_management': 30,\n      'data_analysis': 20,\n      'marketing_campaign': 18,\n      'general': 15\n    };\n    return taskCounts[type] || 15;\n  };\n\n  const getProjectPhases = (type) => {\n    const phases = {\n      'web_application': ['Planning & Analysis', 'Design & Architecture', 'Development', 'Testing & QA', 'Deployment & Launch'],\n      'ecommerce_platform': ['Market Research & Planning', 'Core Platform Development', 'Payment & Security Integration', 'Advanced Features & Optimization', 'Testing & Launch'],\n      'saas_application': ['Architecture & Infrastructure Design', 'Core Application Development', 'Subscription & Billing System', 'Enterprise Features & Security', 'Deployment & Monitoring']\n    };\n    return phases[type] || ['Initiation & Planning', 'Execution Phase 1', 'Execution Phase 2', 'Finalization & Review', 'Closure & Handover'];\n  };\n\n  const getTeamRecommendations = (size, experience) => {\n    const recommendations = [];\n    if (size <= 2) recommendations.push('Consider adding more team members for complex phases');\n    if (size >= 8) recommendations.push('Break into smaller sub-teams to reduce coordination overhead');\n    if (experience === 'junior') recommendations.push('Assign senior mentor for guidance');\n    if (experience === 'expert') recommendations.push('Leverage team expertise for innovation opportunities');\n    return recommendations;\n  };\n\n  const getTechnologies = (type) => {\n    const tech = {\n      'web_application': ['React/Vue.js', 'Node.js/Python', 'PostgreSQL', 'Docker', 'AWS/Azure'],\n      'ecommerce_platform': ['React/Next.js', 'Node.js/Express', 'PostgreSQL', 'Stripe/PayPal', 'Redis', 'Docker'],\n      'saas_application': ['React/Angular', 'Node.js/Python', 'PostgreSQL', 'Docker', 'Kubernetes', 'AWS/GCP']\n    };\n    return tech[type] || ['Modern Framework', 'Backend Technology', 'Database', 'Cloud Platform'];\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.name.trim()) {\n      setError('Project name is required');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const projectData = {\n        name: formData.name.trim(),\n        project_type: formData.projectType,\n        organization_id: organizationId\n      };\n\n      await onCreateAIProject(projectData);\n\n      // Reset form and close modal\n      setFormData({\n        name: '',\n        projectType: 'general'\n      });\n      onClose();\n    } catch (err) {\n      setError(err.message || 'Failed to create AI project');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isLoading) {\n      setFormData({\n        name: '',\n        projectType: 'general'\n      });\n      setError('');\n      onClose();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-border\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg flex items-center justify-center\">\n              <Icon name=\"Sparkles\" size={20} className=\"text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-xl font-semibold text-text-primary\">Create AI Project</h2>\n              <p className=\"text-sm text-text-secondary\">Let AI generate your complete project structure</p>\n            </div>\n          </div>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleClose}\n            disabled={isLoading}\n            iconName=\"X\"\n            className=\"text-text-secondary hover:text-text-primary\"\n          />\n        </div>\n\n        {/* Content */}\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n          {/* Organization Info */}\n          <div className=\"bg-muted rounded-lg p-4\">\n            <div className=\"flex items-center gap-2 text-sm text-text-secondary\">\n              <Icon name=\"Building2\" size={16} />\n              <span>Creating in: <strong className=\"text-text-primary\">{organizationName}</strong></span>\n            </div>\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n              <div className=\"flex items-center gap-2 text-red-700\">\n                <Icon name=\"AlertCircle\" size={16} />\n                <span className=\"text-sm font-medium\">{error}</span>\n              </div>\n            </div>\n          )}\n\n          {/* Project Name */}\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-text-primary\">\n              Project Name *\n            </label>\n            <Input\n              type=\"text\"\n              value={formData.name}\n              onChange={(e) => handleInputChange('name', e.target.value)}\n              placeholder=\"Enter your project name...\"\n              disabled={isLoading}\n              className=\"w-full\"\n              required\n            />\n            <p className=\"text-xs text-text-secondary\">\n              AI will generate a complete project structure based on this name\n            </p>\n          </div>\n\n          {/* Project Type */}\n          <div className=\"space-y-2\">\n            <label className=\"block text-sm font-medium text-text-primary\">\n              Project Type\n            </label>\n            <Select\n              value={formData.projectType}\n              onChange={(value) => handleInputChange('projectType', value)}\n              options={projectTypes}\n              disabled={isLoading}\n              className=\"w-full\"\n            />\n            <p className=\"text-xs text-text-secondary\">\n              Choose the type that best matches your project for optimized AI generation\n            </p>\n          </div>\n\n          {/* AI Features Info */}\n          <div className=\"bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4\">\n            <h3 className=\"text-sm font-medium text-purple-900 mb-2 flex items-center gap-2\">\n              <Icon name=\"Sparkles\" size={16} />\n              What AI will generate for you:\n            </h3>\n            <ul className=\"text-xs text-purple-800 space-y-1\">\n              <li>• Comprehensive project description and objectives</li>\n              <li>• Detailed workflow with phases and milestones</li>\n              <li>• Complete task breakdown with priorities and estimates</li>\n              <li>• Kanban board with organized task categories</li>\n              <li>• Project timeline and resource recommendations</li>\n            </ul>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex items-center justify-end gap-3 pt-4 border-t border-border\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={handleClose}\n              disabled={isLoading}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              variant=\"default\"\n              disabled={isLoading || !formData.name.trim()}\n              iconName={isLoading ? \"Loader2\" : \"Sparkles\"}\n              iconPosition=\"left\"\n              className={`${isLoading ? \"animate-spin\" : \"\"} bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 border-0`}\n            >\n              {isLoading ? 'Generating Project...' : 'Create AI Project'}\n            </Button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default CreateAIProjectModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,iBAAiB;EAAEC,cAAc;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACzG,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,SAAS;IACtBC,QAAQ,EAAE,CAAC;IACXC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAM+B,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,WAAW,EAAE,yCAAyC;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC5I;IAAEJ,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,WAAW,EAAE,oCAAoC;IAAEC,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC9I;IAAEJ,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE,YAAY;IAAEC,WAAW,EAAE,gCAAgC;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAe,CAAC,EACtI;IAAEJ,KAAK,EAAE,oBAAoB;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,WAAW,EAAE,4CAA4C;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACtK;IAAEJ,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,WAAW,EAAE,mCAAmC;IAAEC,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACjJ;IAAEJ,KAAK,EAAE,uBAAuB;IAAEC,KAAK,EAAE,uBAAuB;IAAEC,WAAW,EAAE,+CAA+C;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACxK;IAAEJ,KAAK,EAAE,sBAAsB;IAAEC,KAAK,EAAE,wBAAwB;IAAEC,WAAW,EAAE,4CAA4C;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAc,CAAC,EACvK;IAAEJ,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,WAAW,EAAE,4CAA4C;IAAEC,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC3J;IAAEJ,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,WAAW,EAAE,oCAAoC;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC9I;IAAEJ,KAAK,EAAE,oBAAoB;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,WAAW,EAAE,4CAA4C;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAa,CAAC,CAChK;EAED,MAAMC,eAAe,GAAG,CACtB;IAAEL,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAwB,CAAC,EAC5C;IAAED,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAwB,CAAC,EAC5C;IAAED,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAA0B,CAAC,EAC9C;IAAED,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAwB,CAAC,EAC5C;IAAED,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAA+B,CAAC,CACrD;EAED,MAAMK,iBAAiB,GAAG,CACxB;IAAEN,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAqB,CAAC,EAChD;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAA2B,CAAC,EAC5D;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAoB,CAAC,EAC/C;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAqB,CAAC,CACjD;EAED,MAAMM,iBAAiB,GAAGA,CAACC,KAAK,EAAER,KAAK,KAAK;IAC1ChB,WAAW,CAACyB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACD,KAAK,GAAGR;IACX,CAAC,CAAC,CAAC;IACH;IACA,IAAIT,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMkB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC3B,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACzBnB,QAAQ,CAAC,sCAAsC,CAAC;MAChD;IACF;IAEAM,sBAAsB,CAAC,IAAI,CAAC;IAC5B,IAAI;MACF;MACA,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,YAAY,GAAGhB,YAAY,CAACiB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACjB,KAAK,KAAKjB,QAAQ,CAACG,WAAW,CAAC;MAEnFU,iBAAiB,CAAC;QAChBX,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBgC,IAAI,EAAEF,YAAY;QAClBG,iBAAiB,EAAEC,oBAAoB,CAACpC,QAAQ,CAACG,WAAW,EAAEH,QAAQ,CAACI,QAAQ,EAAEJ,QAAQ,CAACK,cAAc,CAAC;QACzGgC,cAAc,EAAEC,qBAAqB,CAACtC,QAAQ,CAACG,WAAW,CAAC;QAC3DoC,MAAM,EAAEC,gBAAgB,CAACxC,QAAQ,CAACG,WAAW,CAAC;QAC9CsC,mBAAmB,EAAEC,sBAAsB,CAAC1C,QAAQ,CAACI,QAAQ,EAAEJ,QAAQ,CAACK,cAAc,CAAC;QACvFsC,YAAY,EAAEC,eAAe,CAAC5C,QAAQ,CAACG,WAAW;MACpD,CAAC,CAAC;MAEFQ,cAAc,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOkC,GAAG,EAAE;MACZpC,QAAQ,CAAC,4BAA4B,CAAC;IACxC,CAAC,SAAS;MACRM,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMqB,oBAAoB,GAAGA,CAACF,IAAI,EAAE9B,QAAQ,EAAE0C,UAAU,KAAK;IAC3D,MAAMC,aAAa,GAAG;MACpB,iBAAiB,EAAE,EAAE;MACrB,YAAY,EAAE,EAAE;MAChB,oBAAoB,EAAE,EAAE;MACxB,kBAAkB,EAAE,EAAE;MACtB,uBAAuB,EAAE,EAAE;MAC3B,sBAAsB,EAAE,EAAE;MAC1B,kBAAkB,EAAE,EAAE;MACtB,eAAe,EAAE,EAAE;MACnB,oBAAoB,EAAE,EAAE;MACxB,SAAS,EAAE;IACb,CAAC;IAED,IAAIC,QAAQ,GAAGD,aAAa,CAACb,IAAI,CAAC,IAAI,EAAE;;IAExC;IACA,MAAMe,qBAAqB,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,YAAY,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC;IAC1FL,QAAQ,IAAIC,qBAAqB,CAACH,UAAU,CAAC,IAAI,GAAG;;IAEpD;IACA,IAAI1C,QAAQ,IAAI,CAAC,EAAE4C,QAAQ,IAAI,GAAG,CAAC,KAC9B,IAAI5C,QAAQ,IAAI,CAAC,EAAE4C,QAAQ,IAAI,GAAG;IAEvC,OAAOM,IAAI,CAACC,KAAK,CAACP,QAAQ,CAAC;EAC7B,CAAC;EAED,MAAMV,qBAAqB,GAAIJ,IAAI,IAAK;IACtC,MAAMsB,UAAU,GAAG;MACjB,iBAAiB,EAAE,EAAE;MACrB,YAAY,EAAE,EAAE;MAChB,oBAAoB,EAAE,EAAE;MACxB,kBAAkB,EAAE,EAAE;MACtB,uBAAuB,EAAE,EAAE;MAC3B,sBAAsB,EAAE,EAAE;MAC1B,kBAAkB,EAAE,EAAE;MACtB,eAAe,EAAE,EAAE;MACnB,oBAAoB,EAAE,EAAE;MACxB,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,UAAU,CAACtB,IAAI,CAAC,IAAI,EAAE;EAC/B,CAAC;EAED,MAAMM,gBAAgB,GAAIN,IAAI,IAAK;IACjC,MAAMK,MAAM,GAAG;MACb,iBAAiB,EAAE,CAAC,qBAAqB,EAAE,uBAAuB,EAAE,aAAa,EAAE,cAAc,EAAE,qBAAqB,CAAC;MACzH,oBAAoB,EAAE,CAAC,4BAA4B,EAAE,2BAA2B,EAAE,gCAAgC,EAAE,kCAAkC,EAAE,kBAAkB,CAAC;MAC3K,kBAAkB,EAAE,CAAC,sCAAsC,EAAE,8BAA8B,EAAE,+BAA+B,EAAE,gCAAgC,EAAE,yBAAyB;IAC3L,CAAC;IACD,OAAOA,MAAM,CAACL,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,oBAAoB,CAAC;EAC3I,CAAC;EAED,MAAMQ,sBAAsB,GAAGA,CAACe,IAAI,EAAEX,UAAU,KAAK;IACnD,MAAMY,eAAe,GAAG,EAAE;IAC1B,IAAID,IAAI,IAAI,CAAC,EAAEC,eAAe,CAACC,IAAI,CAAC,sDAAsD,CAAC;IAC3F,IAAIF,IAAI,IAAI,CAAC,EAAEC,eAAe,CAACC,IAAI,CAAC,8DAA8D,CAAC;IACnG,IAAIb,UAAU,KAAK,QAAQ,EAAEY,eAAe,CAACC,IAAI,CAAC,mCAAmC,CAAC;IACtF,IAAIb,UAAU,KAAK,QAAQ,EAAEY,eAAe,CAACC,IAAI,CAAC,sDAAsD,CAAC;IACzG,OAAOD,eAAe;EACxB,CAAC;EAED,MAAMd,eAAe,GAAIV,IAAI,IAAK;IAChC,MAAM0B,IAAI,GAAG;MACX,iBAAiB,EAAE,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,CAAC;MAC1F,oBAAoB,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,YAAY,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,CAAC;MAC5G,kBAAkB,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS;IACzG,CAAC;IACD,OAAOA,IAAI,CAAC1B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,UAAU,EAAE,gBAAgB,CAAC;EAC/F,CAAC;EAED,MAAM2B,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAC/D,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACzBnB,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;IAEAF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMuD,WAAW,GAAG;QAClB9D,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAC;QAC1BqC,YAAY,EAAEjE,QAAQ,CAACG,WAAW;QAClC+D,eAAe,EAAErE;MACnB,CAAC;MAED,MAAMD,iBAAiB,CAACoE,WAAW,CAAC;;MAEpC;MACA/D,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE;MACf,CAAC,CAAC;MACFR,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOkD,GAAG,EAAE;MACZpC,QAAQ,CAACoC,GAAG,CAACsB,OAAO,IAAI,6BAA6B,CAAC;IACxD,CAAC,SAAS;MACR5D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM6D,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAC9D,SAAS,EAAE;MACdL,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE;MACf,CAAC,CAAC;MACFM,QAAQ,CAAC,EAAE,CAAC;MACZd,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAK6E,SAAS,EAAC,gFAAgF;IAAAC,QAAA,eAC7F9E,OAAA;MAAK6E,SAAS,EAAC,4EAA4E;MAAAC,QAAA,gBAEzF9E,OAAA;QAAK6E,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3E9E,OAAA;UAAK6E,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC9E,OAAA;YAAK6E,SAAS,EAAC,oGAAoG;YAAAC,QAAA,eACjH9E,OAAA,CAACF,IAAI;cAACY,IAAI,EAAC,UAAU;cAACuD,IAAI,EAAE,EAAG;cAACY,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNlF,OAAA;YAAA8E,QAAA,gBACE9E,OAAA;cAAI6E,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ElF,OAAA;cAAG6E,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlF,OAAA,CAACL,MAAM;UACLwF,OAAO,EAAC,OAAO;UACflB,IAAI,EAAC,IAAI;UACTmB,OAAO,EAAER,WAAY;UACrBS,QAAQ,EAAEvE,SAAU;UACpBwE,QAAQ,EAAC,GAAG;UACZT,SAAS,EAAC;QAA6C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNlF,OAAA;QAAMuF,QAAQ,EAAElB,YAAa;QAACQ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAErD9E,OAAA;UAAK6E,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eACtC9E,OAAA;YAAK6E,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClE9E,OAAA,CAACF,IAAI;cAACY,IAAI,EAAC,WAAW;cAACuD,IAAI,EAAE;YAAG;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnClF,OAAA;cAAA8E,QAAA,GAAM,eAAa,eAAA9E,OAAA;gBAAQ6E,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAExE;cAAgB;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLlE,KAAK,iBACJhB,OAAA;UAAK6E,SAAS,EAAC,gDAAgD;UAAAC,QAAA,eAC7D9E,OAAA;YAAK6E,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBACnD9E,OAAA,CAACF,IAAI;cAACY,IAAI,EAAC,aAAa;cAACuD,IAAI,EAAE;YAAG;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrClF,OAAA;cAAM6E,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE9D;YAAK;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDlF,OAAA;UAAK6E,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9E,OAAA;YAAO6E,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlF,OAAA,CAACJ,KAAK;YACJ8C,IAAI,EAAC,MAAM;YACXjB,KAAK,EAAEjB,QAAQ,CAACE,IAAK;YACrB8E,QAAQ,EAAGlB,CAAC,IAAKtC,iBAAiB,CAAC,MAAM,EAAEsC,CAAC,CAACmB,MAAM,CAAChE,KAAK,CAAE;YAC3DiE,WAAW,EAAC,4BAA4B;YACxCL,QAAQ,EAAEvE,SAAU;YACpB+D,SAAS,EAAC,QAAQ;YAClBc,QAAQ;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFlF,OAAA;YAAG6E,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAE3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9E,OAAA;YAAO6E,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAE/D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRlF,OAAA,CAACH,MAAM;YACL4B,KAAK,EAAEjB,QAAQ,CAACG,WAAY;YAC5B6E,QAAQ,EAAG/D,KAAK,IAAKO,iBAAiB,CAAC,aAAa,EAAEP,KAAK,CAAE;YAC7DmE,OAAO,EAAEpE,YAAa;YACtB6D,QAAQ,EAAEvE,SAAU;YACpB+D,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACFlF,OAAA;YAAG6E,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAE3C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,oFAAoF;UAAAC,QAAA,gBACjG9E,OAAA;YAAI6E,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAC9E9E,OAAA,CAACF,IAAI;cAACY,IAAI,EAAC,UAAU;cAACuD,IAAI,EAAE;YAAG;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEpC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlF,OAAA;YAAI6E,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC/C9E,OAAA;cAAA8E,QAAA,EAAI;YAAkD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DlF,OAAA;cAAA8E,QAAA,EAAI;YAA8C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvDlF,OAAA;cAAA8E,QAAA,EAAI;YAAuD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChElF,OAAA;cAAA8E,QAAA,EAAI;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtDlF,OAAA;cAAA8E,QAAA,EAAI;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,iEAAiE;UAAAC,QAAA,gBAC9E9E,OAAA,CAACL,MAAM;YACL+C,IAAI,EAAC,QAAQ;YACbyC,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAER,WAAY;YACrBS,QAAQ,EAAEvE,SAAU;YAAAgE,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlF,OAAA,CAACL,MAAM;YACL+C,IAAI,EAAC,QAAQ;YACbyC,OAAO,EAAC,SAAS;YACjBE,QAAQ,EAAEvE,SAAS,IAAI,CAACN,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAE;YAC7CkD,QAAQ,EAAExE,SAAS,GAAG,SAAS,GAAG,UAAW;YAC7C+E,YAAY,EAAC,MAAM;YACnBhB,SAAS,EAAE,GAAG/D,SAAS,GAAG,cAAc,GAAG,EAAE,gGAAiG;YAAAgE,QAAA,EAE7IhE,SAAS,GAAG,uBAAuB,GAAG;UAAmB;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3E,EAAA,CA7TIN,oBAAoB;AAAA6F,EAAA,GAApB7F,oBAAoB;AA+T1B,eAAeA,oBAAoB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}