import React, { useState, useEffect } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import authService from '../../utils/authService';
import RoleBasedHeader from '../../components/ui/RoleBasedHeader';
import Breadcrumb from '../../components/ui/Breadcrumb';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import BoardHeader from './components/BoardHeader';
import BoardColumn from './components/BoardColumn';

import AddCardModal from './components/AddCardModal';
import AddColumnModal from './components/AddColumnModal';
import InviteMemberModal from './components/InviteMemberModal';

const KanbanBoard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // User state and role
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState('member');

  // Project context - get from location state or default
  const [currentProject] = useState(() => {
    const locationState = window.location.state;
    return locationState?.projectId ? {
      id: locationState.projectId,
      name: 'Current Project',
      memberRole: 'assigned' // This would come from API
    } : {
      id: 1,
      name: 'Website Redesign',
      memberRole: 'assigned' // assigned, not-assigned
    };
  });

  // Check if current user is assigned to this project
  const isUserAssignedToProject = () => {
    // For members, check if they're assigned to this specific project
    if (userRole === 'member') {
      return currentProject.memberRole === 'assigned';
    }
    // Admins and owners have access to all projects
    return ['admin', 'owner'].includes(userRole);
  };

  // Mock data
  const [board] = useState({
    id: 'board-1',
    title: 'Project Management Board',
    description: 'Main project tracking board for Q4 initiatives',
    isPrivate: false,
    createdAt: '2025-01-15T10:00:00Z',
    updatedAt: '2025-01-28T05:54:23Z'
  });

  // Real members data - will be loaded from team service
  const [members, setMembers] = useState([]);

  const [columns, setColumns] = useState([]);

  const [cards, setCards] = useState([
    {
      id: 'card-1',
      columnId: 'col-1',
      title: 'Design user authentication flow',
      description: 'Create wireframes and mockups for the login and registration process',
      priority: 'high',
      assignedTo: ['user-1', 'user-2'],
      dueDate: '2025-02-05',
      labels: [
        { id: 'design', name: 'Design', color: '#3b82f6' },
        { id: 'ux', name: 'UX', color: '#8b5cf6' }
      ],
      checklist: [
        { id: 'check-1', text: 'Research competitor flows', completed: true },
        { id: 'check-2', text: 'Create wireframes', completed: false },
        { id: 'check-3', text: 'Design mockups', completed: false }
      ],
      comments: [
        {
          id: 'comment-1',
          author: 'user-2',
          content: 'Should we include social login options?',
          createdAt: '2025-01-27T14:30:00Z'
        }
      ],
      attachments: [],
      createdAt: '2025-01-25T09:00:00Z',
      updatedAt: '2025-01-27T14:30:00Z'
    },
    {
      id: 'card-2',
      columnId: 'col-1',
      title: 'Set up project repository',
      description: 'Initialize Git repository with proper folder structure and documentation',
      priority: 'medium',
      assignedTo: ['user-3'],
      dueDate: '2025-01-30',
      labels: [
        { id: 'development', name: 'Development', color: '#10b981' }
      ],
      checklist: [],
      comments: [],
      attachments: [],
      createdAt: '2025-01-26T11:00:00Z',
      updatedAt: '2025-01-26T11:00:00Z'
    },
    {
      id: 'card-3',
      columnId: 'col-2',
      title: 'Implement user registration API',
      description: 'Build backend endpoints for user registration with validation and email verification',
      priority: 'high',
      assignedTo: ['user-2', 'user-5'],
      dueDate: '2025-02-10',
      labels: [
        { id: 'backend', name: 'Backend', color: '#f59e0b' },
        { id: 'api', name: 'API', color: '#ef4444' }
      ],
      checklist: [
        { id: 'check-4', text: 'Design database schema', completed: true },
        { id: 'check-5', text: 'Implement validation', completed: true },
        { id: 'check-6', text: 'Add email verification', completed: false },
        { id: 'check-7', text: 'Write unit tests', completed: false }
      ],
      comments: [
        {
          id: 'comment-2',
          author: 'user-1',
          content: 'Make sure to include proper error handling',
          createdAt: '2025-01-26T16:45:00Z'
        },
        {
          id: 'comment-3',
          author: 'user-5',
          content: 'Working on the email service integration',
          createdAt: '2025-01-27T10:15:00Z'
        }
      ],
      attachments: [
        { id: 'att-1', name: 'api-spec.pdf', size: '2.4 MB' }
      ],
      createdAt: '2025-01-24T13:00:00Z',
      updatedAt: '2025-01-27T10:15:00Z'
    },
    {
      id: 'card-4',
      columnId: 'col-3',
      title: 'Review dashboard components',
      description: 'Code review for the new dashboard UI components',
      priority: 'medium',
      assignedTo: ['user-1', 'user-4'],
      dueDate: '2025-01-29',
      labels: [
        { id: 'review', name: 'Review', color: '#8b5cf6' },
        { id: 'frontend', name: 'Frontend', color: '#06b6d4' }
      ],
      checklist: [
        { id: 'check-8', text: 'Check code quality', completed: true },
        { id: 'check-9', text: 'Test responsiveness', completed: false },
        { id: 'check-10', text: 'Verify accessibility', completed: false }
      ],
      comments: [],
      attachments: [],
      createdAt: '2025-01-23T15:30:00Z',
      updatedAt: '2025-01-27T09:20:00Z'
    },
    {
      id: 'card-5',
      columnId: 'col-4',
      title: 'Update project documentation',
      description: 'Refresh README and API documentation with latest changes',
      priority: 'low',
      assignedTo: ['user-3'],
      dueDate: null,
      labels: [
        { id: 'documentation', name: 'Documentation', color: '#f59e0b' }
      ],
      checklist: [
        { id: 'check-11', text: 'Update README', completed: true },
        { id: 'check-12', text: 'Update API docs', completed: true },
        { id: 'check-13', text: 'Add deployment guide', completed: true }
      ],
      comments: [
        {
          id: 'comment-4',
          author: 'user-1',
          content: 'Great work on the documentation!',
          createdAt: '2025-01-25T12:00:00Z'
        }
      ],
      attachments: [],
      createdAt: '2025-01-20T10:00:00Z',
      updatedAt: '2025-01-25T12:00:00Z'
    }
  ]);

  // Modal states
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [showAddColumnModal, setShowAddColumnModal] = useState(false);
  const [showInviteMemberModal, setShowInviteMemberModal] = useState(false);
  const [selectedColumnId, setSelectedColumnId] = useState(null);

  // Filter and search states
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilters, setActiveFilters] = useState({});

  // Load user data and role
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const userResult = await authService.getCurrentUser();
        if (userResult.data.user) {
          setCurrentUser(userResult.data.user);
          setUserRole(userResult.data.user.role || 'member');
        }
      } catch (error) {
        console.error('Failed to load user data:', error);
      }
    };

    loadUserData();
  }, []);

  // Load boards and columns for current project
  useEffect(() => {
    const loadBoardsAndColumns = async () => {
      try {
        // Get current project ID from localStorage or URL
        const currentProjectId = localStorage.getItem('currentProjectId');
        if (!currentProjectId) {
          console.log('No current project selected');
          return;
        }

        console.log('Loading boards for project:', currentProjectId);
        const apiService = (await import('../../utils/realApiService')).default;

        // Get boards for the project
        const boards = await apiService.boards.getByProject(currentProjectId);
        console.log('Loaded boards:', boards);

        if (boards && boards.length > 0) {
          const board = boards[0]; // Use first board

          // Get columns for the board
          const columns = await apiService.columns.getByBoard(board.id);
          console.log('Loaded columns:', columns);

          if (columns && columns.length > 0) {
            setColumns(columns.sort((a, b) => a.order - b.order));
          } else {
            // Set default columns if none exist
            setColumns([
              { id: 'col-1', title: 'To Do', status: 'todo', order: 1 },
              { id: 'col-2', title: 'In Progress', status: 'in-progress', order: 2 },
              { id: 'col-3', title: 'Review', status: 'review', order: 3 },
              { id: 'col-4', title: 'Done', status: 'done', order: 4 }
            ]);
          }
        }
      } catch (error) {
        console.error('Failed to load boards and columns:', error);
        // Set default columns on error
        setColumns([
          { id: 'col-1', title: 'To Do', status: 'todo', order: 1 },
          { id: 'col-2', title: 'In Progress', status: 'in-progress', order: 2 },
          { id: 'col-3', title: 'Review', status: 'review', order: 3 },
          { id: 'col-4', title: 'Done', status: 'done', order: 4 }
        ]);
      }
    };

    loadBoardsAndColumns();
  }, []);

  // Role-based and project-based permission checks
  const canCreateCards = () => {
    if (userRole === 'viewer') return false;
    return isUserAssignedToProject();
  };

  const canEditCards = () => {
    if (userRole === 'viewer') return false;
    return isUserAssignedToProject();
  };

  const canDeleteCards = () => {
    if (userRole === 'viewer') return false;
    return isUserAssignedToProject();
  };

  const canCreateColumns = () => {
    if (userRole === 'viewer') return false;
    return isUserAssignedToProject();
  };

  const canEditColumns = () => {
    if (userRole === 'viewer') return false;
    return isUserAssignedToProject();
  };

  const canDeleteColumns = () => {
    if (userRole === 'viewer') return false;
    return isUserAssignedToProject();
  };

  const canInviteMembers = () => {
    return ['admin', 'owner'].includes(userRole);
  };

  const canDragCards = () => {
    if (userRole === 'viewer') return false;
    return isUserAssignedToProject();
  };

  // Load cards from API when columns are loaded
  useEffect(() => {
    const loadCards = async () => {
      if (columns.length === 0) return;

      try {
        const apiService = (await import('../../utils/realApiService')).default;
        let allCards = [];

        // Load cards for each column
        for (const column of columns) {
          try {
            const result = await apiService.cards.getAll(column.id);
            console.log(`Loading cards for column ${column.id}:`, result);
            if (result.data && Array.isArray(result.data)) {
              // Transform backend data to frontend format
              const transformedCards = result.data.map(card => ({
                id: card.id,
                columnId: card.column_id,
                title: card.title,
                description: card.description || '',
                priority: card.priority || 'medium',
                assignedTo: card.assigned_to || [],
                dueDate: card.due_date,
                labels: card.labels || [],
                createdAt: card.created_at,
                updatedAt: card.updated_at,
                checklist: card.checklist || [],
                comments: card.comments || [],
                attachments: card.attachments || []
              }));
              allCards = [...allCards, ...transformedCards];
            }
          } catch (columnError) {
            console.error(`Error loading cards for column ${column.id}:`, columnError);
          }
        }

        console.log('All loaded cards:', allCards);
        setCards(allCards);
      } catch (error) {
        console.error('Error loading cards from API:', error);
        // Keep existing mock data if API fails
      }
    };

    loadCards();
  }, [columns]);

  // Filter cards based on search and filters
  const filteredCards = cards.filter(card => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesSearch = 
        card.title.toLowerCase().includes(query) ||
        card.description.toLowerCase().includes(query) ||
        card.labels?.some(label => label.name.toLowerCase().includes(query));
      
      if (!matchesSearch) return false;
    }

    // Priority filter
    if (activeFilters.priority?.length > 0) {
      if (!activeFilters.priority.includes(card.priority)) return false;
    }

    // Assignee filter
    if (activeFilters.assignee?.length > 0) {
      const hasAssignee = card.assignedTo?.some(assigneeId => 
        activeFilters.assignee.includes(assigneeId)
      );
      if (!hasAssignee) return false;
    }

    // Due date filter
    if (activeFilters.dueDate?.length > 0) {
      const today = new Date();
      const cardDueDate = card.dueDate ? new Date(card.dueDate) : null;

      const matchesDueDate = activeFilters.dueDate.some(filter => {
        if (filter === 'overdue') {
          return cardDueDate && cardDueDate < today;
        }
        if (filter === 'today') {
          return cardDueDate && cardDueDate.toDateString() === today.toDateString();
        }
        if (filter === 'this-week') {
          const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
          return cardDueDate && cardDueDate >= today && cardDueDate <= weekFromNow;
        }
        if (filter === 'this-month') {
          const monthFromNow = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
          return cardDueDate && cardDueDate >= today && cardDueDate <= monthFromNow;
        }
        if (filter === 'custom' && activeFilters.customDateRange) {
          const startDate = new Date(activeFilters.customDateRange.start);
          const endDate = new Date(activeFilters.customDateRange.end);
          return cardDueDate && cardDueDate >= startDate && cardDueDate <= endDate;
        }
        return false;
      });

      if (!matchesDueDate) return false;
    }

    return true;
  });

  // Handle card movement between columns
  const handleCardMove = async (cardId, sourceColumnId, targetColumnId) => {
    // Check if user can drag cards
    if (!canDragCards()) {
      if (userRole === 'viewer') {
        console.log('Viewers cannot move cards');
      } else {
        console.log('You can only move cards in projects you are assigned to');
      }
      return;
    }

    try {
      // Update card via API
      const apiService = (await import('../../utils/realApiService')).default;
      const updatedCard = { columnId: targetColumnId, updatedAt: new Date().toISOString() };
      await apiService.cards.update(cardId, updatedCard);

      // Update local state
      setCards(prevCards =>
        prevCards.map(card =>
          card.id === cardId
            ? { ...card, columnId: targetColumnId, updatedAt: new Date().toISOString() }
            : card
        )
      );
    } catch (error) {
      console.error('Failed to move card:', error);
      // Fallback to local state only
      setCards(prevCards =>
        prevCards.map(card =>
          card.id === cardId
            ? { ...card, columnId: targetColumnId, updatedAt: new Date().toISOString() }
            : card
        )
      );
    }
  };

  // Handle card click - navigate to card details
  const handleCardClick = (card) => {
    console.log('Navigating to card details:', card);

    // Navigate with card data in state and URL params
    navigate(`/card-details?id=${card.id}`, {
      state: {
        card: card,
        members: members,
        returnPath: '/kanban-board'
      }
    });
  };

  // Handle adding new card
  const handleAddCard = (columnId) => {
    if (!canCreateCards()) {
      if (userRole === 'viewer') {
        console.log('Viewers cannot create cards');
      } else {
        console.log('You can only create cards in projects you are assigned to');
      }
      return;
    }
    setSelectedColumnId(columnId);
    setShowAddCardModal(true);
  };

  const handleSaveCard = async (newCard) => {
    try {
      // Create card via API
      const apiService = (await import('../../utils/realApiService')).default;
      const result = await apiService.cards.create(newCard);
      console.log('Card created via API:', result);

      // Add to local state
      const createdCard = result.data || newCard;
      setCards(prevCards => [...prevCards, createdCard]);

      // Send notifications for task assignments
      if (createdCard.assignedTo && createdCard.assignedTo.length > 0) {
        try {
          const notificationService = (await import('../../utils/notificationService')).default;

          for (const assigneeId of createdCard.assignedTo) {
            if (assigneeId !== currentUser?.id) { // Don't notify self
              await notificationService.notifyTaskAssigned(
                createdCard,
                assigneeId,
                currentUser?.id
              );
            }
          }
        } catch (notificationError) {
          console.error('Failed to send task assignment notifications:', notificationError);
        }
      }

      console.log('Card saved:', newCard);
    } catch (error) {
      console.error('Failed to create card:', error);
      // Fallback to local state only
      setCards(prevCards => [...prevCards, newCard]);
    }
  };

  // Handle adding new column
  const handleSaveColumn = (newColumn) => {
    if (!canCreateColumns()) {
      if (userRole === 'viewer') {
        console.log('Viewers cannot create columns');
      } else {
        console.log('You can only create columns in projects you are assigned to');
      }
      return;
    }
    setColumns(prevColumns => [...prevColumns, newColumn]);
  };

  // Handle column operations
  const handleEditColumn = (columnId, updates) => {
    if (!canEditColumns()) {
      if (userRole === 'viewer') {
        console.log('Viewers cannot edit columns');
      } else {
        console.log('You can only edit columns in projects you are assigned to');
      }
      return;
    }
    setColumns(prevColumns =>
      prevColumns.map(col =>
        col.id === columnId
          ? { ...col, ...updates, updatedAt: new Date().toISOString() }
          : col
      )
    );
  };

  const handleDeleteColumn = (columnId) => {
    if (!canDeleteColumns()) {
      if (userRole === 'viewer') {
        console.log('Viewers cannot delete columns');
      } else {
        console.log('You can only delete columns in projects you are assigned to');
      }
      return;
    }
    // Move cards from deleted column to first column
    const firstColumnId = columns[0]?.id;
    if (firstColumnId && firstColumnId !== columnId) {
      setCards(prevCards =>
        prevCards.map(card =>
          card.columnId === columnId
            ? { ...card, columnId: firstColumnId }
            : card
        )
      );
    }
    
    setColumns(prevColumns => prevColumns.filter(col => col.id !== columnId));
  };

  // Handle member invitation
  const handleMemberInvite = () => {
    if (!canInviteMembers()) {
      console.log('Only admins and owners can invite members');
      return;
    }
    setShowInviteMemberModal(true);
  };

  const handleSendInvitation = (invitation) => {
    console.log('Invitation sent:', invitation);
    // In real app, this would send the invitation via API
  };

  // Handle board updates
  const handleBoardUpdate = (updates) => {
    console.log('Board updated:', updates);
    // In real app, this would update the board via API
  };

  // Get cards for a specific column
  const getCardsForColumn = (columnId) => {
    return filteredCards.filter(card => card.columnId === columnId);
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="min-h-screen bg-background">
        <RoleBasedHeader
          userRole={userRole.toLowerCase()}
          currentUser={currentUser ? {
            name: `${currentUser.firstName} ${currentUser.lastName}`,
            email: currentUser.email,
            avatar: currentUser.avatar || '/assets/images/avatar.jpg',
            role: userRole
          } : {
            name: 'Loading...',
            email: '',
            avatar: '/assets/images/avatar.jpg',
            role: userRole
          }}
        />

        <main className="pt-16">
          <div className="max-w-full px-4 sm:px-6 lg:px-8 py-8">
            <Breadcrumb />

            {/* Page Header */}
            <div className="mb-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                  <Icon name="Kanban" size={20} className="text-primary-foreground" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-foreground">Projects</h1>
                  <p className="text-muted-foreground">
                    Manage tasks and track project progress
                  </p>
                </div>
              </div>
            </div>

            {/* Board Header */}
            <BoardHeader
              board={board}
              members={members}
              onBoardUpdate={handleBoardUpdate}
              onMemberInvite={handleMemberInvite}
              onFilterChange={setActiveFilters}
              onSearchChange={setSearchQuery}
              searchQuery={searchQuery}
              activeFilters={activeFilters}
              canInviteMembers={canInviteMembers()}
            />

        {/* Board Content */}
        <div className="flex-1 p-6">
          <div className="flex space-x-6 overflow-x-auto pb-6">
            {/* Columns */}
            {columns
              .sort((a, b) => a.order - b.order)
              .map(column => (
                <BoardColumn
                  key={column.id}
                  column={column}
                  cards={getCardsForColumn(column.id)}
                  onCardMove={handleCardMove}
                  onCardClick={handleCardClick}
                  onAddCard={handleAddCard}
                  onEditColumn={handleEditColumn}
                  onDeleteColumn={handleDeleteColumn}
                  members={members}
                  canCreateCards={canCreateCards()}
                  canEditColumns={canEditColumns()}
                  canDeleteColumns={canDeleteColumns()}
                  canDragCards={canDragCards()}
                />
              ))}

            {/* Add Column Button - Only show for non-viewers */}
            {canCreateColumns() && (
              <div className="flex-shrink-0">
                <Button
                  variant="outline"
                  onClick={() => setShowAddColumnModal(true)}
                  className="w-80 h-32 border-2 border-dashed border-border hover:border-primary hover:bg-primary/5 transition-colors"
                  iconName="Plus"
                  iconPosition="left"
                >
                  Add Column
                </Button>
              </div>
            )}
          </div>

          {/* Empty State */}
          {filteredCards.length === 0 && searchQuery && (
            <div className="flex flex-col items-center justify-center py-12">
              <Icon name="Search" size={48} className="text-text-secondary mb-4" />
              <h3 className="text-lg font-medium text-text-primary mb-2">No cards found</h3>
              <p className="text-text-secondary text-center max-w-md">
                No cards match your search criteria. Try adjusting your search terms or filters.
              </p>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery('');
                  setActiveFilters({});
                }}
                className="mt-4"
              >
                Clear Search & Filters
              </Button>
            </div>
          )}
        </div>

        {/* Modals */}
        <AddCardModal
          isOpen={showAddCardModal}
          onClose={() => {
            setShowAddCardModal(false);
            setSelectedColumnId(null);
          }}
          onSave={handleSaveCard}
          columnId={selectedColumnId}
          members={members}
        />

        <AddColumnModal
          isOpen={showAddColumnModal}
          onClose={() => setShowAddColumnModal(false)}
          onSave={handleSaveColumn}
        />

        <InviteMemberModal
          isOpen={showInviteMemberModal}
          onClose={() => setShowInviteMemberModal(false)}
          onInvite={handleSendInvitation}
        />
          </div>
        </main>
      </div>
    </DndProvider>
  );
};

export default KanbanBoard;