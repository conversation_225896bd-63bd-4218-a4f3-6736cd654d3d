#!/usr/bin/env python3
"""
Enhanced FastAPI server with RBAC, email notifications, and organization management
"""
import sys
import os
import time
import uuid
from typing import Optional, List
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, HTTPException, Header, BackgroundTasks, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr, ValidationError
from app.config import settings
from app.core.security import hash_password, verify_password, create_access_token, verify_token
from app.services.email_service import email_service

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Enhanced Agno WorkSphere API with RBAC and email notifications"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add validation error handler
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    print(f"❌ Validation error on {request.method} {request.url}")
    print(f"📋 Validation errors: {exc.errors()}")

    # Get request body for debugging
    try:
        body = await request.body()
        print(f"📄 Request body: {body.decode()}")
    except:
        print("📄 Could not read request body")

    return JSONResponse(
        status_code=422,
        content={
            "detail": exc.errors(),
            "message": "Validation failed",
            "body": body.decode() if body else None
        }
    )

# In-memory storage for testing (replace with database in production)
users_db = {}
organizations_db = {}
organization_members_db = {}
projects_db = {}
project_access_db = {}  # Stores project access permissions for admins
admin_permissions_db = {}  # Stores special permissions for admins (like project creation)
invitations_db = {}

# Initialize demo data
def initialize_demo_data():
    """Initialize demo users and organizations for testing"""
    import time

    # Demo organization
    demo_org_id = "demo-org-1"
    demo_org = {
        "id": demo_org_id,
        "name": "ACME Corporation",
        "description": "Demo organization for testing different roles",
        "domain": "acme.com",
        "created_by": "demo-owner",
        "created_at": time.time()
    }
    organizations_db[demo_org_id] = demo_org

    # Demo users with different roles
    demo_users = [
        {
            "email": "<EMAIL>",
            "password": "Owner123!",
            "first_name": "John",
            "last_name": "Owner",
            "role": "owner"
        },
        {
            "email": "<EMAIL>",
            "password": "Admin123!",
            "first_name": "Jane",
            "last_name": "Admin",
            "role": "admin"
        },
        {
            "email": "<EMAIL>",
            "password": "Member123!",
            "first_name": "Bob",
            "last_name": "Member",
            "role": "member"
        },
        {
            "email": "<EMAIL>",
            "password": "Viewer123!",
            "first_name": "Alice",
            "last_name": "Viewer",
            "role": "viewer"
        },
        # Alternative demo users with @example.com domain
        {
            "email": "<EMAIL>",
            "password": "Owner123!",
            "first_name": "John",
            "last_name": "Owner",
            "role": "owner"
        },
        {
            "email": "<EMAIL>",
            "password": "Admin123!",
            "first_name": "Jane",
            "last_name": "Admin",
            "role": "admin"
        }
    ]

    for i, user_data in enumerate(demo_users):
        user_id = f"demo-user-{i+1}"
        hashed_password = hash_password(user_data["password"])

        user = {
            "id": user_id,
            "email": user_data["email"],
            "password_hash": hashed_password,
            "first_name": user_data["first_name"],
            "last_name": user_data["last_name"],
            "email_verified": True,
            "created_at": time.time()
        }

        users_db[user_data["email"]] = user

        # Add user to demo organization
        member_id = f"demo-member-{i+1}"
        organization_members_db[member_id] = {
            "id": member_id,
            "user_id": user_id,
            "organization_id": demo_org_id,
            "role": user_data["role"],
            "joined_at": time.time()
        }

    print("✅ Demo data initialized with users:")
    for user_data in demo_users:
        print(f"   - {user_data['email']} (Role: {user_data['role']})")

# Initialize demo data on startup (only if not already initialized)
if not users_db:
    initialize_demo_data()

# Pydantic models
class UserRegister(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: Optional[str] = ""
    organization_name: Optional[str] = None
    organization_slug: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int = 900

class UserResponse(BaseModel):
    id: str
    email: str
    first_name: str
    last_name: str
    email_verified: bool = True
    created_at: float
    role: Optional[str] = None

class OrganizationResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    domain: Optional[str] = None
    created_at: float
    member_count: int = 1

class OrganizationMemberResponse(BaseModel):
    id: str
    user_id: str
    organization_id: str
    role: str
    joined_at: float
    user: UserResponse

class AuthResponse(BaseModel):
    user: UserResponse
    tokens: TokenResponse
    organization: Optional[OrganizationResponse] = None
    role: Optional[str] = None

class OrganizationCreate(BaseModel):
    name: str
    description: Optional[str] = None
    domain: Optional[str] = None
    allowed_domains: Optional[List[str]] = None

class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None
    organization_id: str
    budget: Optional[float] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    project_manager: Optional[str] = None
    team_members: Optional[List[str]] = None

class InviteMember(BaseModel):
    email: EmailStr
    role: str = "member"  # viewer, member, admin

class DashboardStats(BaseModel):
    total_organizations: int
    total_projects: int
    total_members: int
    recent_activity: List[dict]


def get_user_from_token(authorization: Optional[str] = Header(None)):
    """Get user from authorization token"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    try:
        token = authorization.replace("Bearer ", "")
        payload = verify_token(token)
        user_id = payload.get("sub")
        
        # Find user in database
        for email, user in users_db.items():
            if user["id"] == user_id:
                return user
        
        raise HTTPException(status_code=401, detail="User not found")
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid token")


def check_organization_access(user_id: str, organization_id: str, required_roles: List[str] = None):
    """Check if user has access to organization with required role"""
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["organization_id"] == organization_id:
            if required_roles and member["role"] not in required_roles:
                raise HTTPException(status_code=403, detail="Insufficient permissions")
            return member
    
    raise HTTPException(status_code=403, detail="Access denied to organization")


def check_domain_access(user_email: str, organization_id: str):
    """Check if user's email domain is allowed for organization"""
    org = organizations_db.get(organization_id)
    if not org or not org.get("allowed_domains"):
        return True  # No domain restrictions

    user_domain = user_email.split("@")[1].lower()
    allowed_domains = [domain.lower() for domain in org["allowed_domains"]]

    return user_domain in allowed_domains


def get_user_role_in_organization(user_id: str, organization_id: str):
    """Get user's role in organization"""
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["organization_id"] == organization_id:
            return member["role"]
    return None


def check_project_access(user_id: str, project_id: str):
    """Check if user has access to a specific project based on role-based rules"""
    project = projects_db.get(project_id)
    if not project:
        return False

    organization_id = project["organization_id"]
    user_role = get_user_role_in_organization(user_id, organization_id)

    if not user_role:
        return False

    # Owners can see all projects in their organization
    if user_role == "owner":
        return True

    # Admins can only see projects they have been given access to
    if user_role == "admin":
        # Check if admin has been granted access to this project
        for access_id, access in project_access_db.items():
            if (access["user_id"] == user_id and
                access["project_id"] == project_id and
                access["access_type"] in ["read", "write", "admin"]):
                return True
        return False

    # Members and viewers can only see projects they are assigned to
    if user_role in ["member", "viewer"]:
        # Check if user is in project team members
        if project.get("team_members") and user_id in project["team_members"]:
            return True
        # Check if user is the project manager
        if project.get("project_manager") == user_id:
            return True
        # Check if user created the project
        if project.get("created_by") == user_id:
            return True
        return False

    return False


def grant_project_access(user_id: str, project_id: str, access_type: str = "read", granted_by: str = None):
    """Grant project access to a user (typically an admin)"""
    access_id = str(uuid.uuid4())
    project_access_db[access_id] = {
        "id": access_id,
        "user_id": user_id,
        "project_id": project_id,
        "access_type": access_type,  # "read", "write", "admin"
        "granted_by": granted_by,
        "granted_at": time.time()
    }
    return access_id


def grant_admin_permission(user_id: str, organization_id: str, permission: str, granted_by: str = None):
    """Grant special permission to an admin"""
    permission_id = str(uuid.uuid4())
    admin_permissions_db[permission_id] = {
        "id": permission_id,
        "user_id": user_id,
        "organization_id": organization_id,
        "permission": permission,  # "create_projects", "manage_members", etc.
        "granted_by": granted_by,
        "granted_at": time.time()
    }
    return permission_id


def check_admin_permission(user_id: str, organization_id: str, permission: str):
    """Check if admin has specific permission"""
    user_role = get_user_role_in_organization(user_id, organization_id)

    # Owners always have all permissions
    if user_role == "owner":
        return True

    # Check if admin has been granted this specific permission
    if user_role == "admin":
        for perm_id, perm in admin_permissions_db.items():
            if (perm["user_id"] == user_id and
                perm["organization_id"] == organization_id and
                perm["permission"] == permission):
                return True

    return False


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "success": True,
        "data": {
            "message": "Welcome to Agno WorkSphere API (Enhanced)",
            "version": settings.app_version,
            "environment": settings.environment,
            "features": ["RBAC", "Email Notifications", "Domain Restrictions"]
        },
        "timestamp": time.time()
    }


@app.get("/api/v1/")
async def api_v1_root():
    """API v1 root endpoint"""
    return {
        "success": True,
        "data": {
            "message": "Agno WorkSphere API v1 (Enhanced)",
            "version": settings.app_version,
            "endpoints": {
                "auth": "/api/v1/auth",
                "users": "/api/v1/users",
                "organizations": "/api/v1/organizations",
                "projects": "/api/v1/projects",
                "boards": "/api/v1/boards",
                "columns": "/api/v1/columns",
                "cards": "/api/v1/cards",
                "teams": "/api/v1/teams",
                "analytics": "/api/v1/analytics",
                "ai": "/api/v1/ai"
            }
        },
        "timestamp": time.time()
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "version": settings.app_version,
            "environment": settings.environment,
            "mode": "enhanced",
            "email_configured": bool(settings.smtp_user and settings.smtp_pass)
        },
        "timestamp": time.time()
    }


@app.post("/api/v1/auth/register", response_model=dict)
async def register(
    user_data: UserRegister,
    background_tasks: BackgroundTasks
):
    """Register a new user and create organization"""
    print(f"📝 Registration attempt for: {user_data.email}")
    print(f"👤 User data: {user_data.dict()}")

    # Check if user already exists
    if user_data.email in users_db:
        print(f"❌ User already exists: {user_data.email}")
        raise HTTPException(status_code=409, detail="User with this email already exists")
    
    # Create user
    user_id = str(uuid.uuid4())
    hashed_password = hash_password(user_data.password)
    
    user = {
        "id": user_id,
        "email": user_data.email,
        "password_hash": hashed_password,
        "first_name": user_data.first_name,
        "last_name": user_data.last_name,
        "email_verified": True,  # Auto-verify for demo
        "created_at": time.time()
    }
    
    users_db[user_data.email] = user
    
    # Create default organization for the user
    org_name = user_data.organization_name or f"{user_data.first_name}'s Organization"
    org_id = str(uuid.uuid4())
    
    organization = {
        "id": org_id,
        "name": org_name,
        "description": f"Default organization for {user_data.first_name} {user_data.last_name}",
        "domain": user_data.email.split("@")[1],  # Set domain from user email
        "created_by": user_id,
        "created_at": time.time(),
        "allowed_domains": [user_data.email.split("@")[1]]  # Allow same domain by default
    }
    
    organizations_db[org_id] = organization
    
    # Add user as organization owner
    member_id = str(uuid.uuid4())
    member = {
        "id": member_id,
        "user_id": user_id,
        "organization_id": org_id,
        "role": "owner",
        "joined_at": time.time(),
        "invited_by": None
    }
    
    organization_members_db[member_id] = member
    
    # Generate token
    access_token = create_access_token({
        "sub": user_id,
        "email": user_data.email,
        "role": "owner",
        "org_id": org_id
    })
    
    # Send welcome email in background
    background_tasks.add_task(
        email_service.send_welcome_email,
        user_data.email,
        f"{user_data.first_name} {user_data.last_name}",
        org_name
    )
    
    return {
        "success": True,
        "data": AuthResponse(
            user=UserResponse(**user),
            tokens=TokenResponse(access_token=access_token),
            organization=OrganizationResponse(
                **organization,
                member_count=1
            ),
            role="owner"
        ),
        "message": "Registration successful! Welcome email sent."
    }


@app.post("/api/v1/auth/login", response_model=dict)
async def login(user_data: UserLogin):
    """Login user"""
    print(f"🔐 Login attempt for: {user_data.email}")

    # Find user
    user = users_db.get(user_data.email)
    print(f"👤 User found: {user is not None}")

    if not user:
        print(f"❌ User not found for email: {user_data.email}")
        print(f"📋 Available users: {list(users_db.keys())}")
        raise HTTPException(status_code=401, detail="Invalid email or password")

    password_valid = verify_password(user_data.password, user["password_hash"])
    print(f"🔑 Password valid: {password_valid}")

    if not password_valid:
        print(f"❌ Invalid password for user: {user_data.email}")
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    # Get user's primary organization and role
    primary_org = None
    user_role = None
    
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user["id"]:
            org = organizations_db.get(member["organization_id"])
            if org:
                primary_org = org
                user_role = member["role"]
                break
    
    # Generate token
    token_data = {
        "sub": user["id"],
        "email": user["email"]
    }
    
    if primary_org:
        token_data.update({
            "role": user_role,
            "org_id": primary_org["id"]
        })
    
    access_token = create_access_token(token_data)
    
    response_data = AuthResponse(
        user=UserResponse(**user),
        tokens=TokenResponse(access_token=access_token),
        role=user_role
    )
    
    if primary_org:
        # Count members
        member_count = sum(1 for m in organization_members_db.values() 
                          if m["organization_id"] == primary_org["id"])
        
        response_data.organization = OrganizationResponse(
            **primary_org,
            member_count=member_count
        )
    
    return {
        "success": True,
        "data": response_data,
        "message": "Login successful"
    }


@app.post("/api/v1/auth/logout")
async def logout(current_user: dict = Depends(get_user_from_token)):
    """Logout user"""
    # In a real implementation, you would invalidate the token
    # For now, we'll just return a success message
    return {
        "success": True,
        "data": None,
        "message": "Logout successful"
    }


@app.get("/api/v1/test/password")
async def test_password():
    """Test password hashing and verification"""
    test_password = "Owner123!"
    try:
        hashed = hash_password(test_password)
        verified = verify_password(test_password, hashed)

        # Check if demo user exists
        demo_user = users_db.get("<EMAIL>")
        demo_verified = False
        if demo_user:
            demo_verified = verify_password(test_password, demo_user["password_hash"])

        return {
            "test_password": test_password,
            "hash_created": bool(hashed),
            "verification_works": verified,
            "demo_user_exists": demo_user is not None,
            "demo_password_verified": demo_verified,
            "users_count": len(users_db)
        }
    except Exception as e:
        return {
            "error": str(e),
            "users_count": len(users_db)
        }

@app.post("/api/v1/test/login")
async def test_login():
    """Test login with hardcoded credentials"""
    try:
        # Simulate the exact login request
        user_data = UserLogin(email="<EMAIL>", password="Owner123!")

        print(f"🔐 Test login attempt for: {user_data.email}")

        # Find user
        user = users_db.get(user_data.email)
        print(f"👤 User found: {user is not None}")

        if not user:
            return {"error": "User not found", "available_users": list(users_db.keys())}

        password_valid = verify_password(user_data.password, user["password_hash"])
        print(f"🔑 Password valid: {password_valid}")

        if not password_valid:
            return {"error": "Invalid password"}

        return {"success": True, "message": "Login would succeed"}

    except Exception as e:
        print(f"❌ Test login error: {str(e)}")
        return {"error": str(e)}

@app.post("/api/v1/test/register")
async def test_register():
    """Test registration with hardcoded data"""
    try:
        # Test the exact registration data format
        test_data = {
            "email": "<EMAIL>",
            "password": "Test123!",
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Org"
        }

        print(f"📝 Test registration data: {test_data}")

        # Try to create UserRegister object
        user_data = UserRegister(**test_data)
        print(f"✅ UserRegister validation passed: {user_data.dict()}")

        return {"success": True, "message": "Registration validation would succeed", "data": user_data.dict()}

    except Exception as e:
        print(f"❌ Test registration error: {str(e)}")
        return {"error": str(e)}

@app.get("/api/v1/analytics/user-activity")
async def get_user_activity_analytics(period: str = "30d", current_user: dict = Depends(get_user_from_token)):
    """Get user activity analytics"""
    # Mock data for now - replace with real analytics
    return {
        "success": True,
        "data": {
            "totalUsers": len(users_db),
            "activeUsers": max(1, len(users_db) - 2),
            "newUsers": 1,
            "userGrowth": 12.5,
            "dailyActiveUsers": [
                {"date": "2024-01-01", "users": 15},
                {"date": "2024-01-02", "users": 18},
                {"date": "2024-01-03", "users": 16},
                {"date": "2024-01-04", "users": 20},
                {"date": "2024-01-05", "users": 22},
                {"date": "2024-01-06", "users": 19},
                {"date": "2024-01-07", "users": 24}
            ]
        }
    }

@app.get("/api/v1/analytics/organization-performance")
async def get_organization_performance(period: str = "30d", current_user: dict = Depends(get_user_from_token)):
    """Get organization performance analytics"""
    return {
        "success": True,
        "data": {
            "totalProjects": len(projects_db),
            "completedProjects": max(0, len(projects_db) - 2),
            "activeProjects": min(len(projects_db), 4),
            "completionRate": 75.0,
            "teamProductivity": 85
        }
    }

@app.get("/api/v1/billing/subscription")
async def get_subscription_details(current_user: dict = Depends(get_user_from_token)):
    """Get subscription details"""
    return {
        "success": True,
        "data": {
            "plan": "Professional",
            "status": "active",
            "price": 29.99,
            "currency": "USD",
            "billingCycle": "monthly",
            "nextBillingDate": "2024-02-15"
        }
    }

@app.get("/api/v1/dashboard/stats")
async def get_dashboard_stats(current_user: dict = Depends(get_user_from_token)):
    """Get dashboard statistics based on user role"""
    user_id = current_user["id"]

    # Get user's organizations
    user_orgs = []
    user_role_in_primary_org = None

    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                user_orgs.append({
                    "organization": org,
                    "role": member["role"]
                })
                if not user_role_in_primary_org:
                    user_role_in_primary_org = member["role"]

    # Count projects user has access to
    accessible_projects = []
    for project_id, project in projects_db.items():
        # Check if user has access to project's organization
        for org_info in user_orgs:
            if project["organization_id"] == org_info["organization"]["id"]:
                accessible_projects.append(project)
                break

    # Count total members across user's organizations
    total_members = 0
    for org_info in user_orgs:
        org_id = org_info["organization"]["id"]
        org_member_count = sum(1 for m in organization_members_db.values()
                              if m["organization_id"] == org_id)
        total_members += org_member_count

    # Recent activity (mock data for now)
    recent_activity = [
        {
            "type": "project_created",
            "message": "New project created",
            "timestamp": time.time() - 3600,
            "user": f"{current_user['first_name']} {current_user['last_name']}"
        },
        {
            "type": "member_joined",
            "message": "New member joined organization",
            "timestamp": time.time() - 7200,
            "user": "System"
        }
    ]

    return {
        "success": True,
        "data": DashboardStats(
            total_organizations=len(user_orgs),
            total_projects=len(accessible_projects),
            total_members=total_members,
            recent_activity=recent_activity
        ),
        "user_role": user_role_in_primary_org
    }


@app.get("/api/v1/users/me")
async def get_current_user_me(current_user: dict = Depends(get_user_from_token)):
    """Get current user profile (alias for /profile)"""
    user_id = current_user["id"]

    # Get user's organizations and roles
    organizations = []
    primary_role = None
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                member_count = sum(1 for m in organization_members_db.values()
                                 if m["organization_id"] == org["id"])

                organizations.append({
                    "organization": OrganizationResponse(**org, member_count=member_count),
                    "role": member["role"],
                    "joined_at": member["joined_at"]
                })

                # Set primary role (first organization's role, or highest priority role)
                if primary_role is None:
                    primary_role = member["role"]
                elif member["role"] == "owner":
                    primary_role = "owner"
                elif member["role"] == "admin" and primary_role not in ["owner"]:
                    primary_role = "admin"

    # Create user response with role information
    user_data = current_user.copy()
    user_data["role"] = primary_role or "member"

    return {
        "success": True,
        "data": {
            "user": UserResponse(**user_data),
            "organizations": organizations
        }
    }


@app.put("/api/v1/users/me")
async def update_current_user_me(
    profile_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update current user profile (alias for /profile)"""
    user_id = current_user["id"]

    # Find user by email and update
    for email, user in users_db.items():
        if user["id"] == user_id:
            # Update allowed fields
            if "first_name" in profile_data:
                user["first_name"] = profile_data["first_name"]
            if "last_name" in profile_data:
                user["last_name"] = profile_data["last_name"]

            users_db[email] = user
            break

    return {
        "success": True,
        "data": UserResponse(**users_db[current_user["email"]]),
        "message": "Profile updated successfully"
    }


@app.get("/api/v1/users/{user_id}")
async def get_user_by_id(
    user_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get user by ID"""
    # Find user by ID
    target_user = None
    for email, user in users_db.items():
        if user["id"] == user_id:
            target_user = user
            break

    if not target_user:
        raise HTTPException(status_code=404, detail="User not found")

    return {
        "success": True,
        "data": UserResponse(**target_user)
    }


@app.get("/api/v1/users/profile")
async def get_profile(current_user: dict = Depends(get_user_from_token)):
    """Get current user profile with organization info"""
    user_id = current_user["id"]

    # Get user's organizations and roles
    organizations = []
    primary_role = None
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                member_count = sum(1 for m in organization_members_db.values()
                                 if m["organization_id"] == org["id"])

                organizations.append({
                    "organization": OrganizationResponse(**org, member_count=member_count),
                    "role": member["role"],
                    "joined_at": member["joined_at"]
                })

                # Set primary role (first organization's role, or highest priority role)
                if primary_role is None:
                    primary_role = member["role"]
                elif member["role"] == "owner":
                    primary_role = "owner"
                elif member["role"] == "admin" and primary_role not in ["owner"]:
                    primary_role = "admin"

    # Create user response with role information
    user_data = current_user.copy()
    user_data["role"] = primary_role or "member"

    return {
        "success": True,
        "data": {
            "user": UserResponse(**user_data),
            "organizations": organizations
        }
    }


@app.get("/api/v1/organizations")
async def get_organizations(current_user: dict = Depends(get_user_from_token)):
    """Get organizations user has access to"""
    user_id = current_user["id"]
    user_email = current_user["email"]

    organizations = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                # Check domain access
                if check_domain_access(user_email, org["id"]):
                    member_count = sum(1 for m in organization_members_db.values()
                                     if m["organization_id"] == org["id"])

                    organizations.append({
                        "organization": OrganizationResponse(**org, member_count=member_count),
                        "role": member["role"]
                    })

    return {
        "success": True,
        "data": organizations
    }


@app.post("/api/v1/organizations")
async def create_organization(
    org_data: OrganizationCreate,
    current_user: dict = Depends(get_user_from_token)
):
    """Create a new organization (owner/admin only)"""
    user_id = current_user["id"]

    # Check if user has permission to create organizations
    # For now, allow any authenticated user to create organizations

    org_id = str(uuid.uuid4())
    organization = {
        "id": org_id,
        "name": org_data.name,
        "description": org_data.description,
        "domain": org_data.domain or current_user["email"].split("@")[1],
        "created_by": user_id,
        "created_at": time.time(),
        "allowed_domains": org_data.allowed_domains or []
    }

    organizations_db[org_id] = organization

    # Add creator as owner
    member_id = str(uuid.uuid4())
    member = {
        "id": member_id,
        "user_id": user_id,
        "organization_id": org_id,
        "role": "owner",
        "joined_at": time.time(),
        "invited_by": None
    }

    organization_members_db[member_id] = member

    return {
        "success": True,
        "data": {
            "organization": OrganizationResponse(**organization, member_count=1),
            "role": "owner"
        },
        "message": "Organization created successfully"
    }


@app.get("/api/v1/organizations/{org_id}/members")
async def get_organization_members(
    org_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get organization members (member+ role required)"""
    user_id = current_user["id"]

    # Check access
    check_organization_access(user_id, org_id, ["member", "admin", "owner"])

    members = []
    for member_id, member in organization_members_db.items():
        if member["organization_id"] == org_id:
            # Get user info
            user_info = None
            for email, user in users_db.items():
                if user["id"] == member["user_id"]:
                    user_info = user
                    break

            if user_info:
                members.append(OrganizationMemberResponse(
                    **member,
                    user=UserResponse(**user_info)
                ))

    return {
        "success": True,
        "data": members
    }


@app.post("/api/v1/organizations/{org_id}/invite")
async def invite_member(
    org_id: str,
    invite_data: InviteMember,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_user_from_token)
):
    """Invite member to organization (admin+ role required)"""
    user_id = current_user["id"]

    # Check access
    member_info = check_organization_access(user_id, org_id, ["admin", "owner"])

    # Check domain restrictions
    if not check_domain_access(invite_data.email, org_id):
        raise HTTPException(
            status_code=403,
            detail="Email domain not allowed for this organization"
        )

    # Check if user already exists
    existing_user = users_db.get(invite_data.email)

    if existing_user:
        # Check if already a member
        for member_id, member in organization_members_db.items():
            if (member["user_id"] == existing_user["id"] and
                member["organization_id"] == org_id):
                raise HTTPException(status_code=409, detail="User is already a member")

        # Add existing user to organization
        member_id = str(uuid.uuid4())
        member = {
            "id": member_id,
            "user_id": existing_user["id"],
            "organization_id": org_id,
            "role": invite_data.role,
            "joined_at": time.time(),
            "invited_by": user_id
        }

        organization_members_db[member_id] = member

        message = "User added to organization successfully"
    else:
        # Create invitation for new user
        invitation_id = str(uuid.uuid4())
        invitation = {
            "id": invitation_id,
            "email": invite_data.email,
            "organization_id": org_id,
            "role": invite_data.role,
            "invited_by": user_id,
            "created_at": time.time(),
            "expires_at": time.time() + (7 * 24 * 3600)  # 7 days
        }

        invitations_db[invitation_id] = invitation

        # Send invitation email
        org = organizations_db[org_id]
        inviter_name = f"{current_user['first_name']} {current_user['last_name']}"
        invitation_url = f"http://localhost:3000/invite/{invitation_id}"

        background_tasks.add_task(
            email_service.send_invitation_email,
            invite_data.email,
            inviter_name,
            org["name"],
            invite_data.role,
            invitation_url
        )

        message = "Invitation sent successfully"

    return {
        "success": True,
        "message": message
    }


@app.get("/api/v1/projects")
async def get_projects(
    organization_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get projects user has access to"""
    user_id = current_user["id"]

    accessible_projects = []

    if organization_id:
        # Check access to specific organization
        member = check_organization_access(user_id, organization_id)
        user_role = member["role"]

        # Get projects for this organization based on role-based access
        for project_id, project in projects_db.items():
            if project["organization_id"] == organization_id and check_project_access(user_id, project_id):
                # Create a copy and filter budget for non-owners
                project_data = project.copy()
                if user_role != "owner" and "budget" in project_data:
                    del project_data["budget"]
                accessible_projects.append(project_data)
    else:
        # Get all projects user has access to across all organizations
        for project_id, project in projects_db.items():
            if check_project_access(user_id, project_id):
                # Get user role for this project's organization
                user_role = get_user_role_in_organization(user_id, project["organization_id"])

                # Create a copy and filter budget for non-owners
                project_data = project.copy()
                if user_role != "owner" and "budget" in project_data:
                    del project_data["budget"]
                accessible_projects.append(project_data)

    return {
        "success": True,
        "data": accessible_projects
    }


@app.post("/api/v1/projects")
async def create_project(
    project_data: ProjectCreate,
    current_user: dict = Depends(get_user_from_token)
):
    """Create project (member+ role required)"""
    user_id = current_user["id"]

    # Check access to organization
    member = check_organization_access(user_id, project_data.organization_id, ["member", "admin", "owner"])
    user_role = member["role"]

    # Additional permission check for admins
    if user_role == "admin":
        if not check_admin_permission(user_id, project_data.organization_id, "create_projects"):
            raise HTTPException(status_code=403, detail="Admin does not have project creation permission")

    # Members can only create projects if they have been granted permission (for future enhancement)
    # For now, allow all members to create projects

    project_id = str(uuid.uuid4())
    project = {
        "id": project_id,
        "name": project_data.name,
        "description": project_data.description,
        "organization_id": project_data.organization_id,
        "status": "active",
        "created_by": user_id,
        "created_at": time.time(),
        "budget": project_data.budget,
        "start_date": project_data.start_date,
        "end_date": project_data.end_date,
        "project_manager": project_data.project_manager,
        "team_members": project_data.team_members or []
    }

    projects_db[project_id] = project

    # Create default board for the project
    board_id = str(uuid.uuid4())
    board = {
        "id": board_id,
        "name": f"{project_data.name} Board",
        "description": f"Main board for {project_data.name}",
        "project_id": project_id,
        "created_by": user_id,
        "created_at": time.time()
    }
    boards_db[board_id] = board

    # Create default columns for the board
    default_columns = [
        {"title": "To Do", "status": "todo", "order": 1},
        {"title": "In Progress", "status": "in-progress", "order": 2},
        {"title": "Review", "status": "review", "order": 3},
        {"title": "Done", "status": "done", "order": 4}
    ]

    for col_data in default_columns:
        column_id = str(uuid.uuid4())
        column = {
            "id": column_id,
            "title": col_data["title"],
            "status": col_data["status"],
            "order": col_data["order"],
            "board_id": board_id,
            "created_by": user_id,
            "created_at": time.time()
        }
        columns_db[column_id] = column

    # Create notification for project creation
    create_project_notification(project_id, "created", user_id, project_data.organization_id)

    return {
        "success": True,
        "data": project,
        "message": "Project created successfully"
    }


@app.post("/api/v1/projects/ai-generate")
async def create_ai_project(
    project_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create AI-powered project (owner role required)"""
    user_id = current_user["id"]

    # Extract required data
    project_name = project_data.get("name")
    organization_id = project_data.get("organization_id")
    project_type = project_data.get("project_type", "general")

    if not project_name:
        raise HTTPException(status_code=400, detail="Project name is required")
    if not organization_id:
        raise HTTPException(status_code=400, detail="Organization ID is required")

    # Check access - OWNER ONLY for AI project creation
    member = check_organization_access(user_id, organization_id, ["owner"])

    try:
        # Import AI service
        from app.services.ai_service import AIService

        # Create mock database session for AI service
        class MockDB:
            def query(self, *args):
                return self
            def filter(self, *args):
                return self
            def first(self):
                return None
            def add(self, *args):
                pass
            def commit(self):
                pass

        # Generate AI project
        ai_service = AIService(MockDB())
        ai_result = await ai_service.generate_ai_project(
            project_name=project_name,
            organization_id=organization_id,
            user_id=user_id,
            project_type=project_type
        )

        if not ai_result.get("success"):
            raise HTTPException(status_code=500, detail=f"AI project generation failed: {ai_result.get('error', 'Unknown error')}")

        # Create the main project
        project_id = str(uuid.uuid4())
        ai_project_data = ai_result["project"]

        project = {
            "id": project_id,
            "name": ai_project_data["name"],
            "description": ai_project_data["description"],
            "organization_id": organization_id,
            "status": "active",
            "priority": ai_project_data.get("priority", "medium"),
            "budget": ai_project_data.get("estimated_budget", ""),
            "start_date": time.time(),
            "end_date": None,
            "project_manager": user_id,
            "team_members": [user_id],
            "created_by": user_id,
            "created_at": time.time(),
            "ai_generated": True,
            "ai_metadata": {
                "project_type": project_type,
                "estimated_duration": ai_result["metadata"]["estimated_duration"],
                "estimated_hours": ai_result["metadata"]["estimated_hours"],
                "risk_level": ai_result["metadata"]["risk_level"],
                "complexity": ai_result["metadata"]["complexity"],
                "recommended_team_size": ai_result["metadata"]["team_size_recommendation"],
                "key_technologies": ai_result["metadata"]["key_technologies"],
                "workflow": ai_result["workflow"],
                "objectives": ai_project_data["objectives"],
                "success_criteria": ai_project_data["success_criteria"]
            }
        }

        projects_db[project_id] = project

        # Create default Kanban board for the project
        board_id = str(uuid.uuid4())
        board = {
            "id": board_id,
            "name": f"{project_name} - Main Board",
            "description": "AI-generated project board with comprehensive task breakdown",
            "project_id": project_id,
            "organization_id": organization_id,
            "created_by": user_id,
            "created_at": time.time(),
            "ai_generated": True
        }
        boards_db[board_id] = board

        # Create default columns
        default_columns = [
            {"title": "To Do", "status": "todo", "order": 0},
            {"title": "In Progress", "status": "in_progress", "order": 1},
            {"title": "Review", "status": "review", "order": 2},
            {"title": "Done", "status": "done", "order": 3}
        ]

        column_ids = []
        for col_data in default_columns:
            column_id = str(uuid.uuid4())
            column = {
                "id": column_id,
                "title": col_data["title"],
                "status": col_data["status"],
                "order": col_data["order"],
                "board_id": board_id,
                "created_by": user_id,
                "created_at": time.time()
            }
            columns_db[column_id] = column
            column_ids.append(column_id)

        # Create AI-generated tasks as cards
        ai_tasks = ai_result["tasks"]
        created_cards = []

        for task_index, task in enumerate(ai_tasks):
            card_id = str(uuid.uuid4())

            # Assign to appropriate column based on phase
            column_id = column_ids[0]  # Default to "To Do"

            card = {
                "id": card_id,
                "title": task["title"],
                "description": task["description"],
                "column_id": column_id,
                "board_id": board_id,
                "project_id": project_id,
                "position": task_index,
                "priority": task.get("priority", "medium"),
                "status": task.get("status", "todo"),
                "assigned_to": [user_id],  # Assign to project creator initially
                "due_date": None,
                "created_by": user_id,
                "created_at": time.time(),
                "ai_generated": True,
                "ai_metadata": {
                    "phase": task.get("phase"),
                    "phase_description": task.get("phase_description"),
                    "estimated_hours": task.get("estimated_hours", 8),
                    "tags": task.get("tags", []),
                    "checklist": task.get("checklist", []),
                    "dependencies": task.get("dependencies", []),
                    "assignee_role": task.get("assignee_role", "team_member")
                }
            }

            cards_db[card_id] = card
            created_cards.append(card)

        # Create notification for AI project creation
        create_project_notification(project_id, "ai_created", user_id, organization_id)

        # Set up integrations for the AI project
        integration_results = None
        try:
            from app.services.integration_service import IntegrationService

            # Create mock database session for integration service
            integration_service = IntegrationService(MockDB())

            # Update integration setup data with actual project ID
            integration_setup = ai_result.get("integration_setup", {})
            if integration_setup:
                integration_setup["project_data"]["id"] = project_id

                # Set up integrations
                integration_results = await integration_service.setup_ai_project_integrations(
                    project_data=integration_setup["project_data"],
                    tasks=integration_setup["tasks"],
                    workflow=integration_setup["workflow"],
                    organization_id=organization_id,
                    integration_preferences={
                        'calendar_enabled': True,
                        'communication_enabled': True,
                        'version_control_enabled': True,
                        'time_tracking_enabled': False  # Optional
                    }
                )

                print(f"✅ Integration setup completed: {integration_results}")

        except Exception as integration_error:
            print(f"⚠️ Integration setup failed (non-critical): {str(integration_error)}")
            # Don't fail the entire project creation if integrations fail

        return {
            "success": True,
            "data": {
                "project": project,
                "board": board,
                "tasks_created": len(created_cards),
                "ai_metadata": ai_result["metadata"],
                "workflow": ai_result["workflow"],
                "integrations": integration_results
            },
            "message": f"AI-powered project '{project_name}' created successfully with {len(created_cards)} tasks and integrations!"
        }

    except Exception as e:
        print(f"❌ AI project creation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create AI project: {str(e)}")


# Additional endpoints for complete API coverage

@app.put("/api/v1/users/profile")
async def update_profile(
    profile_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update user profile"""
    user_id = current_user["id"]
    user_email = current_user["email"]

    if user_email in users_db:
        user = users_db[user_email]
        # Update allowed fields
        if "first_name" in profile_data:
            user["first_name"] = profile_data["first_name"]
        if "last_name" in profile_data:
            user["last_name"] = profile_data["last_name"]
        if "bio" in profile_data:
            user["bio"] = profile_data["bio"]

        users_db[user_email] = user

        return {
            "success": True,
            "data": {
                "id": user["id"],
                "email": user["email"],
                "first_name": user["first_name"],
                "last_name": user["last_name"],
                "bio": user.get("bio", "")
            },
            "message": "Profile updated successfully"
        }

    raise HTTPException(status_code=404, detail="User not found")


@app.get("/api/v1/organizations/{org_id}")
async def get_organization(
    org_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get organization details"""
    user_id = current_user["id"]

    # Check if user has access to this organization
    has_access = False
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["organization_id"] == org_id:
            has_access = True
            break

    if not has_access:
        raise HTTPException(status_code=404, detail="Organization not found")

    if org_id not in organizations_db:
        raise HTTPException(status_code=404, detail="Organization not found")

    org = organizations_db[org_id]
    return {
        "success": True,
        "data": org
    }


@app.put("/api/v1/organizations/{org_id}")
async def update_organization(
    org_id: str,
    org_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update organization details"""
    user_id = current_user["id"]

    # Check if user has admin/owner access to this organization
    check_organization_access(user_id, org_id, ["admin", "owner"])

    if org_id not in organizations_db:
        raise HTTPException(status_code=404, detail="Organization not found")

    org = organizations_db[org_id]

    # Update allowed fields
    if "name" in org_data:
        org["name"] = org_data["name"]
    if "description" in org_data:
        org["description"] = org_data["description"]
    if "domain" in org_data:
        org["domain"] = org_data["domain"]

    # Update timestamp
    org["updated_at"] = time.time()

    organizations_db[org_id] = org

    return {
        "success": True,
        "data": org,
        "message": "Organization updated successfully"
    }


@app.get("/api/v1/projects/{project_id}")
async def get_project(
    project_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get project details"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    # Check access to organization
    try:
        member = check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])
        user_role = member["role"]
    except HTTPException:
        raise HTTPException(status_code=404, detail="Project not found")

    # Create a copy of project data
    project_data = project.copy()

    # Remove budget information for non-owners
    if user_role != "owner" and "budget" in project_data:
        del project_data["budget"]

    return {
        "success": True,
        "data": project_data
    }


@app.put("/api/v1/projects/{project_id}")
async def update_project(
    project_id: str,
    project_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update project"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    # Check access (admin/owner only for updates)
    check_organization_access(user_id, project["organization_id"], ["admin", "owner"])

    # Update allowed fields
    if "name" in project_data:
        project["name"] = project_data["name"]
    if "description" in project_data:
        project["description"] = project_data["description"]
    if "status" in project_data:
        project["status"] = project_data["status"]

    projects_db[project_id] = project

    # Create notification for project update
    create_project_notification(project_id, "updated", user_id, project["organization_id"])

    return {
        "success": True,
        "data": project,
        "message": "Project updated successfully"
    }


@app.delete("/api/v1/projects/{project_id}")
async def delete_project(
    project_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Delete project (owner only)"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    # Check access (owner only for deletion)
    check_organization_access(user_id, project["organization_id"], ["owner"])

    # Create notification before deletion (while project data is still available)
    create_project_notification(project_id, "deleted", user_id, project["organization_id"])

    # Remove project from database
    del projects_db[project_id]

    # Also remove any related boards, columns, and cards
    boards_to_delete = [board_id for board_id, board in boards_db.items() if board["project_id"] == project_id]
    for board_id in boards_to_delete:
        del boards_db[board_id]

        # Remove columns and cards for this board
        columns_to_delete = [col_id for col_id, col in columns_db.items() if col["board_id"] == board_id]
        for col_id in columns_to_delete:
            del columns_db[col_id]

            # Remove cards for this column
            cards_to_delete = [card_id for card_id, card in cards_db.items() if card["column_id"] == col_id]
            for card_id in cards_to_delete:
                del cards_db[card_id]

    return {
        "success": True,
        "message": "Project deleted successfully"
    }


@app.post("/api/v1/projects/{project_id}/grant-access")
async def grant_project_access_endpoint(
    project_id: str,
    access_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Grant project access to a user (owner only)"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    # Check access (owner only)
    check_organization_access(user_id, project["organization_id"], ["owner"])

    # Validate the user to grant access to
    target_user_id = access_data.get("user_id")
    access_type = access_data.get("access_type", "read")  # "read", "write", "admin"

    if not target_user_id:
        raise HTTPException(status_code=400, detail="User ID is required")

    # Check if target user is in the organization
    target_user_role = get_user_role_in_organization(target_user_id, project["organization_id"])
    if not target_user_role:
        raise HTTPException(status_code=400, detail="User is not a member of this organization")

    # Grant access
    access_id = grant_project_access(target_user_id, project_id, access_type, user_id)

    return {
        "success": True,
        "data": {
            "access_id": access_id,
            "user_id": target_user_id,
            "project_id": project_id,
            "access_type": access_type
        },
        "message": "Project access granted successfully"
    }


@app.delete("/api/v1/projects/{project_id}/revoke-access/{user_id}")
async def revoke_project_access(
    project_id: str,
    user_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Revoke project access from a user (owner only)"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    current_user_id = current_user["id"]

    # Check access (owner only)
    check_organization_access(current_user_id, project["organization_id"], ["owner"])

    # Find and remove access
    access_to_remove = []
    for access_id, access in project_access_db.items():
        if access["user_id"] == user_id and access["project_id"] == project_id:
            access_to_remove.append(access_id)

    for access_id in access_to_remove:
        del project_access_db[access_id]

    return {
        "success": True,
        "message": f"Project access revoked for user {user_id}"
    }


@app.get("/api/v1/projects/{project_id}/access")
async def get_project_access_list(
    project_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get list of users with access to project (owner only)"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    # Check access (owner only)
    check_organization_access(user_id, project["organization_id"], ["owner"])

    # Get all access records for this project
    access_list = []
    for access_id, access in project_access_db.items():
        if access["project_id"] == project_id:
            # Get user details
            user_details = None
            for email, user in users_db.items():
                if user["id"] == access["user_id"]:
                    user_details = {
                        "id": user["id"],
                        "name": f"{user['first_name']} {user['last_name']}",
                        "email": user["email"]
                    }
                    break

            if user_details:
                access_list.append({
                    "access_id": access["id"],
                    "user": user_details,
                    "access_type": access["access_type"],
                    "granted_at": access["granted_at"]
                })

    return {
        "success": True,
        "data": access_list
    }


@app.post("/api/v1/organizations/{organization_id}/grant-admin-permission")
async def grant_admin_permission_endpoint(
    organization_id: str,
    permission_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Grant special permission to an admin (owner only)"""
    user_id = current_user["id"]

    # Check access (owner only)
    check_organization_access(user_id, organization_id, ["owner"])

    target_user_id = permission_data.get("user_id")
    permission = permission_data.get("permission")

    if not target_user_id or not permission:
        raise HTTPException(status_code=400, detail="User ID and permission are required")

    # Validate that target user is an admin in this organization
    target_user_role = get_user_role_in_organization(target_user_id, organization_id)
    if target_user_role != "admin":
        raise HTTPException(status_code=400, detail="User must be an admin to receive special permissions")

    # Grant permission
    permission_id = grant_admin_permission(target_user_id, organization_id, permission, user_id)

    return {
        "success": True,
        "data": {
            "permission_id": permission_id,
            "user_id": target_user_id,
            "organization_id": organization_id,
            "permission": permission
        },
        "message": f"Permission '{permission}' granted to admin successfully"
    }


@app.delete("/api/v1/organizations/{organization_id}/revoke-admin-permission/{user_id}/{permission}")
async def revoke_admin_permission(
    organization_id: str,
    user_id: str,
    permission: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Revoke special permission from an admin (owner only)"""
    current_user_id = current_user["id"]

    # Check access (owner only)
    check_organization_access(current_user_id, organization_id, ["owner"])

    # Find and remove permission
    permissions_to_remove = []
    for perm_id, perm in admin_permissions_db.items():
        if (perm["user_id"] == user_id and
            perm["organization_id"] == organization_id and
            perm["permission"] == permission):
            permissions_to_remove.append(perm_id)

    for perm_id in permissions_to_remove:
        del admin_permissions_db[perm_id]

    return {
        "success": True,
        "message": f"Permission '{permission}' revoked from admin successfully"
    }


@app.get("/api/v1/organizations/{organization_id}/admin-permissions")
async def get_admin_permissions(
    organization_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get list of admin permissions in organization (owner only)"""
    user_id = current_user["id"]

    # Check access (owner only)
    check_organization_access(user_id, organization_id, ["owner"])

    # Get all admin permissions for this organization
    permissions_list = []
    for perm_id, perm in admin_permissions_db.items():
        if perm["organization_id"] == organization_id:
            # Get user details
            user_details = None
            for email, user in users_db.items():
                if user["id"] == perm["user_id"]:
                    user_details = {
                        "id": user["id"],
                        "name": f"{user['first_name']} {user['last_name']}",
                        "email": user["email"]
                    }
                    break

            if user_details:
                permissions_list.append({
                    "permission_id": perm["id"],
                    "user": user_details,
                    "permission": perm["permission"],
                    "granted_at": perm["granted_at"]
                })

    return {
        "success": True,
        "data": permissions_list
    }


# Board Management Endpoints
boards_db = {}
columns_db = {}
cards_db = {}

@app.get("/api/v1/boards")
async def get_boards(
    project_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get boards"""
    user_id = current_user["id"]
    accessible_boards = []

    for board_id, board in boards_db.items():
        if project_id and board["project_id"] != project_id:
            continue

        # Check access to project's organization
        if board["project_id"] in projects_db:
            project = projects_db[board["project_id"]]
            try:
                check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])
                accessible_boards.append(board)
            except HTTPException:
                continue

    return {
        "success": True,
        "data": accessible_boards
    }


@app.post("/api/v1/boards")
async def create_board(
    board_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create board"""
    user_id = current_user["id"]
    project_id = board_data.get("project_id")

    if not project_id or project_id not in projects_db:
        raise HTTPException(status_code=400, detail="Valid project_id required")

    project = projects_db[project_id]
    check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    board_id = str(uuid.uuid4())
    board = {
        "id": board_id,
        "name": board_data.get("name", "New Board"),
        "description": board_data.get("description", ""),
        "project_id": project_id,
        "created_by": user_id,
        "created_at": time.time()
    }

    boards_db[board_id] = board

    return {
        "success": True,
        "data": board,
        "message": "Board created successfully"
    }


@app.get("/api/v1/boards/{board_id}")
async def get_board(
    board_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get board details"""
    if board_id not in boards_db:
        raise HTTPException(status_code=404, detail="Board not found")

    board = boards_db[board_id]
    user_id = current_user["id"]

    # Check access
    if board["project_id"] in projects_db:
        project = projects_db[board["project_id"]]
        check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])

    return {
        "success": True,
        "data": board
    }


# Column Management Endpoints
@app.get("/api/v1/columns")
async def get_columns(
    board_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get columns"""
    user_id = current_user["id"]
    accessible_columns = []

    for column_id, column in columns_db.items():
        if board_id and column["board_id"] != board_id:
            continue

        # Check access through board -> project -> organization
        if column["board_id"] in boards_db:
            board = boards_db[column["board_id"]]
            if board["project_id"] in projects_db:
                project = projects_db[board["project_id"]]
                try:
                    check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])
                    accessible_columns.append(column)
                except HTTPException:
                    continue

    return {
        "success": True,
        "data": accessible_columns
    }


@app.post("/api/v1/columns")
async def create_column(
    column_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create column"""
    user_id = current_user["id"]
    board_id = column_data.get("board_id")

    if not board_id or board_id not in boards_db:
        raise HTTPException(status_code=400, detail="Valid board_id required")

    board = boards_db[board_id]
    if board["project_id"] in projects_db:
        project = projects_db[board["project_id"]]
        check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    column_id = str(uuid.uuid4())
    column = {
        "id": column_id,
        "name": column_data.get("name", "New Column"),
        "board_id": board_id,
        "position": column_data.get("position", 0),
        "created_by": user_id,
        "created_at": time.time()
    }

    columns_db[column_id] = column

    return {
        "success": True,
        "data": column,
        "message": "Column created successfully"
    }


@app.put("/api/v1/columns/{column_id}")
async def update_column(
    column_id: str,
    column_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update column"""
    if column_id not in columns_db:
        raise HTTPException(status_code=404, detail="Column not found")

    column = columns_db[column_id]
    user_id = current_user["id"]

    # Check access
    if column["board_id"] in boards_db:
        board = boards_db[column["board_id"]]
        if board["project_id"] in projects_db:
            project = projects_db[board["project_id"]]
            check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    # Update fields
    if "name" in column_data:
        column["name"] = column_data["name"]
    if "position" in column_data:
        column["position"] = column_data["position"]

    columns_db[column_id] = column

    return {
        "success": True,
        "data": column,
        "message": "Column updated successfully"
    }


@app.delete("/api/v1/columns/{column_id}")
async def delete_column(
    column_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Delete column"""
    if column_id not in columns_db:
        raise HTTPException(status_code=404, detail="Column not found")

    column = columns_db[column_id]
    user_id = current_user["id"]

    # Check access
    if column["board_id"] in boards_db:
        board = boards_db[column["board_id"]]
        if board["project_id"] in projects_db:
            project = projects_db[board["project_id"]]
            check_organization_access(user_id, project["organization_id"], ["admin", "owner"])

    del columns_db[column_id]

    return {
        "success": True,
        "message": "Column deleted successfully"
    }


# Card Management Endpoints
@app.get("/api/v1/cards")
async def get_cards(
    column_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get cards"""
    user_id = current_user["id"]
    accessible_cards = []

    for card_id, card in cards_db.items():
        if column_id and card["column_id"] != column_id:
            continue

        # Check access through column -> board -> project -> organization
        if card["column_id"] in columns_db:
            column = columns_db[card["column_id"]]
            if column["board_id"] in boards_db:
                board = boards_db[column["board_id"]]
                if board["project_id"] in projects_db:
                    project = projects_db[board["project_id"]]
                    try:
                        check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])
                        accessible_cards.append(card)
                    except HTTPException:
                        continue

    return {
        "success": True,
        "data": accessible_cards
    }


@app.post("/api/v1/cards")
async def create_card(
    card_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create card"""
    user_id = current_user["id"]
    column_id = card_data.get("column_id")

    if not column_id or column_id not in columns_db:
        raise HTTPException(status_code=400, detail="Valid column_id required")

    # Check access
    column = columns_db[column_id]
    if column["board_id"] in boards_db:
        board = boards_db[column["board_id"]]
        if board["project_id"] in projects_db:
            project = projects_db[board["project_id"]]
            check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    card_id = str(uuid.uuid4())
    card = {
        "id": card_id,
        "title": card_data.get("title", "New Card"),
        "description": card_data.get("description", ""),
        "column_id": column_id,
        "position": card_data.get("position", 0),
        "priority": card_data.get("priority", "medium"),
        "created_by": user_id,
        "created_at": time.time()
    }

    cards_db[card_id] = card

    return {
        "success": True,
        "data": card,
        "message": "Card created successfully"
    }


@app.get("/api/v1/cards/{card_id}")
async def get_card_by_id(
    card_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get card by ID"""
    user_id = current_user["id"]

    card = cards_db.get(card_id)
    if not card:
        raise HTTPException(status_code=404, detail="Card not found")

    # Check access through column -> board -> project -> organization
    column = columns_db.get(card["column_id"])
    if not column:
        raise HTTPException(status_code=404, detail="Column not found")

    board = boards_db.get(column["board_id"])
    if not board:
        raise HTTPException(status_code=404, detail="Board not found")

    project = projects_db.get(board["project_id"])
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])
    except HTTPException:
        raise HTTPException(status_code=403, detail="Access denied")

    # Get card comments
    card_comments = [c for c in comments_db.values() if c["card_id"] == card_id]

    card_with_details = card.copy()
    card_with_details["comments"] = card_comments
    card_with_details["comment_count"] = len(card_comments)

    return {
        "success": True,
        "data": card_with_details
    }


@app.put("/api/v1/cards/{card_id}")
async def update_card(
    card_id: str,
    card_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update card"""
    if card_id not in cards_db:
        raise HTTPException(status_code=404, detail="Card not found")

    card = cards_db[card_id]
    user_id = current_user["id"]

    # Check access
    if card["column_id"] in columns_db:
        column = columns_db[card["column_id"]]
        if column["board_id"] in boards_db:
            board = boards_db[column["board_id"]]
            if board["project_id"] in projects_db:
                project = projects_db[board["project_id"]]
                check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    # Update fields
    if "title" in card_data:
        card["title"] = card_data["title"]
    if "description" in card_data:
        card["description"] = card_data["description"]
    if "priority" in card_data:
        card["priority"] = card_data["priority"]
    if "column_id" in card_data:
        card["column_id"] = card_data["column_id"]
    if "assignee" in card_data:
        card["assignee"] = card_data["assignee"]

    # Update timestamp
    card["updated_at"] = time.time()

    cards_db[card_id] = card

    return {
        "success": True,
        "data": card,
        "message": "Card updated successfully"
    }


# Card Comments
comments_db = {}

@app.get("/api/v1/cards/{card_id}/comments")
async def get_card_comments(
    card_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get card comments"""
    if card_id not in cards_db:
        raise HTTPException(status_code=404, detail="Card not found")

    user_id = current_user["id"]
    card = cards_db[card_id]

    # Check access
    if card["column_id"] in columns_db:
        column = columns_db[card["column_id"]]
        if column["board_id"] in boards_db:
            board = boards_db[column["board_id"]]
            if board["project_id"] in projects_db:
                project = projects_db[board["project_id"]]
                check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])

    card_comments = [comment for comment in comments_db.values() if comment["card_id"] == card_id]

    return {
        "success": True,
        "data": card_comments
    }


@app.post("/api/v1/cards/{card_id}/comments")
async def add_card_comment(
    card_id: str,
    comment_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Add comment to card"""
    if card_id not in cards_db:
        raise HTTPException(status_code=404, detail="Card not found")

    user_id = current_user["id"]
    card = cards_db[card_id]

    # Check access
    if card["column_id"] in columns_db:
        column = columns_db[card["column_id"]]
        if column["board_id"] in boards_db:
            board = boards_db[column["board_id"]]
            if board["project_id"] in projects_db:
                project = projects_db[board["project_id"]]
                check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    comment_id = str(uuid.uuid4())
    comment = {
        "id": comment_id,
        "card_id": card_id,
        "content": comment_data.get("content", ""),
        "created_by": user_id,
        "created_at": time.time()
    }

    comments_db[comment_id] = comment

    return {
        "success": True,
        "data": comment,
        "message": "Comment added successfully"
    }


# Team Management Endpoints
teams_db = {}
team_members_db = {}

@app.get("/api/v1/teams")
async def get_teams(
    organization_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get teams"""
    user_id = current_user["id"]
    accessible_teams = []

    for team_id, team in teams_db.items():
        if organization_id and team["organization_id"] != organization_id:
            continue

        try:
            check_organization_access(user_id, team["organization_id"], ["viewer", "member", "admin", "owner"])
            accessible_teams.append(team)
        except HTTPException:
            continue

    return {
        "success": True,
        "data": accessible_teams
    }


@app.post("/api/v1/teams")
async def create_team(
    team_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create team"""
    user_id = current_user["id"]
    organization_id = team_data.get("organization_id")

    if not organization_id:
        raise HTTPException(status_code=400, detail="organization_id required")

    check_organization_access(user_id, organization_id, ["admin", "owner"])

    team_id = str(uuid.uuid4())
    team = {
        "id": team_id,
        "name": team_data.get("name", "New Team"),
        "description": team_data.get("description", ""),
        "organization_id": organization_id,
        "created_by": user_id,
        "created_at": time.time()
    }

    teams_db[team_id] = team

    return {
        "success": True,
        "data": team,
        "message": "Team created successfully"
    }


@app.get("/api/v1/teams/{team_id}")
async def get_team_by_id(
    team_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get team by ID"""
    user_id = current_user["id"]

    team = teams_db.get(team_id)
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")

    # Check access
    try:
        check_organization_access(user_id, team["organization_id"], ["viewer", "member", "admin", "owner"])
    except HTTPException:
        raise HTTPException(status_code=403, detail="Access denied")

    # Get team members
    members = []
    for member_id, member in team_members_db.items():
        if member["team_id"] == team_id:
            user = None
            for email, u in users_db.items():
                if u["id"] == member["user_id"]:
                    user = u
                    break
            if user:
                members.append({
                    "id": user["id"],
                    "email": user["email"],
                    "first_name": user["first_name"],
                    "last_name": user["last_name"],
                    "role": member.get("role", "member")
                })

    team_with_members = team.copy()
    team_with_members["members"] = members
    team_with_members["member_count"] = len(members)

    return {
        "success": True,
        "data": team_with_members
    }


@app.put("/api/v1/teams/{team_id}")
async def update_team(
    team_id: str,
    team_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update team"""
    if team_id not in teams_db:
        raise HTTPException(status_code=404, detail="Team not found")

    team = teams_db[team_id]
    user_id = current_user["id"]

    check_organization_access(user_id, team["organization_id"], ["admin", "owner"])

    if "name" in team_data:
        team["name"] = team_data["name"]
    if "description" in team_data:
        team["description"] = team_data["description"]

    teams_db[team_id] = team

    return {
        "success": True,
        "data": team,
        "message": "Team updated successfully"
    }


@app.get("/api/v1/teams/{team_id}/members")
async def get_team_members(
    team_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get team members"""
    if team_id not in teams_db:
        raise HTTPException(status_code=404, detail="Team not found")

    team = teams_db[team_id]
    user_id = current_user["id"]

    check_organization_access(user_id, team["organization_id"], ["viewer", "member", "admin", "owner"])

    members = [member for member in team_members_db.values() if member["team_id"] == team_id]

    return {
        "success": True,
        "data": members
    }


@app.post("/api/v1/teams/{team_id}/members")
async def add_team_member(
    team_id: str,
    member_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Add team member"""
    if team_id not in teams_db:
        raise HTTPException(status_code=404, detail="Team not found")

    team = teams_db[team_id]
    user_id = current_user["id"]

    check_organization_access(user_id, team["organization_id"], ["admin", "owner"])

    member_id = str(uuid.uuid4())
    member = {
        "id": member_id,
        "team_id": team_id,
        "user_email": member_data.get("user_email"),
        "role": member_data.get("role", "member"),
        "added_by": user_id,
        "added_at": time.time()
    }

    team_members_db[member_id] = member

    return {
        "success": True,
        "data": member,
        "message": "Team member added successfully"
    }


# Analytics Endpoints
@app.get("/api/v1/analytics")
async def get_analytics_overview(current_user: dict = Depends(get_user_from_token)):
    """Get analytics overview for current user's organizations"""
    user_id = current_user["id"]

    # Get user's organizations
    user_orgs = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            user_orgs.append(member["organization_id"])

    if not user_orgs:
        return {
            "success": True,
            "data": {
                "total_projects": 0,
                "total_tasks": 0,
                "completed_tasks": 0,
                "organizations": 0,
                "completion_rate": 0
            }
        }

    # Mock analytics data
    return {
        "success": True,
        "data": {
            "total_projects": len(projects_db),
            "total_tasks": len(cards_db),
            "completed_tasks": len([c for c in cards_db.values() if c.get("status") == "completed"]),
            "organizations": len(user_orgs),
            "completion_rate": 72.0,
            "recent_activity": [
                {"type": "task_completed", "count": 3, "date": "2025-08-03"},
                {"type": "project_created", "count": 1, "date": "2025-08-02"},
                {"type": "task_created", "count": 5, "date": "2025-08-01"}
            ]
        }
    }


@app.get("/api/v1/analytics/projects/{project_id}")
async def get_project_analytics_by_id(
    project_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get analytics for a specific project"""
    user_id = current_user["id"]

    # Check if project exists and user has access
    project = projects_db.get(project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])
    except HTTPException:
        raise HTTPException(status_code=403, detail="Access denied")

    # Mock project analytics data
    return {
        "success": True,
        "data": {
            "project_id": project_id,
            "total_tasks": 12,
            "completed_tasks": 8,
            "in_progress_tasks": 3,
            "pending_tasks": 1,
            "completion_rate": 66.7,
            "average_completion_time": "2.5 days",
            "team_members": 4,
            "recent_activity": [
                {"type": "task_completed", "count": 2, "date": "2025-08-03"},
                {"type": "task_created", "count": 1, "date": "2025-08-02"}
            ],
            "task_distribution": {
                "todo": 1,
                "in_progress": 3,
                "done": 8
            }
        }
    }


@app.get("/api/v1/analytics/dashboard")
async def get_analytics_dashboard(current_user: dict = Depends(get_user_from_token)):
    """Get analytics dashboard data"""
    user_id = current_user["id"]

    # Check if user has admin/owner role in any organization
    user_orgs = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["role"] in ["admin", "owner"]:
            user_orgs.append(member["organization_id"])

    if not user_orgs:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    analytics_data = {
        "total_projects": len([p for p in projects_db.values() if p["organization_id"] in user_orgs]),
        "total_boards": len([b for b in boards_db.values() if projects_db.get(b["project_id"], {}).get("organization_id") in user_orgs]),
        "total_cards": len([c for c in cards_db.values()]),
        "active_users": len(set(member["user_id"] for member in organization_members_db.values() if member["organization_id"] in user_orgs))
    }

    return {
        "success": True,
        "data": analytics_data
    }


@app.get("/api/v1/analytics/projects")
async def get_project_analytics(current_user: dict = Depends(get_user_from_token)):
    """Get project analytics"""
    user_id = current_user["id"]

    # Check permissions
    user_orgs = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["role"] in ["admin", "owner"]:
            user_orgs.append(member["organization_id"])

    if not user_orgs:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    project_stats = []
    for project_id, project in projects_db.items():
        if project["organization_id"] in user_orgs:
            project_boards = [b for b in boards_db.values() if b["project_id"] == project_id]
            project_stats.append({
                "project_id": project_id,
                "name": project["name"],
                "boards_count": len(project_boards),
                "status": project["status"]
            })

    return {
        "success": True,
        "data": project_stats
    }


@app.get("/api/v1/analytics/users")
async def get_user_analytics(current_user: dict = Depends(get_user_from_token)):
    """Get user analytics"""
    user_id = current_user["id"]

    # Check permissions
    user_orgs = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["role"] in ["admin", "owner"]:
            user_orgs.append(member["organization_id"])

    if not user_orgs:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    user_stats = []
    for member_id, member in organization_members_db.items():
        if member["organization_id"] in user_orgs:
            user_email = None
            for email, user in users_db.items():
                if user["id"] == member["user_id"]:
                    user_email = email
                    break

            if user_email:
                user_stats.append({
                    "user_id": member["user_id"],
                    "email": user_email,
                    "role": member["role"],
                    "organization_id": member["organization_id"]
                })

    return {
        "success": True,
        "data": user_stats
    }


# Security Endpoints
@app.get("/api/v1/security/audit-logs")
async def get_audit_logs(current_user: dict = Depends(get_user_from_token)):
    """Get audit logs"""
    user_id = current_user["id"]

    # Check permissions
    user_orgs = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["role"] in ["admin", "owner"]:
            user_orgs.append(member["organization_id"])

    if not user_orgs:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    # Mock audit logs
    audit_logs = [
        {
            "id": str(uuid.uuid4()),
            "action": "user_login",
            "user_id": user_id,
            "timestamp": time.time(),
            "details": "User logged in successfully"
        },
        {
            "id": str(uuid.uuid4()),
            "action": "project_created",
            "user_id": user_id,
            "timestamp": time.time() - 3600,
            "details": "New project created"
        }
    ]

    return {
        "success": True,
        "data": audit_logs
    }


@app.get("/api/v1/security/permissions")
async def get_permissions(current_user: dict = Depends(get_user_from_token)):
    """Get user permissions"""
    user_id = current_user["id"]

    permissions = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                permissions.append({
                    "organization_id": member["organization_id"],
                    "organization_name": org["name"],
                    "role": member["role"],
                    "permissions": get_role_permissions(member["role"])
                })

    return {
        "success": True,
        "data": permissions
    }


def get_role_permissions(role: str) -> list:
    """Get permissions for a role"""
    role_permissions = {
        "owner": ["all"],
        "admin": ["manage_users", "manage_projects", "view_analytics", "manage_teams"],
        "member": ["create_projects", "manage_own_projects", "view_projects"],
        "viewer": ["view_projects", "view_boards"]
    }
    return role_permissions.get(role, [])


# AI & Automation Endpoints
@app.get("/api/v1/ai/models")
async def get_ai_models(current_user: dict = Depends(get_user_from_token)):
    """Get available AI models"""
    models = [
        {
            "id": "text-classifier",
            "name": "Text Classification Model",
            "type": "classification",
            "status": "active"
        },
        {
            "id": "priority-predictor",
            "name": "Priority Prediction Model",
            "type": "prediction",
            "status": "active"
        }
    ]

    return {
        "success": True,
        "data": models
    }


@app.get("/api/v1/ai/workflows")
async def get_ai_workflows(current_user: dict = Depends(get_user_from_token)):
    """Get AI workflows"""
    workflows = [
        {
            "id": str(uuid.uuid4()),
            "name": "Auto-assign Priority",
            "description": "Automatically assign priority to new cards",
            "status": "active"
        },
        {
            "id": str(uuid.uuid4()),
            "name": "Smart Notifications",
            "description": "Send intelligent notifications based on activity",
            "status": "active"
        }
    ]

    return {
        "success": True,
        "data": workflows
    }


@app.post("/api/v1/ai/predictions")
async def create_ai_prediction(
    prediction_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create AI prediction for a task or project"""
    try:
        # Extract prediction parameters
        entity_type = prediction_data.get("entity_type", "task")
        entity_id = prediction_data.get("entity_id")
        prediction_type = prediction_data.get("prediction_type", "priority")
        input_data = prediction_data.get("input_data", {})

        # Generate AI prediction based on type
        if prediction_type == "priority":
            # Analyze task content for priority prediction
            title = input_data.get("title", "")
            description = input_data.get("description", "")

            # AI logic for priority prediction
            priority_score = 0.5
            if any(word in title.lower() for word in ["urgent", "critical", "asap", "emergency"]):
                priority_score = 0.9
            elif any(word in title.lower() for word in ["important", "high", "priority"]):
                priority_score = 0.7
            elif any(word in title.lower() for word in ["low", "minor", "optional"]):
                priority_score = 0.3

            prediction_result = {
                "priority": "high" if priority_score > 0.7 else "medium" if priority_score > 0.4 else "low",
                "confidence": priority_score,
                "reasoning": "Based on content analysis and priority indicators"
            }

        elif prediction_type == "completion_time":
            # Estimate completion time
            complexity_score = len(input_data.get("description", "")) / 100
            estimated_hours = max(1, min(40, complexity_score * 8))

            prediction_result = {
                "estimated_hours": round(estimated_hours, 1),
                "confidence": 0.75,
                "reasoning": "Based on task complexity analysis"
            }

        else:
            prediction_result = {
                "error": f"Unknown prediction type: {prediction_type}"
            }

        # Create prediction record
        prediction = {
            "id": str(uuid.uuid4()),
            "entity_type": entity_type,
            "entity_id": entity_id,
            "prediction_type": prediction_type,
            "result": prediction_result,
            "created_at": time.time(),
            "created_by": current_user["id"]
        }

        return {
            "success": True,
            "data": prediction,
            "message": f"AI {prediction_type} prediction generated successfully"
        }

    except Exception as e:
        print(f"❌ AI prediction error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to generate AI prediction"
        }


@app.get("/api/v1/ai/insights")
async def get_ai_insights(
    entity_type: str = "project",
    entity_id: str = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get AI-generated insights for projects or tasks"""
    try:
        insights = [
            {
                "id": str(uuid.uuid4()),
                "type": "performance",
                "title": "Project Velocity Trend",
                "description": "Team velocity has increased by 15% this sprint",
                "impact": "positive",
                "confidence": 0.85,
                "recommendations": ["Continue current practices", "Consider additional tasks"]
            },
            {
                "id": str(uuid.uuid4()),
                "type": "risk",
                "title": "Potential Bottleneck",
                "description": "Tasks accumulating in Review column",
                "impact": "warning",
                "confidence": 0.75,
                "recommendations": ["Assign additional reviewers", "Set up reminders"]
            }
        ]

        return {
            "success": True,
            "data": insights,
            "message": f"Generated {len(insights)} AI insights"
        }

    except Exception as e:
        print(f"❌ AI insights error: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to generate AI insights"
        }


# Integration Endpoints
@app.get("/api/v1/integrations")
async def get_integrations(current_user: dict = Depends(get_user_from_token)):
    """Get available integrations"""
    integrations = [
        {
            "id": "slack",
            "name": "Slack",
            "type": "communication",
            "status": "available"
        },
        {
            "id": "github",
            "name": "GitHub",
            "type": "development",
            "status": "available"
        },
        {
            "id": "google-drive",
            "name": "Google Drive",
            "type": "storage",
            "status": "available"
        }
    ]

    return {
        "success": True,
        "data": integrations
    }


@app.post("/api/v1/integrations/{integration_id}/connect")
async def connect_integration(
    integration_id: str,
    connection_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Connect an integration"""
    user_id = current_user["id"]

    # Mock integration connection
    connection = {
        "id": str(uuid.uuid4()),
        "integration_id": integration_id,
        "user_id": user_id,
        "status": "connected",
        "connected_at": time.time()
    }

    return {
        "success": True,
        "data": connection,
        "message": f"Successfully connected to {integration_id}"
    }


# Bulk Operations Endpoints
@app.post("/api/v1/bulk/projects")
async def bulk_update_projects(
    bulk_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Bulk update projects"""
    user_id = current_user["id"]
    operation = bulk_data.get("operation")
    project_ids = bulk_data.get("project_ids", [])
    data = bulk_data.get("data", {})

    updated_projects = []
    for project_id in project_ids:
        if project_id in projects_db:
            project = projects_db[project_id]
            try:
                check_organization_access(user_id, project["organization_id"], ["admin", "owner"])

                if operation == "update_status" and "status" in data:
                    project["status"] = data["status"]
                    projects_db[project_id] = project
                    updated_projects.append(project)
            except HTTPException:
                continue

    return {
        "success": True,
        "data": updated_projects,
        "message": f"Bulk operation completed on {len(updated_projects)} projects"
    }


@app.post("/api/v1/bulk/cards")
async def bulk_update_cards(
    bulk_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Bulk update cards"""
    user_id = current_user["id"]
    operation = bulk_data.get("operation")
    card_ids = bulk_data.get("card_ids", [])
    data = bulk_data.get("data", {})

    updated_cards = []
    for card_id in card_ids:
        if card_id in cards_db:
            card = cards_db[card_id]
            # Check access through card -> column -> board -> project -> organization
            try:
                if card["column_id"] in columns_db:
                    column = columns_db[card["column_id"]]
                    if column["board_id"] in boards_db:
                        board = boards_db[column["board_id"]]
                        if board["project_id"] in projects_db:
                            project = projects_db[board["project_id"]]
                            check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

                            if operation == "update_priority" and "priority" in data:
                                card["priority"] = data["priority"]
                                cards_db[card_id] = card
                                updated_cards.append(card)
            except HTTPException:
                continue

    return {
        "success": True,
        "data": updated_cards,
        "message": f"Bulk operation completed on {len(updated_cards)} cards"
    }


# File Upload Endpoints
@app.post("/api/v1/upload")
async def upload_file(
    file: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Upload file endpoint"""
    if not file:
        raise HTTPException(status_code=422, detail="No file provided")

    # Mock file upload
    file_id = str(uuid.uuid4())
    file_info = {
        "id": file_id,
        "filename": "uploaded_file.txt",
        "size": 1024,
        "type": "text/plain",
        "uploaded_by": current_user["id"],
        "uploaded_at": time.time()
    }

    return {
        "success": True,
        "data": file_info,
        "message": "File uploaded successfully"
    }


# Notification Endpoints
notifications_db = {}

def create_project_notification(project_id: str, action: str, actor_user_id: str, organization_id: str):
    """Helper function to create project-related notifications"""
    import time
    import uuid

    # Get project details
    project = projects_db.get(project_id)
    if not project:
        return

    # Get organization members to notify
    org_members = []
    for member_id, member in organization_members_db.items():
        if member["organization_id"] == organization_id and member["user_id"] != actor_user_id:
            # Only notify admins and owners for project changes
            if member["role"] in ["admin", "owner"]:
                org_members.append(member["user_id"])

    # Create notification for each relevant member
    for user_id in org_members:
        notification_id = str(uuid.uuid4())

        # Get actor user details
        actor_user = None
        for email, user in users_db.items():
            if user["id"] == actor_user_id:
                actor_user = user
                break

        actor_name = f"{actor_user['first_name']} {actor_user['last_name']}" if actor_user else "Someone"

        # Create notification message based on action
        if action == "created":
            title = "New Project Created"
            message = f"{actor_name} created a new project '{project['name']}'"
        elif action == "updated":
            title = "Project Updated"
            message = f"{actor_name} updated project '{project['name']}'"
        elif action == "deleted":
            title = "Project Deleted"
            message = f"{actor_name} deleted project '{project['name']}'"
        else:
            title = "Project Activity"
            message = f"{actor_name} performed an action on project '{project['name']}'"

        notification = {
            "id": notification_id,
            "user_id": user_id,
            "type": "project_update",
            "title": title,
            "message": message,
            "data": {
                "project_id": project_id,
                "project_name": project["name"],
                "action": action,
                "actor_user_id": actor_user_id,
                "actor_name": actor_name
            },
            "read": False,
            "created_at": time.time()
        }

        notifications_db[notification_id] = notification

@app.get("/api/v1/notifications")
async def get_notifications(current_user: dict = Depends(get_user_from_token)):
    """Get user notifications"""
    user_id = current_user["id"]
    user_notifications = [n for n in notifications_db.values() if n["user_id"] == user_id]

    return {
        "success": True,
        "data": user_notifications
    }


@app.post("/api/v1/notifications")
async def create_notification(
    notification_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create a new notification"""
    notification_id = str(uuid.uuid4())
    notification = {
        "id": notification_id,
        "user_id": notification_data.get("userId"),
        "type": notification_data.get("type", "info"),
        "title": notification_data.get("title"),
        "message": notification_data.get("message"),
        "priority": notification_data.get("priority", "medium"),
        "data": notification_data.get("data", {}),
        "read": False,
        "created_at": time.time()
    }

    notifications_db[notification_id] = notification

    return {
        "success": True,
        "data": notification,
        "message": "Notification created successfully"
    }


@app.put("/api/v1/notifications/{notification_id}/read")
async def mark_notification_as_read(
    notification_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Mark notification as read"""
    if notification_id not in notifications_db:
        raise HTTPException(status_code=404, detail="Notification not found")

    notification = notifications_db[notification_id]
    if notification["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="Access denied")

    notification["read"] = True
    notification["read_at"] = time.time()

    return {
        "success": True,
        "data": notification,
        "message": "Notification marked as read"
    }


@app.put("/api/v1/notifications/read-all")
async def mark_all_notifications_as_read(current_user: dict = Depends(get_user_from_token)):
    """Mark all notifications as read"""
    user_id = current_user["id"]
    marked_count = 0

    for notification in notifications_db.values():
        if notification["user_id"] == user_id and not notification["read"]:
            notification["read"] = True
            notification["read_at"] = time.time()
            marked_count += 1

    return {
        "success": True,
        "data": {"marked_count": marked_count},
        "message": f"Marked {marked_count} notifications as read"
    }


@app.delete("/api/v1/notifications/{notification_id}")
async def delete_notification(
    notification_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Delete notification"""
    if notification_id not in notifications_db:
        raise HTTPException(status_code=404, detail="Notification not found")

    notification = notifications_db[notification_id]
    if notification["user_id"] != current_user["id"]:
        raise HTTPException(status_code=403, detail="Access denied")

    del notifications_db[notification_id]

    return {
        "success": True,
        "message": "Notification deleted successfully"
    }


@app.get("/api/v1/users/notifications/preferences")
async def get_notification_preferences(current_user: dict = Depends(get_user_from_token)):
    """Get notification preferences"""
    preferences = {
        "email_notifications": True,
        "push_notifications": True,
        "task_assignments": True,
        "project_updates": True,
        "comment_mentions": True
    }

    return {
        "success": True,
        "data": preferences
    }


@app.put("/api/v1/users/notifications/preferences")
async def update_notification_preferences(
    preferences: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update notification preferences"""
    # Mock update
    return {
        "success": True,
        "data": preferences,
        "message": "Notification preferences updated successfully"
    }


@app.get("/api/v1/users/activity")
async def get_user_activity(current_user: dict = Depends(get_user_from_token)):
    """Get user activity"""
    user_id = current_user["id"]

    # Mock activity data
    activities = [
        {
            "id": str(uuid.uuid4()),
            "type": "project_created",
            "description": "Created a new project",
            "timestamp": time.time() - 3600
        },
        {
            "id": str(uuid.uuid4()),
            "type": "card_updated",
            "description": "Updated card priority",
            "timestamp": time.time() - 7200
        }
    ]

    return {
        "success": True,
        "data": activities
    }


# Organization Settings Endpoints
@app.get("/api/v1/organizations/{org_id}/settings")
async def get_organization_settings(
    org_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get organization settings"""
    user_id = current_user["id"]
    check_organization_access(user_id, org_id, ["admin", "owner"])

    settings = {
        "allowed_domains": ["example.com"],
        "require_2fa": False,
        "project_creation_restricted": False,
        "default_role": "member"
    }

    return {
        "success": True,
        "data": settings
    }


@app.put("/api/v1/organizations/{org_id}/settings")
async def update_organization_settings(
    org_id: str,
    settings_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update organization settings"""
    user_id = current_user["id"]
    check_organization_access(user_id, org_id, ["owner"])

    # Mock settings update
    return {
        "success": True,
        "data": settings_data,
        "message": "Organization settings updated successfully"
    }


# Project Member Management
@app.get("/api/v1/projects/{project_id}/members")
async def get_project_members(
    project_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get project members"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])

    # Mock project members
    members = [
        {
            "user_id": user_id,
            "email": current_user["email"],
            "role": "owner",
            "joined_at": time.time()
        }
    ]

    return {
        "success": True,
        "data": members
    }


@app.post("/api/v1/projects/{project_id}/members")
async def add_project_member(
    project_id: str,
    member_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Add project member"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    check_organization_access(user_id, project["organization_id"], ["admin", "owner"])

    member = {
        "user_email": member_data.get("user_email"),
        "role": member_data.get("role", "member"),
        "added_by": user_id,
        "added_at": time.time()
    }

    return {
        "success": True,
        "data": member,
        "message": "Project member added successfully"
    }


@app.get("/api/v1/projects/{project_id}/activity")
async def get_project_activity(
    project_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get project activity"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])

    activities = [
        {
            "id": str(uuid.uuid4()),
            "type": "project_created",
            "description": f"Project '{project['name']}' was created",
            "timestamp": project["created_at"]
        }
    ]

    return {
        "success": True,
        "data": activities
    }


if __name__ == "__main__":
    import uvicorn
    print(f"🚀 Starting {settings.app_name} (Enhanced Mode)")
    print(f"📍 Server will be available at: http://localhost:3001")
    print(f"📚 API Documentation: http://localhost:3001/docs")
    print(f"🔧 Environment: {settings.environment}")
    print(f"📧 Email configured: {bool(settings.smtp_user and settings.smtp_pass)}")

    uvicorn.run(
        "enhanced_server:app",
        host="0.0.0.0",
        port=3001,
        reload=settings.debug,
        log_level="info"
    )
