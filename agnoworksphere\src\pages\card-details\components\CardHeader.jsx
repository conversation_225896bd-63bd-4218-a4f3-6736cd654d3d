import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CardHeader = ({ card, onTitleChange, onClose, onDelete, canEdit, canDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(card.title);

  const handleSave = () => {
    onTitleChange(title);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTitle(card.title);
    setIsEditing(false);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  return (
    <div className="flex items-start justify-between p-6 border-b border-border">
      <div className="flex items-start space-x-3 flex-1">
        <Icon name="Square" size={20} className="text-text-secondary mt-1" />
        <div className="flex-1">
          {isEditing ? (
            <div className="space-y-3">
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                onKeyDown={handleKeyPress}
                className="w-full text-xl font-semibold bg-transparent border-none outline-none focus:ring-2 focus:ring-primary rounded px-2 py-1"
                autoFocus
              />
              <div className="flex items-center space-x-2">
                <Button size="sm" onClick={handleSave}>
                  Save
                </Button>
                <Button variant="ghost" size="sm" onClick={handleCancel}>
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <h1 
                className={`text-xl font-semibold text-text-primary ${canEdit ? 'cursor-pointer hover:bg-muted rounded px-2 py-1 -mx-2 -my-1' : ''}`}
                onClick={() => canEdit && setIsEditing(true)}
              >
                {card.title}
              </h1>
              <div className="flex items-center space-x-4 text-sm text-text-secondary">
                <span>in list <span className="font-medium">{card.columnTitle}</span></span>
                <span>•</span>
                <span>Created {new Date(card.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <div className="flex items-center space-x-2 ml-4">
        {canDelete && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDelete}
            className="text-destructive hover:text-destructive hover:bg-destructive/10"
          >
            <Icon name="Trash2" size={16} />
          </Button>
        )}
        <Button variant="ghost" size="sm" onClick={onClose}>
          <Icon name="X" size={20} />
        </Button>
      </div>
    </div>
  );
};

export default CardHeader;