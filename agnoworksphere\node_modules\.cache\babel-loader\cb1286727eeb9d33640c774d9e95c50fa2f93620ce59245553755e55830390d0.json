{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\project\\\\ProjectConfigurationInterface.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport Select from '../ui/Select';\nimport Slider from '../ui/Slider';\nimport Toggle from '../ui/Toggle';\nimport DatePicker from '../ui/DatePicker';\nimport { Textarea } from '../ui/Textarea';\nimport Icon from '../AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProjectConfigurationInterface = ({\n  onNext,\n  onBack,\n  initialData = {},\n  className\n}) => {\n  _s();\n  var _priorityOptions$find;\n  const [config, setConfig] = useState({\n    budget: 50000,\n    duration: 12,\n    teamSize: 5,\n    priority: 'medium',\n    methodology: 'agile',\n    tools: {\n      projectManagement: true,\n      versionControl: true,\n      cicd: false,\n      monitoring: false,\n      testing: true,\n      documentation: true\n    },\n    resources: {\n      frontend: 2,\n      backend: 2,\n      design: 1,\n      qa: 1,\n      devops: 0\n    },\n    features: {\n      realTimeCollaboration: false,\n      aiAssistance: false,\n      advancedAnalytics: false,\n      mobileApp: false,\n      apiIntegration: true\n    },\n    ...initialData\n  });\n  const [errors, setErrors] = useState({});\n  const [isValid, setIsValid] = useState(false);\n  const methodologyOptions = [{\n    value: 'agile',\n    label: 'Agile/Scrum'\n  }, {\n    value: 'waterfall',\n    label: 'Waterfall'\n  }, {\n    value: 'kanban',\n    label: 'Kanban'\n  }, {\n    value: 'hybrid',\n    label: 'Hybrid'\n  }];\n  const priorityOptions = [{\n    value: 'low',\n    label: 'Low Priority'\n  }, {\n    value: 'medium',\n    label: 'Medium Priority'\n  }, {\n    value: 'high',\n    label: 'High Priority'\n  }, {\n    value: 'urgent',\n    label: 'Urgent'\n  }];\n  const toolCategories = [{\n    key: 'projectManagement',\n    label: 'Project Management',\n    description: 'Task tracking and team coordination'\n  }, {\n    key: 'versionControl',\n    label: 'Version Control',\n    description: 'Git-based source code management'\n  }, {\n    key: 'cicd',\n    label: 'CI/CD Pipeline',\n    description: 'Automated testing and deployment'\n  }, {\n    key: 'monitoring',\n    label: 'Monitoring & Logging',\n    description: 'Application performance monitoring'\n  }, {\n    key: 'testing',\n    label: 'Testing Framework',\n    description: 'Automated testing tools'\n  }, {\n    key: 'documentation',\n    label: 'Documentation',\n    description: 'Technical documentation platform'\n  }];\n  const resourceTypes = [{\n    key: 'frontend',\n    label: 'Frontend Developers',\n    max: 10\n  }, {\n    key: 'backend',\n    label: 'Backend Developers',\n    max: 10\n  }, {\n    key: 'design',\n    label: 'UI/UX Designers',\n    max: 5\n  }, {\n    key: 'qa',\n    label: 'QA Engineers',\n    max: 5\n  }, {\n    key: 'devops',\n    label: 'DevOps Engineers',\n    max: 3\n  }];\n  const featureOptions = [{\n    key: 'realTimeCollaboration',\n    label: 'Real-time Collaboration',\n    description: 'Live editing and updates'\n  }, {\n    key: 'aiAssistance',\n    label: 'AI Assistance',\n    description: 'AI-powered project insights'\n  }, {\n    key: 'advancedAnalytics',\n    label: 'Advanced Analytics',\n    description: 'Detailed project metrics'\n  }, {\n    key: 'mobileApp',\n    label: 'Mobile Application',\n    description: 'Native mobile app development'\n  }, {\n    key: 'apiIntegration',\n    label: 'API Integration',\n    description: 'Third-party service integration'\n  }];\n  useEffect(() => {\n    validateConfiguration();\n  }, [config]);\n  const validateConfiguration = () => {\n    const newErrors = {};\n    if (config.budget < 1000) {\n      newErrors.budget = 'Budget must be at least $1,000';\n    }\n    if (config.duration < 1) {\n      newErrors.duration = 'Duration must be at least 1 week';\n    }\n    const totalResources = Object.values(config.resources).reduce((sum, count) => sum + count, 0);\n    if (totalResources === 0) {\n      newErrors.resources = 'At least one team member is required';\n    }\n    setErrors(newErrors);\n    setIsValid(Object.keys(newErrors).length === 0);\n  };\n  const updateConfig = (path, value) => {\n    setConfig(prev => {\n      const newConfig = {\n        ...prev\n      };\n      const keys = path.split('.');\n      let current = newConfig;\n      for (let i = 0; i < keys.length - 1; i++) {\n        current = current[keys[i]];\n      }\n      current[keys[keys.length - 1]] = value;\n      return newConfig;\n    });\n  };\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n  const formatDuration = weeks => {\n    if (weeks < 4) return `${weeks} week${weeks !== 1 ? 's' : ''}`;\n    const months = Math.round(weeks / 4.33);\n    return `${months} month${months !== 1 ? 's' : ''}`;\n  };\n  const handleNext = () => {\n    if (isValid) {\n      onNext === null || onNext === void 0 ? void 0 : onNext(config);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn(\"max-w-4xl mx-auto p-6 space-y-8\", className),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-3xl font-bold text-foreground\",\n        children: \"Project Configuration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-muted-foreground\",\n        children: \"Customize your project settings and resource allocation\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"DollarSign\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), \"Budget & Timeline\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(Slider, {\n          label: \"Project Budget\",\n          min: 1000,\n          max: 500000,\n          step: 1000,\n          value: config.budget,\n          onChange: value => updateConfig('budget', value),\n          formatValue: formatCurrency,\n          description: \"Total project budget allocation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Slider, {\n          label: \"Project Duration\",\n          min: 1,\n          max: 52,\n          step: 1,\n          value: config.duration,\n          onChange: value => updateConfig('duration', value),\n          formatValue: formatDuration,\n          description: \"Estimated project timeline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), errors.budget && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-destructive text-sm\",\n        children: errors.budget\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 27\n      }, this), errors.duration && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-destructive text-sm\",\n        children: errors.duration\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 29\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Settings\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), \"Project Approach\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(Select, {\n          label: \"Development Methodology\",\n          value: config.methodology,\n          onValueChange: value => updateConfig('methodology', value),\n          options: methodologyOptions,\n          description: \"Choose your preferred development approach\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          label: \"Project Priority\",\n          value: config.priority,\n          onValueChange: value => updateConfig('priority', value),\n          options: priorityOptions,\n          description: \"Set the project priority level\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Users\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), \"Team Resources\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n        children: resourceTypes.map(({\n          key,\n          label,\n          max\n        }) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: /*#__PURE__*/_jsxDEV(Slider, {\n            label: label,\n            min: 0,\n            max: max,\n            step: 1,\n            value: config.resources[key],\n            onChange: value => updateConfig(`resources.${key}`, value),\n            description: `0-${max} team members`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), errors.resources && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-destructive text-sm\",\n        children: errors.resources\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 30\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-secondary/50 rounded-lg p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium\",\n            children: \"Total Team Size:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-lg font-bold text-primary\",\n            children: [Object.values(config.resources).reduce((sum, count) => sum + count, 0), \" members\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Wrench\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), \"Development Tools\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: toolCategories.map(({\n          key,\n          label,\n          description\n        }) => /*#__PURE__*/_jsxDEV(Toggle, {\n          label: label,\n          description: description,\n          checked: config.tools[key],\n          onChange: checked => updateConfig(`tools.${key}`, checked)\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Zap\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), \"Advanced Features\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: featureOptions.map(({\n          key,\n          label,\n          description\n        }) => /*#__PURE__*/_jsxDEV(Toggle, {\n          label: label,\n          description: description,\n          checked: config.features[key],\n          onChange: checked => updateConfig(`features.${key}`, checked)\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6 border border-border\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-foreground mb-4 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Eye\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), \"Configuration Preview\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-foreground\",\n            children: \"Budget & Timeline\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-muted-foreground\",\n            children: [\"Budget: \", formatCurrency(config.budget), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 54\n            }, this), \"Duration: \", formatDuration(config.duration), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 58\n            }, this), \"Priority: \", (_priorityOptions$find = priorityOptions.find(p => p.value === config.priority)) === null || _priorityOptions$find === void 0 ? void 0 : _priorityOptions$find.label]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-foreground\",\n            children: \"Team Composition\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-muted-foreground\",\n            children: resourceTypes.map(({\n              key,\n              label\n            }) => config.resources[key] > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [label, \": \", config.resources[key]]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-foreground\",\n            children: \"Selected Tools\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-muted-foreground\",\n            children: toolCategories.filter(({\n              key\n            }) => config.tools[key]).map(({\n              label\n            }) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u2022 \", label]\n            }, label, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center pt-6\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        onClick: onBack,\n        iconName: \"ArrowLeft\",\n        iconPosition: \"left\",\n        children: \"Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-muted-foreground\",\n          children: \"Step 1 of 6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          disabled: !isValid,\n          iconName: \"ArrowRight\",\n          iconPosition: \"right\",\n          children: \"Continue to Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 152,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectConfigurationInterface, \"N9d2teyoD4WYS6rlBq53sAB0PCs=\");\n_c = ProjectConfigurationInterface;\nexport default ProjectConfigurationInterface;\nvar _c;\n$RefreshReg$(_c, \"ProjectConfigurationInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "cn", "<PERSON><PERSON>", "Input", "Select", "Slide<PERSON>", "Toggle", "DatePicker", "Textarea", "Icon", "jsxDEV", "_jsxDEV", "ProjectConfigurationInterface", "onNext", "onBack", "initialData", "className", "_s", "_priorityOptions$find", "config", "setConfig", "budget", "duration", "teamSize", "priority", "methodology", "tools", "projectManagement", "versionControl", "cicd", "monitoring", "testing", "documentation", "resources", "frontend", "backend", "design", "qa", "devops", "features", "realTimeCollaboration", "aiAssistance", "advancedAnalytics", "mobileApp", "apiIntegration", "errors", "setErrors", "<PERSON><PERSON><PERSON><PERSON>", "setIsValid", "methodologyOptions", "value", "label", "priorityOptions", "toolCategories", "key", "description", "resourceTypes", "max", "featureOptions", "validateConfiguration", "newErrors", "totalResources", "Object", "values", "reduce", "sum", "count", "keys", "length", "updateConfig", "path", "prev", "newConfig", "split", "current", "i", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDuration", "weeks", "months", "Math", "round", "handleNext", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "min", "step", "onChange", "formatValue", "onValueChange", "options", "map", "checked", "find", "p", "filter", "variant", "onClick", "iconName", "iconPosition", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/project/ProjectConfigurationInterface.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport Select from '../ui/Select';\nimport Slider from '../ui/Slider';\nimport Toggle from '../ui/Toggle';\nimport DatePicker from '../ui/DatePicker';\nimport { Textarea } from '../ui/Textarea';\nimport Icon from '../AppIcon';\n\nconst ProjectConfigurationInterface = ({ \n  onNext, \n  onBack, \n  initialData = {}, \n  className \n}) => {\n  const [config, setConfig] = useState({\n    budget: 50000,\n    duration: 12,\n    teamSize: 5,\n    priority: 'medium',\n    methodology: 'agile',\n    tools: {\n      projectManagement: true,\n      versionControl: true,\n      cicd: false,\n      monitoring: false,\n      testing: true,\n      documentation: true\n    },\n    resources: {\n      frontend: 2,\n      backend: 2,\n      design: 1,\n      qa: 1,\n      devops: 0\n    },\n    features: {\n      realTimeCollaboration: false,\n      aiAssistance: false,\n      advancedAnalytics: false,\n      mobileApp: false,\n      apiIntegration: true\n    },\n    ...initialData\n  });\n\n  const [errors, setErrors] = useState({});\n  const [isValid, setIsValid] = useState(false);\n\n  const methodologyOptions = [\n    { value: 'agile', label: 'Agile/Scrum' },\n    { value: 'waterfall', label: 'Waterfall' },\n    { value: 'kanban', label: 'Kanban' },\n    { value: 'hybrid', label: 'Hybrid' }\n  ];\n\n  const priorityOptions = [\n    { value: 'low', label: 'Low Priority' },\n    { value: 'medium', label: 'Medium Priority' },\n    { value: 'high', label: 'High Priority' },\n    { value: 'urgent', label: 'Urgent' }\n  ];\n\n  const toolCategories = [\n    { key: 'projectManagement', label: 'Project Management', description: 'Task tracking and team coordination' },\n    { key: 'versionControl', label: 'Version Control', description: 'Git-based source code management' },\n    { key: 'cicd', label: 'CI/CD Pipeline', description: 'Automated testing and deployment' },\n    { key: 'monitoring', label: 'Monitoring & Logging', description: 'Application performance monitoring' },\n    { key: 'testing', label: 'Testing Framework', description: 'Automated testing tools' },\n    { key: 'documentation', label: 'Documentation', description: 'Technical documentation platform' }\n  ];\n\n  const resourceTypes = [\n    { key: 'frontend', label: 'Frontend Developers', max: 10 },\n    { key: 'backend', label: 'Backend Developers', max: 10 },\n    { key: 'design', label: 'UI/UX Designers', max: 5 },\n    { key: 'qa', label: 'QA Engineers', max: 5 },\n    { key: 'devops', label: 'DevOps Engineers', max: 3 }\n  ];\n\n  const featureOptions = [\n    { key: 'realTimeCollaboration', label: 'Real-time Collaboration', description: 'Live editing and updates' },\n    { key: 'aiAssistance', label: 'AI Assistance', description: 'AI-powered project insights' },\n    { key: 'advancedAnalytics', label: 'Advanced Analytics', description: 'Detailed project metrics' },\n    { key: 'mobileApp', label: 'Mobile Application', description: 'Native mobile app development' },\n    { key: 'apiIntegration', label: 'API Integration', description: 'Third-party service integration' }\n  ];\n\n  useEffect(() => {\n    validateConfiguration();\n  }, [config]);\n\n  const validateConfiguration = () => {\n    const newErrors = {};\n    \n    if (config.budget < 1000) {\n      newErrors.budget = 'Budget must be at least $1,000';\n    }\n    \n    if (config.duration < 1) {\n      newErrors.duration = 'Duration must be at least 1 week';\n    }\n    \n    const totalResources = Object.values(config.resources).reduce((sum, count) => sum + count, 0);\n    if (totalResources === 0) {\n      newErrors.resources = 'At least one team member is required';\n    }\n\n    setErrors(newErrors);\n    setIsValid(Object.keys(newErrors).length === 0);\n  };\n\n  const updateConfig = (path, value) => {\n    setConfig(prev => {\n      const newConfig = { ...prev };\n      const keys = path.split('.');\n      let current = newConfig;\n      \n      for (let i = 0; i < keys.length - 1; i++) {\n        current = current[keys[i]];\n      }\n      \n      current[keys[keys.length - 1]] = value;\n      return newConfig;\n    });\n  };\n\n  const formatCurrency = (value) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value);\n  };\n\n  const formatDuration = (weeks) => {\n    if (weeks < 4) return `${weeks} week${weeks !== 1 ? 's' : ''}`;\n    const months = Math.round(weeks / 4.33);\n    return `${months} month${months !== 1 ? 's' : ''}`;\n  };\n\n  const handleNext = () => {\n    if (isValid) {\n      onNext?.(config);\n    }\n  };\n\n  return (\n    <div className={cn(\"max-w-4xl mx-auto p-6 space-y-8\", className)}>\n      {/* Header */}\n      <div className=\"text-center space-y-2\">\n        <h2 className=\"text-3xl font-bold text-foreground\">Project Configuration</h2>\n        <p className=\"text-muted-foreground\">\n          Customize your project settings and resource allocation\n        </p>\n      </div>\n\n      {/* Budget & Timeline Section */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-6\">\n        <h3 className=\"text-xl font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"DollarSign\" className=\"h-5 w-5\" />\n          Budget & Timeline\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <Slider\n            label=\"Project Budget\"\n            min={1000}\n            max={500000}\n            step={1000}\n            value={config.budget}\n            onChange={(value) => updateConfig('budget', value)}\n            formatValue={formatCurrency}\n            description=\"Total project budget allocation\"\n          />\n          \n          <Slider\n            label=\"Project Duration\"\n            min={1}\n            max={52}\n            step={1}\n            value={config.duration}\n            onChange={(value) => updateConfig('duration', value)}\n            formatValue={formatDuration}\n            description=\"Estimated project timeline\"\n          />\n        </div>\n        \n        {errors.budget && <p className=\"text-destructive text-sm\">{errors.budget}</p>}\n        {errors.duration && <p className=\"text-destructive text-sm\">{errors.duration}</p>}\n      </div>\n\n      {/* Methodology & Priority Section */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-6\">\n        <h3 className=\"text-xl font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"Settings\" className=\"h-5 w-5\" />\n          Project Approach\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <Select\n            label=\"Development Methodology\"\n            value={config.methodology}\n            onValueChange={(value) => updateConfig('methodology', value)}\n            options={methodologyOptions}\n            description=\"Choose your preferred development approach\"\n          />\n          \n          <Select\n            label=\"Project Priority\"\n            value={config.priority}\n            onValueChange={(value) => updateConfig('priority', value)}\n            options={priorityOptions}\n            description=\"Set the project priority level\"\n          />\n        </div>\n      </div>\n\n      {/* Team Resources Section */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-6\">\n        <h3 className=\"text-xl font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"Users\" className=\"h-5 w-5\" />\n          Team Resources\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {resourceTypes.map(({ key, label, max }) => (\n            <div key={key} className=\"space-y-2\">\n              <Slider\n                label={label}\n                min={0}\n                max={max}\n                step={1}\n                value={config.resources[key]}\n                onChange={(value) => updateConfig(`resources.${key}`, value)}\n                description={`0-${max} team members`}\n              />\n            </div>\n          ))}\n        </div>\n\n        {errors.resources && <p className=\"text-destructive text-sm\">{errors.resources}</p>}\n\n        <div className=\"bg-secondary/50 rounded-lg p-4\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm font-medium\">Total Team Size:</span>\n            <span className=\"text-lg font-bold text-primary\">\n              {Object.values(config.resources).reduce((sum, count) => sum + count, 0)} members\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Tools & Technologies Section */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-6\">\n        <h3 className=\"text-xl font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"Wrench\" className=\"h-5 w-5\" />\n          Development Tools\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {toolCategories.map(({ key, label, description }) => (\n            <Toggle\n              key={key}\n              label={label}\n              description={description}\n              checked={config.tools[key]}\n              onChange={(checked) => updateConfig(`tools.${key}`, checked)}\n            />\n          ))}\n        </div>\n      </div>\n\n      {/* Advanced Features Section */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-6\">\n        <h3 className=\"text-xl font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"Zap\" className=\"h-5 w-5\" />\n          Advanced Features\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {featureOptions.map(({ key, label, description }) => (\n            <Toggle\n              key={key}\n              label={label}\n              description={description}\n              checked={config.features[key]}\n              onChange={(checked) => updateConfig(`features.${key}`, checked)}\n            />\n          ))}\n        </div>\n      </div>\n\n      {/* Configuration Preview */}\n      <div className=\"bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6 border border-border\">\n        <h3 className=\"text-xl font-semibold text-foreground mb-4 flex items-center gap-2\">\n          <Icon name=\"Eye\" className=\"h-5 w-5\" />\n          Configuration Preview\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n          <div className=\"space-y-2\">\n            <div className=\"font-medium text-foreground\">Budget & Timeline</div>\n            <div className=\"text-muted-foreground\">\n              Budget: {formatCurrency(config.budget)}<br/>\n              Duration: {formatDuration(config.duration)}<br/>\n              Priority: {priorityOptions.find(p => p.value === config.priority)?.label}\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"font-medium text-foreground\">Team Composition</div>\n            <div className=\"text-muted-foreground\">\n              {resourceTypes.map(({ key, label }) =>\n                config.resources[key] > 0 && (\n                  <div key={key}>{label}: {config.resources[key]}</div>\n                )\n              )}\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"font-medium text-foreground\">Selected Tools</div>\n            <div className=\"text-muted-foreground\">\n              {toolCategories.filter(({ key }) => config.tools[key]).map(({ label }) => (\n                <div key={label}>• {label}</div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex justify-between items-center pt-6\">\n        <Button\n          variant=\"outline\"\n          onClick={onBack}\n          iconName=\"ArrowLeft\"\n          iconPosition=\"left\"\n        >\n          Back\n        </Button>\n\n        <div className=\"flex items-center gap-2\">\n          <div className=\"text-sm text-muted-foreground\">\n            Step 1 of 6\n          </div>\n          <Button\n            onClick={handleNext}\n            disabled={!isValid}\n            iconName=\"ArrowRight\"\n            iconPosition=\"right\"\n          >\n            Continue to Overview\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProjectConfigurationInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,EAAE,QAAQ,gBAAgB;AACnC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,6BAA6B,GAAGA,CAAC;EACrCC,MAAM;EACNC,MAAM;EACNC,WAAW,GAAG,CAAC,CAAC;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACJ,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC;IACnCsB,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE,OAAO;IACpBC,KAAK,EAAE;MACLC,iBAAiB,EAAE,IAAI;MACvBC,cAAc,EAAE,IAAI;MACpBC,IAAI,EAAE,KAAK;MACXC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE,IAAI;MACbC,aAAa,EAAE;IACjB,CAAC;IACDC,SAAS,EAAE;MACTC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTC,EAAE,EAAE,CAAC;MACLC,MAAM,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACRC,qBAAqB,EAAE,KAAK;MAC5BC,YAAY,EAAE,KAAK;MACnBC,iBAAiB,EAAE,KAAK;MACxBC,SAAS,EAAE,KAAK;MAChBC,cAAc,EAAE;IAClB,CAAC;IACD,GAAG7B;EACL,CAAC,CAAC;EAEF,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMkD,kBAAkB,GAAG,CACzB;IAAEC,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAc,CAAC,EACxC;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,CACrC;EAED,MAAMC,eAAe,GAAG,CACtB;IAAEF,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAe,CAAC,EACvC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAkB,CAAC,EAC7C;IAAED,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACzC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,CACrC;EAED,MAAME,cAAc,GAAG,CACrB;IAAEC,GAAG,EAAE,mBAAmB;IAAEH,KAAK,EAAE,oBAAoB;IAAEI,WAAW,EAAE;EAAsC,CAAC,EAC7G;IAAED,GAAG,EAAE,gBAAgB;IAAEH,KAAK,EAAE,iBAAiB;IAAEI,WAAW,EAAE;EAAmC,CAAC,EACpG;IAAED,GAAG,EAAE,MAAM;IAAEH,KAAK,EAAE,gBAAgB;IAAEI,WAAW,EAAE;EAAmC,CAAC,EACzF;IAAED,GAAG,EAAE,YAAY;IAAEH,KAAK,EAAE,sBAAsB;IAAEI,WAAW,EAAE;EAAqC,CAAC,EACvG;IAAED,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE,mBAAmB;IAAEI,WAAW,EAAE;EAA0B,CAAC,EACtF;IAAED,GAAG,EAAE,eAAe;IAAEH,KAAK,EAAE,eAAe;IAAEI,WAAW,EAAE;EAAmC,CAAC,CAClG;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEF,GAAG,EAAE,UAAU;IAAEH,KAAK,EAAE,qBAAqB;IAAEM,GAAG,EAAE;EAAG,CAAC,EAC1D;IAAEH,GAAG,EAAE,SAAS;IAAEH,KAAK,EAAE,oBAAoB;IAAEM,GAAG,EAAE;EAAG,CAAC,EACxD;IAAEH,GAAG,EAAE,QAAQ;IAAEH,KAAK,EAAE,iBAAiB;IAAEM,GAAG,EAAE;EAAE,CAAC,EACnD;IAAEH,GAAG,EAAE,IAAI;IAAEH,KAAK,EAAE,cAAc;IAAEM,GAAG,EAAE;EAAE,CAAC,EAC5C;IAAEH,GAAG,EAAE,QAAQ;IAAEH,KAAK,EAAE,kBAAkB;IAAEM,GAAG,EAAE;EAAE,CAAC,CACrD;EAED,MAAMC,cAAc,GAAG,CACrB;IAAEJ,GAAG,EAAE,uBAAuB;IAAEH,KAAK,EAAE,yBAAyB;IAAEI,WAAW,EAAE;EAA2B,CAAC,EAC3G;IAAED,GAAG,EAAE,cAAc;IAAEH,KAAK,EAAE,eAAe;IAAEI,WAAW,EAAE;EAA8B,CAAC,EAC3F;IAAED,GAAG,EAAE,mBAAmB;IAAEH,KAAK,EAAE,oBAAoB;IAAEI,WAAW,EAAE;EAA2B,CAAC,EAClG;IAAED,GAAG,EAAE,WAAW;IAAEH,KAAK,EAAE,oBAAoB;IAAEI,WAAW,EAAE;EAAgC,CAAC,EAC/F;IAAED,GAAG,EAAE,gBAAgB;IAAEH,KAAK,EAAE,iBAAiB;IAAEI,WAAW,EAAE;EAAkC,CAAC,CACpG;EAEDvD,SAAS,CAAC,MAAM;IACd2D,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAACxC,MAAM,CAAC,CAAC;EAEZ,MAAMwC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAIzC,MAAM,CAACE,MAAM,GAAG,IAAI,EAAE;MACxBuC,SAAS,CAACvC,MAAM,GAAG,gCAAgC;IACrD;IAEA,IAAIF,MAAM,CAACG,QAAQ,GAAG,CAAC,EAAE;MACvBsC,SAAS,CAACtC,QAAQ,GAAG,kCAAkC;IACzD;IAEA,MAAMuC,cAAc,GAAGC,MAAM,CAACC,MAAM,CAAC5C,MAAM,CAACc,SAAS,CAAC,CAAC+B,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;IAC7F,IAAIL,cAAc,KAAK,CAAC,EAAE;MACxBD,SAAS,CAAC3B,SAAS,GAAG,sCAAsC;IAC9D;IAEAa,SAAS,CAACc,SAAS,CAAC;IACpBZ,UAAU,CAACc,MAAM,CAACK,IAAI,CAACP,SAAS,CAAC,CAACQ,MAAM,KAAK,CAAC,CAAC;EACjD,CAAC;EAED,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEpB,KAAK,KAAK;IACpC9B,SAAS,CAACmD,IAAI,IAAI;MAChB,MAAMC,SAAS,GAAG;QAAE,GAAGD;MAAK,CAAC;MAC7B,MAAMJ,IAAI,GAAGG,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;MAC5B,IAAIC,OAAO,GAAGF,SAAS;MAEvB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACC,MAAM,GAAG,CAAC,EAAEO,CAAC,EAAE,EAAE;QACxCD,OAAO,GAAGA,OAAO,CAACP,IAAI,CAACQ,CAAC,CAAC,CAAC;MAC5B;MAEAD,OAAO,CAACP,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGlB,KAAK;MACtC,OAAOsB,SAAS;IAClB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,cAAc,GAAI1B,KAAK,IAAK;IAChC,OAAO,IAAI2B,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACjC,KAAK,CAAC;EAClB,CAAC;EAED,MAAMkC,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,GAAG,CAAC,EAAE,OAAO,GAAGA,KAAK,QAAQA,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IAC9D,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAG,IAAI,CAAC;IACvC,OAAO,GAAGC,MAAM,SAASA,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;EACpD,CAAC;EAED,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI1C,OAAO,EAAE;MACXlC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGM,MAAM,CAAC;IAClB;EACF,CAAC;EAED,oBACER,OAAA;IAAKK,SAAS,EAAEf,EAAE,CAAC,iCAAiC,EAAEe,SAAS,CAAE;IAAA0E,QAAA,gBAE/D/E,OAAA;MAAKK,SAAS,EAAC,uBAAuB;MAAA0E,QAAA,gBACpC/E,OAAA;QAAIK,SAAS,EAAC,oCAAoC;QAAA0E,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7EnF,OAAA;QAAGK,SAAS,EAAC,uBAAuB;QAAA0E,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnF,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAA0E,QAAA,gBACpE/E,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAA0E,QAAA,gBAC3E/E,OAAA,CAACF,IAAI;UAACsF,IAAI,EAAC,YAAY;UAAC/E,SAAS,EAAC;QAAS;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAEhD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnF,OAAA;QAAKK,SAAS,EAAC,uCAAuC;QAAA0E,QAAA,gBACpD/E,OAAA,CAACN,MAAM;UACL8C,KAAK,EAAC,gBAAgB;UACtB6C,GAAG,EAAE,IAAK;UACVvC,GAAG,EAAE,MAAO;UACZwC,IAAI,EAAE,IAAK;UACX/C,KAAK,EAAE/B,MAAM,CAACE,MAAO;UACrB6E,QAAQ,EAAGhD,KAAK,IAAKmB,YAAY,CAAC,QAAQ,EAAEnB,KAAK,CAAE;UACnDiD,WAAW,EAAEvB,cAAe;UAC5BrB,WAAW,EAAC;QAAiC;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eAEFnF,OAAA,CAACN,MAAM;UACL8C,KAAK,EAAC,kBAAkB;UACxB6C,GAAG,EAAE,CAAE;UACPvC,GAAG,EAAE,EAAG;UACRwC,IAAI,EAAE,CAAE;UACR/C,KAAK,EAAE/B,MAAM,CAACG,QAAS;UACvB4E,QAAQ,EAAGhD,KAAK,IAAKmB,YAAY,CAAC,UAAU,EAAEnB,KAAK,CAAE;UACrDiD,WAAW,EAAEf,cAAe;UAC5B7B,WAAW,EAAC;QAA4B;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELjD,MAAM,CAACxB,MAAM,iBAAIV,OAAA;QAAGK,SAAS,EAAC,0BAA0B;QAAA0E,QAAA,EAAE7C,MAAM,CAACxB;MAAM;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC5EjD,MAAM,CAACvB,QAAQ,iBAAIX,OAAA;QAAGK,SAAS,EAAC,0BAA0B;QAAA0E,QAAA,EAAE7C,MAAM,CAACvB;MAAQ;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9E,CAAC,eAGNnF,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAA0E,QAAA,gBACpE/E,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAA0E,QAAA,gBAC3E/E,OAAA,CAACF,IAAI;UAACsF,IAAI,EAAC,UAAU;UAAC/E,SAAS,EAAC;QAAS;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE9C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnF,OAAA;QAAKK,SAAS,EAAC,uCAAuC;QAAA0E,QAAA,gBACpD/E,OAAA,CAACP,MAAM;UACL+C,KAAK,EAAC,yBAAyB;UAC/BD,KAAK,EAAE/B,MAAM,CAACM,WAAY;UAC1B2E,aAAa,EAAGlD,KAAK,IAAKmB,YAAY,CAAC,aAAa,EAAEnB,KAAK,CAAE;UAC7DmD,OAAO,EAAEpD,kBAAmB;UAC5BM,WAAW,EAAC;QAA4C;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eAEFnF,OAAA,CAACP,MAAM;UACL+C,KAAK,EAAC,kBAAkB;UACxBD,KAAK,EAAE/B,MAAM,CAACK,QAAS;UACvB4E,aAAa,EAAGlD,KAAK,IAAKmB,YAAY,CAAC,UAAU,EAAEnB,KAAK,CAAE;UAC1DmD,OAAO,EAAEjD,eAAgB;UACzBG,WAAW,EAAC;QAAgC;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAA0E,QAAA,gBACpE/E,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAA0E,QAAA,gBAC3E/E,OAAA,CAACF,IAAI;UAACsF,IAAI,EAAC,OAAO;UAAC/E,SAAS,EAAC;QAAS;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kBAE3C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnF,OAAA;QAAKK,SAAS,EAAC,sDAAsD;QAAA0E,QAAA,EAClElC,aAAa,CAAC8C,GAAG,CAAC,CAAC;UAAEhD,GAAG;UAAEH,KAAK;UAAEM;QAAI,CAAC,kBACrC9C,OAAA;UAAeK,SAAS,EAAC,WAAW;UAAA0E,QAAA,eAClC/E,OAAA,CAACN,MAAM;YACL8C,KAAK,EAAEA,KAAM;YACb6C,GAAG,EAAE,CAAE;YACPvC,GAAG,EAAEA,GAAI;YACTwC,IAAI,EAAE,CAAE;YACR/C,KAAK,EAAE/B,MAAM,CAACc,SAAS,CAACqB,GAAG,CAAE;YAC7B4C,QAAQ,EAAGhD,KAAK,IAAKmB,YAAY,CAAC,aAAaf,GAAG,EAAE,EAAEJ,KAAK,CAAE;YAC7DK,WAAW,EAAE,KAAKE,GAAG;UAAgB;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC,GATMxC,GAAG;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUR,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELjD,MAAM,CAACZ,SAAS,iBAAItB,OAAA;QAAGK,SAAS,EAAC,0BAA0B;QAAA0E,QAAA,EAAE7C,MAAM,CAACZ;MAAS;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEnFnF,OAAA;QAAKK,SAAS,EAAC,gCAAgC;QAAA0E,QAAA,eAC7C/E,OAAA;UAAKK,SAAS,EAAC,mCAAmC;UAAA0E,QAAA,gBAChD/E,OAAA;YAAMK,SAAS,EAAC,qBAAqB;YAAA0E,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7DnF,OAAA;YAAMK,SAAS,EAAC,gCAAgC;YAAA0E,QAAA,GAC7C5B,MAAM,CAACC,MAAM,CAAC5C,MAAM,CAACc,SAAS,CAAC,CAAC+B,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,EAAC,UAC1E;UAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAA0E,QAAA,gBACpE/E,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAA0E,QAAA,gBAC3E/E,OAAA,CAACF,IAAI;UAACsF,IAAI,EAAC,QAAQ;UAAC/E,SAAS,EAAC;QAAS;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAE5C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnF,OAAA;QAAKK,SAAS,EAAC,uCAAuC;QAAA0E,QAAA,EACnDrC,cAAc,CAACiD,GAAG,CAAC,CAAC;UAAEhD,GAAG;UAAEH,KAAK;UAAEI;QAAY,CAAC,kBAC9C5C,OAAA,CAACL,MAAM;UAEL6C,KAAK,EAAEA,KAAM;UACbI,WAAW,EAAEA,WAAY;UACzBgD,OAAO,EAAEpF,MAAM,CAACO,KAAK,CAAC4B,GAAG,CAAE;UAC3B4C,QAAQ,EAAGK,OAAO,IAAKlC,YAAY,CAAC,SAASf,GAAG,EAAE,EAAEiD,OAAO;QAAE,GAJxDjD,GAAG;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKT,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAA0E,QAAA,gBACpE/E,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAA0E,QAAA,gBAC3E/E,OAAA,CAACF,IAAI;UAACsF,IAAI,EAAC,KAAK;UAAC/E,SAAS,EAAC;QAAS;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAEzC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnF,OAAA;QAAKK,SAAS,EAAC,uCAAuC;QAAA0E,QAAA,EACnDhC,cAAc,CAAC4C,GAAG,CAAC,CAAC;UAAEhD,GAAG;UAAEH,KAAK;UAAEI;QAAY,CAAC,kBAC9C5C,OAAA,CAACL,MAAM;UAEL6C,KAAK,EAAEA,KAAM;UACbI,WAAW,EAAEA,WAAY;UACzBgD,OAAO,EAAEpF,MAAM,CAACoB,QAAQ,CAACe,GAAG,CAAE;UAC9B4C,QAAQ,EAAGK,OAAO,IAAKlC,YAAY,CAAC,YAAYf,GAAG,EAAE,EAAEiD,OAAO;QAAE,GAJ3DjD,GAAG;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKT,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAKK,SAAS,EAAC,sFAAsF;MAAA0E,QAAA,gBACnG/E,OAAA;QAAIK,SAAS,EAAC,oEAAoE;QAAA0E,QAAA,gBAChF/E,OAAA,CAACF,IAAI;UAACsF,IAAI,EAAC,KAAK;UAAC/E,SAAS,EAAC;QAAS;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,yBAEzC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnF,OAAA;QAAKK,SAAS,EAAC,+CAA+C;QAAA0E,QAAA,gBAC5D/E,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAA0E,QAAA,gBACxB/E,OAAA;YAAKK,SAAS,EAAC,6BAA6B;YAAA0E,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpEnF,OAAA;YAAKK,SAAS,EAAC,uBAAuB;YAAA0E,QAAA,GAAC,UAC7B,EAACd,cAAc,CAACzD,MAAM,CAACE,MAAM,CAAC,eAACV,OAAA;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,cAClC,EAACV,cAAc,CAACjE,MAAM,CAACG,QAAQ,CAAC,eAACX,OAAA;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,cACtC,GAAA5E,qBAAA,GAACkC,eAAe,CAACoD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvD,KAAK,KAAK/B,MAAM,CAACK,QAAQ,CAAC,cAAAN,qBAAA,uBAAtDA,qBAAA,CAAwDiC,KAAK;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnF,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAA0E,QAAA,gBACxB/E,OAAA;YAAKK,SAAS,EAAC,6BAA6B;YAAA0E,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnEnF,OAAA;YAAKK,SAAS,EAAC,uBAAuB;YAAA0E,QAAA,EACnClC,aAAa,CAAC8C,GAAG,CAAC,CAAC;cAAEhD,GAAG;cAAEH;YAAM,CAAC,KAChChC,MAAM,CAACc,SAAS,CAACqB,GAAG,CAAC,GAAG,CAAC,iBACvB3C,OAAA;cAAA+E,QAAA,GAAgBvC,KAAK,EAAC,IAAE,EAAChC,MAAM,CAACc,SAAS,CAACqB,GAAG,CAAC;YAAA,GAApCA,GAAG;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuC,CAExD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnF,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAA0E,QAAA,gBACxB/E,OAAA;YAAKK,SAAS,EAAC,6BAA6B;YAAA0E,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjEnF,OAAA;YAAKK,SAAS,EAAC,uBAAuB;YAAA0E,QAAA,EACnCrC,cAAc,CAACqD,MAAM,CAAC,CAAC;cAAEpD;YAAI,CAAC,KAAKnC,MAAM,CAACO,KAAK,CAAC4B,GAAG,CAAC,CAAC,CAACgD,GAAG,CAAC,CAAC;cAAEnD;YAAM,CAAC,kBACnExC,OAAA;cAAA+E,QAAA,GAAiB,SAAE,EAACvC,KAAK;YAAA,GAAfA,KAAK;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnF,OAAA;MAAKK,SAAS,EAAC,wCAAwC;MAAA0E,QAAA,gBACrD/E,OAAA,CAACT,MAAM;QACLyG,OAAO,EAAC,SAAS;QACjBC,OAAO,EAAE9F,MAAO;QAChB+F,QAAQ,EAAC,WAAW;QACpBC,YAAY,EAAC,MAAM;QAAApB,QAAA,EACpB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETnF,OAAA;QAAKK,SAAS,EAAC,yBAAyB;QAAA0E,QAAA,gBACtC/E,OAAA;UAAKK,SAAS,EAAC,+BAA+B;UAAA0E,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnF,OAAA,CAACT,MAAM;UACL0G,OAAO,EAAEnB,UAAW;UACpBsB,QAAQ,EAAE,CAAChE,OAAQ;UACnB8D,QAAQ,EAAC,YAAY;UACrBC,YAAY,EAAC,OAAO;UAAApB,QAAA,EACrB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7E,EAAA,CA/VIL,6BAA6B;AAAAoG,EAAA,GAA7BpG,6BAA6B;AAiWnC,eAAeA,6BAA6B;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}