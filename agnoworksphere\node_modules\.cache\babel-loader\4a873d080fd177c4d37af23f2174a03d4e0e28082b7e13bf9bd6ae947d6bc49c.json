{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\project\\\\ProjectConfirmationSummary.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from '../ui/Button';\nimport Toggle from '../ui/Toggle';\nimport Checkbox from '../ui/Checkbox';\nimport Icon from '../AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProjectConfirmationSummary = ({\n  onFinalize,\n  onBack,\n  projectData = {},\n  className\n}) => {\n  _s();\n  var _overview$timeline, _overview$timeline2, _overview$timeline3;\n  const [notifications, setNotifications] = useState({\n    email: true,\n    push: false,\n    slack: false,\n    teams: false\n  });\n  const [launchOptions, setLaunchOptions] = useState({\n    createRepository: true,\n    setupCI: false,\n    deployStaging: false,\n    inviteTeam: true,\n    generateDocumentation: true\n  });\n  const [agreements, setAgreements] = useState({\n    termsAccepted: false,\n    dataProcessing: false,\n    teamNotification: false\n  });\n  const [isValidated, setIsValidated] = useState(false);\n  const {\n    configuration = {},\n    overview = {},\n    techStack = {},\n    workflow = {},\n    tasks = []\n  } = projectData;\n  React.useEffect(() => {\n    const allAgreementsAccepted = Object.values(agreements).every(Boolean);\n    setIsValidated(allAgreementsAccepted);\n  }, [agreements]);\n  const formatCurrency = value => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value || 0);\n  };\n  const formatDuration = weeks => {\n    if (!weeks) return 'Not specified';\n    if (weeks < 4) return `${weeks} week${weeks !== 1 ? 's' : ''}`;\n    const months = Math.round(weeks / 4.33);\n    return `${months} month${months !== 1 ? 's' : ''}`;\n  };\n  const getTotalSelectedTech = () => {\n    return Object.values(techStack).reduce((total, category) => {\n      return total + (Array.isArray(category) ? category.length : 0);\n    }, 0);\n  };\n  const getTotalTasks = () => {\n    if (Array.isArray(tasks)) return tasks.length;\n    if (workflow.phases) {\n      return workflow.phases.reduce((total, phase) => {\n        var _phase$tasks;\n        return total + (((_phase$tasks = phase.tasks) === null || _phase$tasks === void 0 ? void 0 : _phase$tasks.length) || 0);\n      }, 0);\n    }\n    return 0;\n  };\n  const getProjectComplexity = () => {\n    const techCount = getTotalSelectedTech();\n    const taskCount = getTotalTasks();\n    const teamSize = Object.values(configuration.resources || {}).reduce((sum, count) => sum + count, 0);\n    const complexityScore = techCount * 0.3 + taskCount * 0.4 + teamSize * 0.3;\n    if (complexityScore < 10) return {\n      level: 'Simple',\n      color: 'text-green-600',\n      description: 'Straightforward project with minimal complexity'\n    };\n    if (complexityScore < 20) return {\n      level: 'Moderate',\n      color: 'text-yellow-600',\n      description: 'Balanced project with moderate complexity'\n    };\n    if (complexityScore < 30) return {\n      level: 'Complex',\n      color: 'text-orange-600',\n      description: 'Advanced project requiring careful coordination'\n    };\n    return {\n      level: 'Enterprise',\n      color: 'text-red-600',\n      description: 'Large-scale project with high complexity'\n    };\n  };\n  const complexity = getProjectComplexity();\n  const handleFinalize = () => {\n    if (isValidated) {\n      onFinalize === null || onFinalize === void 0 ? void 0 : onFinalize({\n        ...projectData,\n        notifications,\n        launchOptions,\n        finalizedAt: new Date().toISOString()\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn(\"max-w-6xl mx-auto p-6 space-y-8\", className),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-3xl font-bold text-foreground\",\n        children: \"Project Summary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-muted-foreground\",\n        children: \"Review your project configuration and finalize setup\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6 border border-border\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-foreground\",\n            children: overview.title || 'Untitled Project'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted-foreground mt-1\",\n            children: overview.description || 'No description provided'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: cn(\"px-3 py-1 rounded-full text-sm font-medium\", complexity.color, \"bg-background border\"),\n          children: complexity.level\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-foreground\",\n            children: formatCurrency(configuration.budget)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-muted-foreground\",\n            children: \"Budget\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-foreground\",\n            children: formatDuration(configuration.duration)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-muted-foreground\",\n            children: \"Duration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-foreground\",\n            children: getTotalSelectedTech()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-muted-foreground\",\n            children: \"Technologies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-foreground\",\n            children: getTotalTasks()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-muted-foreground\",\n            children: \"Tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Settings\",\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), \"Configuration\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted-foreground\",\n              children: \"Methodology:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: configuration.methodology || 'Not specified'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted-foreground\",\n              children: \"Priority:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium capitalize\",\n              children: configuration.priority || 'Medium'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted-foreground\",\n              children: \"Team Size:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: [Object.values(configuration.resources || {}).reduce((sum, count) => sum + count, 0), \" members\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), configuration.resources && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-foreground\",\n            children: \"Team Composition:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-1 text-sm\",\n            children: Object.entries(configuration.resources).map(([role, count]) => count > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-muted-foreground capitalize\",\n                children: [role.replace(/([A-Z])/g, ' $1'), \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 23\n              }, this)]\n            }, role, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Code\",\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), \"Technology Stack\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: Object.entries(techStack).map(([category, technologies]) => {\n            if (!Array.isArray(technologies) || technologies.length === 0) return null;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-foreground capitalize mb-1\",\n                children: [category.replace(/([A-Z])/g, ' $1'), \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-1\",\n                children: technologies.map(tech => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 bg-secondary text-secondary-foreground rounded text-xs\",\n                  children: tech\n                }, tech, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this)]\n            }, category, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Target\",\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), \"Objectives & Deliverables\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), overview.objectives && overview.objectives.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-foreground mb-2\",\n            children: \"Objectives:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-1\",\n            children: overview.objectives.filter(obj => obj.trim()).map((objective, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"text-sm text-muted-foreground flex items-start gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"CheckCircle\",\n                className: \"h-3 w-3 text-green-600 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), objective]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this), overview.deliverables && overview.deliverables.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-foreground mb-2\",\n            children: \"Deliverables:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-1\",\n            children: overview.deliverables.filter(del => del.trim()).map((deliverable, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"text-sm text-muted-foreground flex items-start gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Package\",\n                className: \"h-3 w-3 text-blue-600 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 21\n              }, this), deliverable]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Calendar\",\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), \"Timeline & Milestones\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [((_overview$timeline = overview.timeline) === null || _overview$timeline === void 0 ? void 0 : _overview$timeline.startDate) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted-foreground\",\n              children: \"Start Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: new Date(overview.timeline.startDate).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), ((_overview$timeline2 = overview.timeline) === null || _overview$timeline2 === void 0 ? void 0 : _overview$timeline2.endDate) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted-foreground\",\n              children: \"End Date:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: new Date(overview.timeline.endDate).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), ((_overview$timeline3 = overview.timeline) === null || _overview$timeline3 === void 0 ? void 0 : _overview$timeline3.milestones) && overview.timeline.milestones.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-foreground mb-2\",\n            children: \"Key Milestones:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: overview.timeline.milestones.filter(m => m.name.trim()).map((milestone, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Flag\",\n                className: \"h-3 w-3 text-orange-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: milestone.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this), milestone.date && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-muted-foreground\",\n                children: [\"- \", new Date(milestone.date).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Bell\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), \"Notification Preferences\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(Toggle, {\n          label: \"Email Notifications\",\n          description: \"Receive project updates via email\",\n          checked: notifications.email,\n          onChange: checked => setNotifications(prev => ({\n            ...prev,\n            email: checked\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toggle, {\n          label: \"Push Notifications\",\n          description: \"Browser push notifications for urgent updates\",\n          checked: notifications.push,\n          onChange: checked => setNotifications(prev => ({\n            ...prev,\n            push: checked\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toggle, {\n          label: \"Slack Integration\",\n          description: \"Send updates to Slack channels\",\n          checked: notifications.slack,\n          onChange: checked => setNotifications(prev => ({\n            ...prev,\n            slack: checked\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toggle, {\n          label: \"Microsoft Teams\",\n          description: \"Integrate with Teams for notifications\",\n          checked: notifications.teams,\n          onChange: checked => setNotifications(prev => ({\n            ...prev,\n            teams: checked\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Rocket\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), \"Project Launch Options\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(Toggle, {\n          label: \"Create Git Repository\",\n          description: \"Set up version control repository\",\n          checked: launchOptions.createRepository,\n          onChange: checked => setLaunchOptions(prev => ({\n            ...prev,\n            createRepository: checked\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toggle, {\n          label: \"Setup CI/CD Pipeline\",\n          description: \"Configure automated testing and deployment\",\n          checked: launchOptions.setupCI,\n          onChange: checked => setLaunchOptions(prev => ({\n            ...prev,\n            setupCI: checked\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toggle, {\n          label: \"Deploy Staging Environment\",\n          description: \"Create staging environment for testing\",\n          checked: launchOptions.deployStaging,\n          onChange: checked => setLaunchOptions(prev => ({\n            ...prev,\n            deployStaging: checked\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toggle, {\n          label: \"Invite Team Members\",\n          description: \"Send invitations to project team\",\n          checked: launchOptions.inviteTeam,\n          onChange: checked => setLaunchOptions(prev => ({\n            ...prev,\n            inviteTeam: checked\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Toggle, {\n          label: \"Generate Documentation\",\n          description: \"Create initial project documentation\",\n          checked: launchOptions.generateDocumentation,\n          onChange: checked => setLaunchOptions(prev => ({\n            ...prev,\n            generateDocumentation: checked\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"AlertTriangle\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), \"Project Assessment\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 rounded-lg bg-secondary/30\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-foreground mb-2\",\n            children: \"Complexity Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-muted-foreground mb-2\",\n            children: complexity.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium\",\n              children: \"Complexity Level:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: cn(\"font-bold\", complexity.color),\n              children: complexity.level\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 rounded-lg border border-green-200 bg-green-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-green-800 mb-2 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"CheckCircle\",\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this), \"Strengths\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-sm text-green-700 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Well-defined objectives and deliverables\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Appropriate technology stack selection\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Clear timeline and milestones\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Balanced team composition\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 rounded-lg border border-yellow-200 bg-yellow-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-yellow-800 mb-2 flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"AlertTriangle\",\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), \"Recommendations\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"text-sm text-yellow-700 space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Regular progress reviews and checkpoints\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Risk mitigation planning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Stakeholder communication plan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Quality assurance processes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"FileText\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), \"Terms and Agreements\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-start gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n            checked: agreements.termsAccepted,\n            onChange: checked => setAgreements(prev => ({\n              ...prev,\n              termsAccepted: checked\n            })),\n            className: \"mt-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium text-foreground\",\n              children: \"Terms of Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-muted-foreground\",\n              children: \"I agree to the terms of service and project management guidelines\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-start gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n            checked: agreements.dataProcessing,\n            onChange: checked => setAgreements(prev => ({\n              ...prev,\n              dataProcessing: checked\n            })),\n            className: \"mt-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium text-foreground\",\n              children: \"Data Processing Agreement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-muted-foreground\",\n              children: \"I consent to the processing of project data for management and analytics purposes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-start gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n            checked: agreements.teamNotification,\n            onChange: checked => setAgreements(prev => ({\n              ...prev,\n              teamNotification: checked\n            })),\n            className: \"mt-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium text-foreground\",\n              children: \"Team Notification\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-muted-foreground\",\n              children: \"I authorize sending project invitations and notifications to team members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg p-6 border border-border\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-foreground\",\n          children: \"Ready to Launch Your Project?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted-foreground\",\n          children: \"Review all settings above and click \\\"Launch Project\\\" to begin your journey.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: isValidated ? \"CheckCircle\" : \"AlertCircle\",\n              className: cn(\"h-4 w-4\", isValidated ? \"text-green-600\" : \"text-red-600\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: isValidated ? \"text-green-600\" : \"text-red-600\",\n              children: isValidated ? \"All requirements met\" : \"Please accept all agreements\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center pt-6\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        onClick: onBack,\n        iconName: \"ArrowLeft\",\n        iconPosition: \"left\",\n        children: \"Back to Tasks\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-muted-foreground\",\n          children: \"Step 6 of 6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleFinalize,\n          disabled: !isValidated,\n          iconName: \"Rocket\",\n          iconPosition: \"right\",\n          size: \"lg\",\n          className: \"bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70\",\n          children: \"Launch Project\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectConfirmationSummary, \"mVstEhw4x0jyEfQsJ11JI2btCKw=\");\n_c = ProjectConfirmationSummary;\nexport default ProjectConfirmationSummary;\nvar _c;\n$RefreshReg$(_c, \"ProjectConfirmationSummary\");", "map": {"version": 3, "names": ["React", "useState", "cn", "<PERSON><PERSON>", "Toggle", "Checkbox", "Icon", "jsxDEV", "_jsxDEV", "ProjectConfirmationSummary", "onFinalize", "onBack", "projectData", "className", "_s", "_overview$timeline", "_overview$timeline2", "_overview$timeline3", "notifications", "setNotifications", "email", "push", "slack", "teams", "launchOptions", "setLaunchOptions", "createRepository", "setupCI", "deployStaging", "inviteTeam", "generateDocumentation", "agreements", "setAgreements", "termsAccepted", "dataProcessing", "teamNotification", "isValidated", "setIsValidated", "configuration", "overview", "techStack", "workflow", "tasks", "useEffect", "allAgreementsAccepted", "Object", "values", "every", "Boolean", "formatCurrency", "value", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatDuration", "weeks", "months", "Math", "round", "getTotalSelectedTech", "reduce", "total", "category", "Array", "isArray", "length", "getTotalTasks", "phases", "phase", "_phase$tasks", "getProjectComplexity", "techCount", "taskCount", "teamSize", "resources", "sum", "count", "complexityScore", "level", "color", "description", "complexity", "handleFinalize", "finalizedAt", "Date", "toISOString", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "budget", "duration", "name", "methodology", "priority", "entries", "map", "role", "replace", "technologies", "tech", "objectives", "filter", "obj", "trim", "objective", "index", "deliverables", "del", "deliverable", "timeline", "startDate", "toLocaleDateString", "endDate", "milestones", "m", "milestone", "date", "label", "checked", "onChange", "prev", "variant", "onClick", "iconName", "iconPosition", "disabled", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/project/ProjectConfirmationSummary.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from '../ui/Button';\nimport Toggle from '../ui/Toggle';\nimport Checkbox from '../ui/Checkbox';\nimport Icon from '../AppIcon';\n\nconst ProjectConfirmationSummary = ({ \n  onFinalize, \n  onBack, \n  projectData = {}, \n  className \n}) => {\n  const [notifications, setNotifications] = useState({\n    email: true,\n    push: false,\n    slack: false,\n    teams: false\n  });\n\n  const [launchOptions, setLaunchOptions] = useState({\n    createRepository: true,\n    setupCI: false,\n    deployStaging: false,\n    inviteTeam: true,\n    generateDocumentation: true\n  });\n\n  const [agreements, setAgreements] = useState({\n    termsAccepted: false,\n    dataProcessing: false,\n    teamNotification: false\n  });\n\n  const [isValidated, setIsValidated] = useState(false);\n\n  const {\n    configuration = {},\n    overview = {},\n    techStack = {},\n    workflow = {},\n    tasks = []\n  } = projectData;\n\n  React.useEffect(() => {\n    const allAgreementsAccepted = Object.values(agreements).every(Boolean);\n    setIsValidated(allAgreementsAccepted);\n  }, [agreements]);\n\n  const formatCurrency = (value) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(value || 0);\n  };\n\n  const formatDuration = (weeks) => {\n    if (!weeks) return 'Not specified';\n    if (weeks < 4) return `${weeks} week${weeks !== 1 ? 's' : ''}`;\n    const months = Math.round(weeks / 4.33);\n    return `${months} month${months !== 1 ? 's' : ''}`;\n  };\n\n  const getTotalSelectedTech = () => {\n    return Object.values(techStack).reduce((total, category) => {\n      return total + (Array.isArray(category) ? category.length : 0);\n    }, 0);\n  };\n\n  const getTotalTasks = () => {\n    if (Array.isArray(tasks)) return tasks.length;\n    if (workflow.phases) {\n      return workflow.phases.reduce((total, phase) => total + (phase.tasks?.length || 0), 0);\n    }\n    return 0;\n  };\n\n  const getProjectComplexity = () => {\n    const techCount = getTotalSelectedTech();\n    const taskCount = getTotalTasks();\n    const teamSize = Object.values(configuration.resources || {}).reduce((sum, count) => sum + count, 0);\n    \n    const complexityScore = (techCount * 0.3) + (taskCount * 0.4) + (teamSize * 0.3);\n    \n    if (complexityScore < 10) return { level: 'Simple', color: 'text-green-600', description: 'Straightforward project with minimal complexity' };\n    if (complexityScore < 20) return { level: 'Moderate', color: 'text-yellow-600', description: 'Balanced project with moderate complexity' };\n    if (complexityScore < 30) return { level: 'Complex', color: 'text-orange-600', description: 'Advanced project requiring careful coordination' };\n    return { level: 'Enterprise', color: 'text-red-600', description: 'Large-scale project with high complexity' };\n  };\n\n  const complexity = getProjectComplexity();\n\n  const handleFinalize = () => {\n    if (isValidated) {\n      onFinalize?.({\n        ...projectData,\n        notifications,\n        launchOptions,\n        finalizedAt: new Date().toISOString()\n      });\n    }\n  };\n\n  return (\n    <div className={cn(\"max-w-6xl mx-auto p-6 space-y-8\", className)}>\n      {/* Header */}\n      <div className=\"text-center space-y-2\">\n        <h2 className=\"text-3xl font-bold text-foreground\">Project Summary</h2>\n        <p className=\"text-muted-foreground\">\n          Review your project configuration and finalize setup\n        </p>\n      </div>\n\n      {/* Project Overview Card */}\n      <div className=\"bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6 border border-border\">\n        <div className=\"flex items-start justify-between mb-4\">\n          <div>\n            <h3 className=\"text-2xl font-bold text-foreground\">{overview.title || 'Untitled Project'}</h3>\n            <p className=\"text-muted-foreground mt-1\">{overview.description || 'No description provided'}</p>\n          </div>\n          <div className={cn(\"px-3 py-1 rounded-full text-sm font-medium\", complexity.color, \"bg-background border\")}>\n            {complexity.level}\n          </div>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-foreground\">{formatCurrency(configuration.budget)}</div>\n            <div className=\"text-sm text-muted-foreground\">Budget</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-foreground\">{formatDuration(configuration.duration)}</div>\n            <div className=\"text-sm text-muted-foreground\">Duration</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-foreground\">{getTotalSelectedTech()}</div>\n            <div className=\"text-sm text-muted-foreground\">Technologies</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-foreground\">{getTotalTasks()}</div>\n            <div className=\"text-sm text-muted-foreground\">Tasks</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Detailed Summary Sections */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Configuration Summary */}\n        <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n          <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n            <Icon name=\"Settings\" className=\"h-5 w-5\" />\n            Configuration\n          </h3>\n          \n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-muted-foreground\">Methodology:</span>\n              <span className=\"font-medium\">{configuration.methodology || 'Not specified'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-muted-foreground\">Priority:</span>\n              <span className=\"font-medium capitalize\">{configuration.priority || 'Medium'}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-muted-foreground\">Team Size:</span>\n              <span className=\"font-medium\">\n                {Object.values(configuration.resources || {}).reduce((sum, count) => sum + count, 0)} members\n              </span>\n            </div>\n          </div>\n          \n          {configuration.resources && (\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium text-foreground\">Team Composition:</h4>\n              <div className=\"space-y-1 text-sm\">\n                {Object.entries(configuration.resources).map(([role, count]) => \n                  count > 0 && (\n                    <div key={role} className=\"flex justify-between\">\n                      <span className=\"text-muted-foreground capitalize\">{role.replace(/([A-Z])/g, ' $1')}:</span>\n                      <span>{count}</span>\n                    </div>\n                  )\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Technology Stack Summary */}\n        <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n          <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n            <Icon name=\"Code\" className=\"h-5 w-5\" />\n            Technology Stack\n          </h3>\n          \n          <div className=\"space-y-3\">\n            {Object.entries(techStack).map(([category, technologies]) => {\n              if (!Array.isArray(technologies) || technologies.length === 0) return null;\n              \n              return (\n                <div key={category}>\n                  <h4 className=\"font-medium text-foreground capitalize mb-1\">\n                    {category.replace(/([A-Z])/g, ' $1')}:\n                  </h4>\n                  <div className=\"flex flex-wrap gap-1\">\n                    {technologies.map((tech) => (\n                      <span \n                        key={tech}\n                        className=\"px-2 py-1 bg-secondary text-secondary-foreground rounded text-xs\"\n                      >\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Project Objectives */}\n        <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n          <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n            <Icon name=\"Target\" className=\"h-5 w-5\" />\n            Objectives & Deliverables\n          </h3>\n          \n          {overview.objectives && overview.objectives.length > 0 && (\n            <div>\n              <h4 className=\"font-medium text-foreground mb-2\">Objectives:</h4>\n              <ul className=\"space-y-1\">\n                {overview.objectives.filter(obj => obj.trim()).map((objective, index) => (\n                  <li key={index} className=\"text-sm text-muted-foreground flex items-start gap-2\">\n                    <Icon name=\"CheckCircle\" className=\"h-3 w-3 text-green-600 mt-0.5 flex-shrink-0\" />\n                    {objective}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n          \n          {overview.deliverables && overview.deliverables.length > 0 && (\n            <div>\n              <h4 className=\"font-medium text-foreground mb-2\">Deliverables:</h4>\n              <ul className=\"space-y-1\">\n                {overview.deliverables.filter(del => del.trim()).map((deliverable, index) => (\n                  <li key={index} className=\"text-sm text-muted-foreground flex items-start gap-2\">\n                    <Icon name=\"Package\" className=\"h-3 w-3 text-blue-600 mt-0.5 flex-shrink-0\" />\n                    {deliverable}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n        </div>\n\n        {/* Timeline & Milestones */}\n        <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n          <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n            <Icon name=\"Calendar\" className=\"h-5 w-5\" />\n            Timeline & Milestones\n          </h3>\n          \n          <div className=\"space-y-3\">\n            {overview.timeline?.startDate && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-muted-foreground\">Start Date:</span>\n                <span className=\"font-medium\">\n                  {new Date(overview.timeline.startDate).toLocaleDateString()}\n                </span>\n              </div>\n            )}\n            \n            {overview.timeline?.endDate && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-muted-foreground\">End Date:</span>\n                <span className=\"font-medium\">\n                  {new Date(overview.timeline.endDate).toLocaleDateString()}\n                </span>\n              </div>\n            )}\n          </div>\n          \n          {overview.timeline?.milestones && overview.timeline.milestones.length > 0 && (\n            <div>\n              <h4 className=\"font-medium text-foreground mb-2\">Key Milestones:</h4>\n              <div className=\"space-y-2\">\n                {overview.timeline.milestones.filter(m => m.name.trim()).map((milestone, index) => (\n                  <div key={index} className=\"flex items-center gap-2 text-sm\">\n                    <Icon name=\"Flag\" className=\"h-3 w-3 text-orange-600\" />\n                    <span className=\"font-medium\">{milestone.name}</span>\n                    {milestone.date && (\n                      <span className=\"text-muted-foreground\">\n                        - {new Date(milestone.date).toLocaleDateString()}\n                      </span>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Notification Preferences */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n        <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"Bell\" className=\"h-5 w-5\" />\n          Notification Preferences\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <Toggle\n            label=\"Email Notifications\"\n            description=\"Receive project updates via email\"\n            checked={notifications.email}\n            onChange={(checked) => setNotifications(prev => ({ ...prev, email: checked }))}\n          />\n\n          <Toggle\n            label=\"Push Notifications\"\n            description=\"Browser push notifications for urgent updates\"\n            checked={notifications.push}\n            onChange={(checked) => setNotifications(prev => ({ ...prev, push: checked }))}\n          />\n\n          <Toggle\n            label=\"Slack Integration\"\n            description=\"Send updates to Slack channels\"\n            checked={notifications.slack}\n            onChange={(checked) => setNotifications(prev => ({ ...prev, slack: checked }))}\n          />\n\n          <Toggle\n            label=\"Microsoft Teams\"\n            description=\"Integrate with Teams for notifications\"\n            checked={notifications.teams}\n            onChange={(checked) => setNotifications(prev => ({ ...prev, teams: checked }))}\n          />\n        </div>\n      </div>\n\n      {/* Launch Options */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n        <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"Rocket\" className=\"h-5 w-5\" />\n          Project Launch Options\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <Toggle\n            label=\"Create Git Repository\"\n            description=\"Set up version control repository\"\n            checked={launchOptions.createRepository}\n            onChange={(checked) => setLaunchOptions(prev => ({ ...prev, createRepository: checked }))}\n          />\n\n          <Toggle\n            label=\"Setup CI/CD Pipeline\"\n            description=\"Configure automated testing and deployment\"\n            checked={launchOptions.setupCI}\n            onChange={(checked) => setLaunchOptions(prev => ({ ...prev, setupCI: checked }))}\n          />\n\n          <Toggle\n            label=\"Deploy Staging Environment\"\n            description=\"Create staging environment for testing\"\n            checked={launchOptions.deployStaging}\n            onChange={(checked) => setLaunchOptions(prev => ({ ...prev, deployStaging: checked }))}\n          />\n\n          <Toggle\n            label=\"Invite Team Members\"\n            description=\"Send invitations to project team\"\n            checked={launchOptions.inviteTeam}\n            onChange={(checked) => setLaunchOptions(prev => ({ ...prev, inviteTeam: checked }))}\n          />\n\n          <Toggle\n            label=\"Generate Documentation\"\n            description=\"Create initial project documentation\"\n            checked={launchOptions.generateDocumentation}\n            onChange={(checked) => setLaunchOptions(prev => ({ ...prev, generateDocumentation: checked }))}\n          />\n        </div>\n      </div>\n\n      {/* Risk Assessment & Recommendations */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n        <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"AlertTriangle\" className=\"h-5 w-5\" />\n          Project Assessment\n        </h3>\n\n        <div className=\"space-y-4\">\n          <div className=\"p-4 rounded-lg bg-secondary/30\">\n            <h4 className=\"font-medium text-foreground mb-2\">Complexity Analysis</h4>\n            <p className=\"text-sm text-muted-foreground mb-2\">{complexity.description}</p>\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm font-medium\">Complexity Level:</span>\n              <span className={cn(\"font-bold\", complexity.color)}>{complexity.level}</span>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"p-4 rounded-lg border border-green-200 bg-green-50\">\n              <h4 className=\"font-medium text-green-800 mb-2 flex items-center gap-2\">\n                <Icon name=\"CheckCircle\" className=\"h-4 w-4\" />\n                Strengths\n              </h4>\n              <ul className=\"text-sm text-green-700 space-y-1\">\n                <li>• Well-defined objectives and deliverables</li>\n                <li>• Appropriate technology stack selection</li>\n                <li>• Clear timeline and milestones</li>\n                <li>• Balanced team composition</li>\n              </ul>\n            </div>\n\n            <div className=\"p-4 rounded-lg border border-yellow-200 bg-yellow-50\">\n              <h4 className=\"font-medium text-yellow-800 mb-2 flex items-center gap-2\">\n                <Icon name=\"AlertTriangle\" className=\"h-4 w-4\" />\n                Recommendations\n              </h4>\n              <ul className=\"text-sm text-yellow-700 space-y-1\">\n                <li>• Regular progress reviews and checkpoints</li>\n                <li>• Risk mitigation planning</li>\n                <li>• Stakeholder communication plan</li>\n                <li>• Quality assurance processes</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Terms and Agreements */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n        <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"FileText\" className=\"h-5 w-5\" />\n          Terms and Agreements\n        </h3>\n\n        <div className=\"space-y-3\">\n          <label className=\"flex items-start gap-3\">\n            <Checkbox\n              checked={agreements.termsAccepted}\n              onChange={(checked) => setAgreements(prev => ({ ...prev, termsAccepted: checked }))}\n              className=\"mt-1\"\n            />\n            <div>\n              <div className=\"font-medium text-foreground\">Terms of Service</div>\n              <div className=\"text-sm text-muted-foreground\">\n                I agree to the terms of service and project management guidelines\n              </div>\n            </div>\n          </label>\n\n          <label className=\"flex items-start gap-3\">\n            <Checkbox\n              checked={agreements.dataProcessing}\n              onChange={(checked) => setAgreements(prev => ({ ...prev, dataProcessing: checked }))}\n              className=\"mt-1\"\n            />\n            <div>\n              <div className=\"font-medium text-foreground\">Data Processing Agreement</div>\n              <div className=\"text-sm text-muted-foreground\">\n                I consent to the processing of project data for management and analytics purposes\n              </div>\n            </div>\n          </label>\n\n          <label className=\"flex items-start gap-3\">\n            <Checkbox\n              checked={agreements.teamNotification}\n              onChange={(checked) => setAgreements(prev => ({ ...prev, teamNotification: checked }))}\n              className=\"mt-1\"\n            />\n            <div>\n              <div className=\"font-medium text-foreground\">Team Notification</div>\n              <div className=\"text-sm text-muted-foreground\">\n                I authorize sending project invitations and notifications to team members\n              </div>\n            </div>\n          </label>\n        </div>\n      </div>\n\n      {/* Final Actions */}\n      <div className=\"bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg p-6 border border-border\">\n        <div className=\"text-center space-y-4\">\n          <h3 className=\"text-xl font-semibold text-foreground\">Ready to Launch Your Project?</h3>\n          <p className=\"text-muted-foreground\">\n            Review all settings above and click \"Launch Project\" to begin your journey.\n          </p>\n\n          <div className=\"flex items-center justify-center gap-4\">\n            <div className=\"flex items-center gap-2 text-sm\">\n              <Icon\n                name={isValidated ? \"CheckCircle\" : \"AlertCircle\"}\n                className={cn(\"h-4 w-4\", isValidated ? \"text-green-600\" : \"text-red-600\")}\n              />\n              <span className={isValidated ? \"text-green-600\" : \"text-red-600\"}>\n                {isValidated ? \"All requirements met\" : \"Please accept all agreements\"}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex justify-between items-center pt-6\">\n        <Button\n          variant=\"outline\"\n          onClick={onBack}\n          iconName=\"ArrowLeft\"\n          iconPosition=\"left\"\n        >\n          Back to Tasks\n        </Button>\n\n        <div className=\"flex items-center gap-4\">\n          <div className=\"text-sm text-muted-foreground\">\n            Step 6 of 6\n          </div>\n          <Button\n            onClick={handleFinalize}\n            disabled={!isValidated}\n            iconName=\"Rocket\"\n            iconPosition=\"right\"\n            size=\"lg\"\n            className=\"bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70\"\n          >\n            Launch Project\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProjectConfirmationSummary;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,EAAE,QAAQ,gBAAgB;AACnC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,0BAA0B,GAAGA,CAAC;EAClCC,UAAU;EACVC,MAAM;EACNC,WAAW,GAAG,CAAC,CAAC;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC;IACjDmB,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,KAAK;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC;IACjDyB,gBAAgB,EAAE,IAAI;IACtBC,OAAO,EAAE,KAAK;IACdC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,IAAI;IAChBC,qBAAqB,EAAE;EACzB,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC;IAC3CgC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,KAAK;IACrBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM;IACJqC,aAAa,GAAG,CAAC,CAAC;IAClBC,QAAQ,GAAG,CAAC,CAAC;IACbC,SAAS,GAAG,CAAC,CAAC;IACdC,QAAQ,GAAG,CAAC,CAAC;IACbC,KAAK,GAAG;EACV,CAAC,GAAG9B,WAAW;EAEfZ,KAAK,CAAC2C,SAAS,CAAC,MAAM;IACpB,MAAMC,qBAAqB,GAAGC,MAAM,CAACC,MAAM,CAACf,UAAU,CAAC,CAACgB,KAAK,CAACC,OAAO,CAAC;IACtEX,cAAc,CAACO,qBAAqB,CAAC;EACvC,CAAC,EAAE,CAACb,UAAU,CAAC,CAAC;EAEhB,MAAMkB,cAAc,GAAIC,KAAK,IAAK;IAChC,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC,CAAC,CAACC,MAAM,CAACP,KAAK,IAAI,CAAC,CAAC;EACvB,CAAC;EAED,MAAMQ,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAI,CAACA,KAAK,EAAE,OAAO,eAAe;IAClC,IAAIA,KAAK,GAAG,CAAC,EAAE,OAAO,GAAGA,KAAK,QAAQA,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IAC9D,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAG,IAAI,CAAC;IACvC,OAAO,GAAGC,MAAM,SAASA,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;EACpD,CAAC;EAED,MAAMG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,OAAOlB,MAAM,CAACC,MAAM,CAACN,SAAS,CAAC,CAACwB,MAAM,CAAC,CAACC,KAAK,EAAEC,QAAQ,KAAK;MAC1D,OAAOD,KAAK,IAAIE,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,GAAGA,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC;IAChE,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIH,KAAK,CAACC,OAAO,CAAC1B,KAAK,CAAC,EAAE,OAAOA,KAAK,CAAC2B,MAAM;IAC7C,IAAI5B,QAAQ,CAAC8B,MAAM,EAAE;MACnB,OAAO9B,QAAQ,CAAC8B,MAAM,CAACP,MAAM,CAAC,CAACC,KAAK,EAAEO,KAAK;QAAA,IAAAC,YAAA;QAAA,OAAKR,KAAK,IAAI,EAAAQ,YAAA,GAAAD,KAAK,CAAC9B,KAAK,cAAA+B,YAAA,uBAAXA,YAAA,CAAaJ,MAAM,KAAI,CAAC,CAAC;MAAA,GAAE,CAAC,CAAC;IACxF;IACA,OAAO,CAAC;EACV,CAAC;EAED,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,SAAS,GAAGZ,oBAAoB,CAAC,CAAC;IACxC,MAAMa,SAAS,GAAGN,aAAa,CAAC,CAAC;IACjC,MAAMO,QAAQ,GAAGhC,MAAM,CAACC,MAAM,CAACR,aAAa,CAACwC,SAAS,IAAI,CAAC,CAAC,CAAC,CAACd,MAAM,CAAC,CAACe,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC;IAEpG,MAAMC,eAAe,GAAIN,SAAS,GAAG,GAAG,GAAKC,SAAS,GAAG,GAAI,GAAIC,QAAQ,GAAG,GAAI;IAEhF,IAAII,eAAe,GAAG,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,WAAW,EAAE;IAAkD,CAAC;IAC7I,IAAIH,eAAe,GAAG,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,WAAW,EAAE;IAA4C,CAAC;IAC1I,IAAIH,eAAe,GAAG,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,WAAW,EAAE;IAAkD,CAAC;IAC/I,OAAO;MAAEF,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE,cAAc;MAAEC,WAAW,EAAE;IAA2C,CAAC;EAChH,CAAC;EAED,MAAMC,UAAU,GAAGX,oBAAoB,CAAC,CAAC;EAEzC,MAAMY,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIlD,WAAW,EAAE;MACf1B,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG;QACX,GAAGE,WAAW;QACdM,aAAa;QACbM,aAAa;QACb+D,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACtC,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEjF,OAAA;IAAKK,SAAS,EAAEX,EAAE,CAAC,iCAAiC,EAAEW,SAAS,CAAE;IAAA6E,QAAA,gBAE/DlF,OAAA;MAAKK,SAAS,EAAC,uBAAuB;MAAA6E,QAAA,gBACpClF,OAAA;QAAIK,SAAS,EAAC,oCAAoC;QAAA6E,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvEtF,OAAA;QAAGK,SAAS,EAAC,uBAAuB;QAAA6E,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNtF,OAAA;MAAKK,SAAS,EAAC,sFAAsF;MAAA6E,QAAA,gBACnGlF,OAAA;QAAKK,SAAS,EAAC,uCAAuC;QAAA6E,QAAA,gBACpDlF,OAAA;UAAAkF,QAAA,gBACElF,OAAA;YAAIK,SAAS,EAAC,oCAAoC;YAAA6E,QAAA,EAAEnD,QAAQ,CAACwD,KAAK,IAAI;UAAkB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9FtF,OAAA;YAAGK,SAAS,EAAC,4BAA4B;YAAA6E,QAAA,EAAEnD,QAAQ,CAAC6C,WAAW,IAAI;UAAyB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC,eACNtF,OAAA;UAAKK,SAAS,EAAEX,EAAE,CAAC,4CAA4C,EAAEmF,UAAU,CAACF,KAAK,EAAE,sBAAsB,CAAE;UAAAO,QAAA,EACxGL,UAAU,CAACH;QAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtF,OAAA;QAAKK,SAAS,EAAC,uCAAuC;QAAA6E,QAAA,gBACpDlF,OAAA;UAAKK,SAAS,EAAC,aAAa;UAAA6E,QAAA,gBAC1BlF,OAAA;YAAKK,SAAS,EAAC,oCAAoC;YAAA6E,QAAA,EAAEzC,cAAc,CAACX,aAAa,CAAC0D,MAAM;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChGtF,OAAA;YAAKK,SAAS,EAAC,+BAA+B;YAAA6E,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACNtF,OAAA;UAAKK,SAAS,EAAC,aAAa;UAAA6E,QAAA,gBAC1BlF,OAAA;YAAKK,SAAS,EAAC,oCAAoC;YAAA6E,QAAA,EAAEhC,cAAc,CAACpB,aAAa,CAAC2D,QAAQ;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClGtF,OAAA;YAAKK,SAAS,EAAC,+BAA+B;YAAA6E,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACNtF,OAAA;UAAKK,SAAS,EAAC,aAAa;UAAA6E,QAAA,gBAC1BlF,OAAA;YAAKK,SAAS,EAAC,oCAAoC;YAAA6E,QAAA,EAAE3B,oBAAoB,CAAC;UAAC;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClFtF,OAAA;YAAKK,SAAS,EAAC,+BAA+B;YAAA6E,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACNtF,OAAA;UAAKK,SAAS,EAAC,aAAa;UAAA6E,QAAA,gBAC1BlF,OAAA;YAAKK,SAAS,EAAC,oCAAoC;YAAA6E,QAAA,EAAEpB,aAAa,CAAC;UAAC;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3EtF,OAAA;YAAKK,SAAS,EAAC,+BAA+B;YAAA6E,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAKK,SAAS,EAAC,uCAAuC;MAAA6E,QAAA,gBAEpDlF,OAAA;QAAKK,SAAS,EAAC,uDAAuD;QAAA6E,QAAA,gBACpElF,OAAA;UAAIK,SAAS,EAAC,+DAA+D;UAAA6E,QAAA,gBAC3ElF,OAAA,CAACF,IAAI;YAAC4F,IAAI,EAAC,UAAU;YAACrF,SAAS,EAAC;UAAS;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELtF,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAA6E,QAAA,gBACxBlF,OAAA;YAAKK,SAAS,EAAC,sBAAsB;YAAA6E,QAAA,gBACnClF,OAAA;cAAMK,SAAS,EAAC,uBAAuB;cAAA6E,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DtF,OAAA;cAAMK,SAAS,EAAC,aAAa;cAAA6E,QAAA,EAAEpD,aAAa,CAAC6D,WAAW,IAAI;YAAe;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACNtF,OAAA;YAAKK,SAAS,EAAC,sBAAsB;YAAA6E,QAAA,gBACnClF,OAAA;cAAMK,SAAS,EAAC,uBAAuB;cAAA6E,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDtF,OAAA;cAAMK,SAAS,EAAC,wBAAwB;cAAA6E,QAAA,EAAEpD,aAAa,CAAC8D,QAAQ,IAAI;YAAQ;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACNtF,OAAA;YAAKK,SAAS,EAAC,sBAAsB;YAAA6E,QAAA,gBACnClF,OAAA;cAAMK,SAAS,EAAC,uBAAuB;cAAA6E,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDtF,OAAA;cAAMK,SAAS,EAAC,aAAa;cAAA6E,QAAA,GAC1B7C,MAAM,CAACC,MAAM,CAACR,aAAa,CAACwC,SAAS,IAAI,CAAC,CAAC,CAAC,CAACd,MAAM,CAAC,CAACe,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,EAAC,UACvF;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELxD,aAAa,CAACwC,SAAS,iBACtBtE,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAA6E,QAAA,gBACxBlF,OAAA;YAAIK,SAAS,EAAC,6BAA6B;YAAA6E,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClEtF,OAAA;YAAKK,SAAS,EAAC,mBAAmB;YAAA6E,QAAA,EAC/B7C,MAAM,CAACwD,OAAO,CAAC/D,aAAa,CAACwC,SAAS,CAAC,CAACwB,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEvB,KAAK,CAAC,KACzDA,KAAK,GAAG,CAAC,iBACPxE,OAAA;cAAgBK,SAAS,EAAC,sBAAsB;cAAA6E,QAAA,gBAC9ClF,OAAA;gBAAMK,SAAS,EAAC,kCAAkC;gBAAA6E,QAAA,GAAEa,IAAI,CAACC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,EAAC,GAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5FtF,OAAA;gBAAAkF,QAAA,EAAOV;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAFZS,IAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGT,CAET;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtF,OAAA;QAAKK,SAAS,EAAC,uDAAuD;QAAA6E,QAAA,gBACpElF,OAAA;UAAIK,SAAS,EAAC,+DAA+D;UAAA6E,QAAA,gBAC3ElF,OAAA,CAACF,IAAI;YAAC4F,IAAI,EAAC,MAAM;YAACrF,SAAS,EAAC;UAAS;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAE1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELtF,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAA6E,QAAA,EACvB7C,MAAM,CAACwD,OAAO,CAAC7D,SAAS,CAAC,CAAC8D,GAAG,CAAC,CAAC,CAACpC,QAAQ,EAAEuC,YAAY,CAAC,KAAK;YAC3D,IAAI,CAACtC,KAAK,CAACC,OAAO,CAACqC,YAAY,CAAC,IAAIA,YAAY,CAACpC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;YAE1E,oBACE7D,OAAA;cAAAkF,QAAA,gBACElF,OAAA;gBAAIK,SAAS,EAAC,6CAA6C;gBAAA6E,QAAA,GACxDxB,QAAQ,CAACsC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,EAAC,GACvC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLtF,OAAA;gBAAKK,SAAS,EAAC,sBAAsB;gBAAA6E,QAAA,EAClCe,YAAY,CAACH,GAAG,CAAEI,IAAI,iBACrBlG,OAAA;kBAEEK,SAAS,EAAC,kEAAkE;kBAAA6E,QAAA,EAE3EgB;gBAAI,GAHAA,IAAI;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIL,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GAbE5B,QAAQ;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcb,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKK,SAAS,EAAC,uDAAuD;QAAA6E,QAAA,gBACpElF,OAAA;UAAIK,SAAS,EAAC,+DAA+D;UAAA6E,QAAA,gBAC3ElF,OAAA,CAACF,IAAI;YAAC4F,IAAI,EAAC,QAAQ;YAACrF,SAAS,EAAC;UAAS;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJvD,QAAQ,CAACoE,UAAU,IAAIpE,QAAQ,CAACoE,UAAU,CAACtC,MAAM,GAAG,CAAC,iBACpD7D,OAAA;UAAAkF,QAAA,gBACElF,OAAA;YAAIK,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEtF,OAAA;YAAIK,SAAS,EAAC,WAAW;YAAA6E,QAAA,EACtBnD,QAAQ,CAACoE,UAAU,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAACR,GAAG,CAAC,CAACS,SAAS,EAAEC,KAAK,kBAClExG,OAAA;cAAgBK,SAAS,EAAC,sDAAsD;cAAA6E,QAAA,gBAC9ElF,OAAA,CAACF,IAAI;gBAAC4F,IAAI,EAAC,aAAa;gBAACrF,SAAS,EAAC;cAA6C;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAClFiB,SAAS;YAAA,GAFHC,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN,EAEAvD,QAAQ,CAAC0E,YAAY,IAAI1E,QAAQ,CAAC0E,YAAY,CAAC5C,MAAM,GAAG,CAAC,iBACxD7D,OAAA;UAAAkF,QAAA,gBACElF,OAAA;YAAIK,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEtF,OAAA;YAAIK,SAAS,EAAC,WAAW;YAAA6E,QAAA,EACtBnD,QAAQ,CAAC0E,YAAY,CAACL,MAAM,CAACM,GAAG,IAAIA,GAAG,CAACJ,IAAI,CAAC,CAAC,CAAC,CAACR,GAAG,CAAC,CAACa,WAAW,EAAEH,KAAK,kBACtExG,OAAA;cAAgBK,SAAS,EAAC,sDAAsD;cAAA6E,QAAA,gBAC9ElF,OAAA,CAACF,IAAI;gBAAC4F,IAAI,EAAC,SAAS;gBAACrF,SAAS,EAAC;cAA4C;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC7EqB,WAAW;YAAA,GAFLH,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtF,OAAA;QAAKK,SAAS,EAAC,uDAAuD;QAAA6E,QAAA,gBACpElF,OAAA;UAAIK,SAAS,EAAC,+DAA+D;UAAA6E,QAAA,gBAC3ElF,OAAA,CAACF,IAAI;YAAC4F,IAAI,EAAC,UAAU;YAACrF,SAAS,EAAC;UAAS;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAE9C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELtF,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAA6E,QAAA,GACvB,EAAA3E,kBAAA,GAAAwB,QAAQ,CAAC6E,QAAQ,cAAArG,kBAAA,uBAAjBA,kBAAA,CAAmBsG,SAAS,kBAC3B7G,OAAA;YAAKK,SAAS,EAAC,sBAAsB;YAAA6E,QAAA,gBACnClF,OAAA;cAAMK,SAAS,EAAC,uBAAuB;cAAA6E,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DtF,OAAA;cAAMK,SAAS,EAAC,aAAa;cAAA6E,QAAA,EAC1B,IAAIF,IAAI,CAACjD,QAAQ,CAAC6E,QAAQ,CAACC,SAAS,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN,EAEA,EAAA9E,mBAAA,GAAAuB,QAAQ,CAAC6E,QAAQ,cAAApG,mBAAA,uBAAjBA,mBAAA,CAAmBuG,OAAO,kBACzB/G,OAAA;YAAKK,SAAS,EAAC,sBAAsB;YAAA6E,QAAA,gBACnClF,OAAA;cAAMK,SAAS,EAAC,uBAAuB;cAAA6E,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxDtF,OAAA;cAAMK,SAAS,EAAC,aAAa;cAAA6E,QAAA,EAC1B,IAAIF,IAAI,CAACjD,QAAQ,CAAC6E,QAAQ,CAACG,OAAO,CAAC,CAACD,kBAAkB,CAAC;YAAC;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEL,EAAA7E,mBAAA,GAAAsB,QAAQ,CAAC6E,QAAQ,cAAAnG,mBAAA,uBAAjBA,mBAAA,CAAmBuG,UAAU,KAAIjF,QAAQ,CAAC6E,QAAQ,CAACI,UAAU,CAACnD,MAAM,GAAG,CAAC,iBACvE7D,OAAA;UAAAkF,QAAA,gBACElF,OAAA;YAAIK,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrEtF,OAAA;YAAKK,SAAS,EAAC,WAAW;YAAA6E,QAAA,EACvBnD,QAAQ,CAAC6E,QAAQ,CAACI,UAAU,CAACZ,MAAM,CAACa,CAAC,IAAIA,CAAC,CAACvB,IAAI,CAACY,IAAI,CAAC,CAAC,CAAC,CAACR,GAAG,CAAC,CAACoB,SAAS,EAAEV,KAAK,kBAC5ExG,OAAA;cAAiBK,SAAS,EAAC,iCAAiC;cAAA6E,QAAA,gBAC1DlF,OAAA,CAACF,IAAI;gBAAC4F,IAAI,EAAC,MAAM;gBAACrF,SAAS,EAAC;cAAyB;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxDtF,OAAA;gBAAMK,SAAS,EAAC,aAAa;gBAAA6E,QAAA,EAAEgC,SAAS,CAACxB;cAAI;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACpD4B,SAAS,CAACC,IAAI,iBACbnH,OAAA;gBAAMK,SAAS,EAAC,uBAAuB;gBAAA6E,QAAA,GAAC,IACpC,EAAC,IAAIF,IAAI,CAACkC,SAAS,CAACC,IAAI,CAAC,CAACL,kBAAkB,CAAC,CAAC;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CACP;YAAA,GAPOkB,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAA6E,QAAA,gBACpElF,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAA6E,QAAA,gBAC3ElF,OAAA,CAACF,IAAI;UAAC4F,IAAI,EAAC,MAAM;UAACrF,SAAS,EAAC;QAAS;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAE1C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELtF,OAAA;QAAKK,SAAS,EAAC,uCAAuC;QAAA6E,QAAA,gBACpDlF,OAAA,CAACJ,MAAM;UACLwH,KAAK,EAAC,qBAAqB;UAC3BxC,WAAW,EAAC,mCAAmC;UAC/CyC,OAAO,EAAE3G,aAAa,CAACE,KAAM;UAC7B0G,QAAQ,EAAGD,OAAO,IAAK1G,gBAAgB,CAAC4G,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE3G,KAAK,EAAEyG;UAAQ,CAAC,CAAC;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eAEFtF,OAAA,CAACJ,MAAM;UACLwH,KAAK,EAAC,oBAAoB;UAC1BxC,WAAW,EAAC,+CAA+C;UAC3DyC,OAAO,EAAE3G,aAAa,CAACG,IAAK;UAC5ByG,QAAQ,EAAGD,OAAO,IAAK1G,gBAAgB,CAAC4G,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE1G,IAAI,EAAEwG;UAAQ,CAAC,CAAC;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC,eAEFtF,OAAA,CAACJ,MAAM;UACLwH,KAAK,EAAC,mBAAmB;UACzBxC,WAAW,EAAC,gCAAgC;UAC5CyC,OAAO,EAAE3G,aAAa,CAACI,KAAM;UAC7BwG,QAAQ,EAAGD,OAAO,IAAK1G,gBAAgB,CAAC4G,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEzG,KAAK,EAAEuG;UAAQ,CAAC,CAAC;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC,eAEFtF,OAAA,CAACJ,MAAM;UACLwH,KAAK,EAAC,iBAAiB;UACvBxC,WAAW,EAAC,wCAAwC;UACpDyC,OAAO,EAAE3G,aAAa,CAACK,KAAM;UAC7BuG,QAAQ,EAAGD,OAAO,IAAK1G,gBAAgB,CAAC4G,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAExG,KAAK,EAAEsG;UAAQ,CAAC,CAAC;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAA6E,QAAA,gBACpElF,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAA6E,QAAA,gBAC3ElF,OAAA,CAACF,IAAI;UAAC4F,IAAI,EAAC,QAAQ;UAACrF,SAAS,EAAC;QAAS;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,0BAE5C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELtF,OAAA;QAAKK,SAAS,EAAC,uCAAuC;QAAA6E,QAAA,gBACpDlF,OAAA,CAACJ,MAAM;UACLwH,KAAK,EAAC,uBAAuB;UAC7BxC,WAAW,EAAC,mCAAmC;UAC/CyC,OAAO,EAAErG,aAAa,CAACE,gBAAiB;UACxCoG,QAAQ,EAAGD,OAAO,IAAKpG,gBAAgB,CAACsG,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAErG,gBAAgB,EAAEmG;UAAQ,CAAC,CAAC;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC,eAEFtF,OAAA,CAACJ,MAAM;UACLwH,KAAK,EAAC,sBAAsB;UAC5BxC,WAAW,EAAC,4CAA4C;UACxDyC,OAAO,EAAErG,aAAa,CAACG,OAAQ;UAC/BmG,QAAQ,EAAGD,OAAO,IAAKpG,gBAAgB,CAACsG,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEpG,OAAO,EAAEkG;UAAQ,CAAC,CAAC;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC,eAEFtF,OAAA,CAACJ,MAAM;UACLwH,KAAK,EAAC,4BAA4B;UAClCxC,WAAW,EAAC,wCAAwC;UACpDyC,OAAO,EAAErG,aAAa,CAACI,aAAc;UACrCkG,QAAQ,EAAGD,OAAO,IAAKpG,gBAAgB,CAACsG,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEnG,aAAa,EAAEiG;UAAQ,CAAC,CAAC;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,eAEFtF,OAAA,CAACJ,MAAM;UACLwH,KAAK,EAAC,qBAAqB;UAC3BxC,WAAW,EAAC,kCAAkC;UAC9CyC,OAAO,EAAErG,aAAa,CAACK,UAAW;UAClCiG,QAAQ,EAAGD,OAAO,IAAKpG,gBAAgB,CAACsG,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAElG,UAAU,EAAEgG;UAAQ,CAAC,CAAC;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eAEFtF,OAAA,CAACJ,MAAM;UACLwH,KAAK,EAAC,wBAAwB;UAC9BxC,WAAW,EAAC,sCAAsC;UAClDyC,OAAO,EAAErG,aAAa,CAACM,qBAAsB;UAC7CgG,QAAQ,EAAGD,OAAO,IAAKpG,gBAAgB,CAACsG,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEjG,qBAAqB,EAAE+F;UAAQ,CAAC,CAAC;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAA6E,QAAA,gBACpElF,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAA6E,QAAA,gBAC3ElF,OAAA,CAACF,IAAI;UAAC4F,IAAI,EAAC,eAAe;UAACrF,SAAS,EAAC;QAAS;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,sBAEnD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELtF,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAA6E,QAAA,gBACxBlF,OAAA;UAAKK,SAAS,EAAC,gCAAgC;UAAA6E,QAAA,gBAC7ClF,OAAA;YAAIK,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEtF,OAAA;YAAGK,SAAS,EAAC,oCAAoC;YAAA6E,QAAA,EAAEL,UAAU,CAACD;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EtF,OAAA;YAAKK,SAAS,EAAC,yBAAyB;YAAA6E,QAAA,gBACtClF,OAAA;cAAMK,SAAS,EAAC,qBAAqB;cAAA6E,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DtF,OAAA;cAAMK,SAAS,EAAEX,EAAE,CAAC,WAAW,EAAEmF,UAAU,CAACF,KAAK,CAAE;cAAAO,QAAA,EAAEL,UAAU,CAACH;YAAK;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtF,OAAA;UAAKK,SAAS,EAAC,uCAAuC;UAAA6E,QAAA,gBACpDlF,OAAA;YAAKK,SAAS,EAAC,oDAAoD;YAAA6E,QAAA,gBACjElF,OAAA;cAAIK,SAAS,EAAC,yDAAyD;cAAA6E,QAAA,gBACrElF,OAAA,CAACF,IAAI;gBAAC4F,IAAI,EAAC,aAAa;gBAACrF,SAAS,EAAC;cAAS;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAEjD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtF,OAAA;cAAIK,SAAS,EAAC,kCAAkC;cAAA6E,QAAA,gBAC9ClF,OAAA;gBAAAkF,QAAA,EAAI;cAA0C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDtF,OAAA;gBAAAkF,QAAA,EAAI;cAAwC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjDtF,OAAA;gBAAAkF,QAAA,EAAI;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCtF,OAAA;gBAAAkF,QAAA,EAAI;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENtF,OAAA;YAAKK,SAAS,EAAC,sDAAsD;YAAA6E,QAAA,gBACnElF,OAAA;cAAIK,SAAS,EAAC,0DAA0D;cAAA6E,QAAA,gBACtElF,OAAA,CAACF,IAAI;gBAAC4F,IAAI,EAAC,eAAe;gBAACrF,SAAS,EAAC;cAAS;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtF,OAAA;cAAIK,SAAS,EAAC,mCAAmC;cAAA6E,QAAA,gBAC/ClF,OAAA;gBAAAkF,QAAA,EAAI;cAA0C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDtF,OAAA;gBAAAkF,QAAA,EAAI;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCtF,OAAA;gBAAAkF,QAAA,EAAI;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzCtF,OAAA;gBAAAkF,QAAA,EAAI;cAA6B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAA6E,QAAA,gBACpElF,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAA6E,QAAA,gBAC3ElF,OAAA,CAACF,IAAI;UAAC4F,IAAI,EAAC,UAAU;UAACrF,SAAS,EAAC;QAAS;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,wBAE9C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELtF,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAA6E,QAAA,gBACxBlF,OAAA;UAAOK,SAAS,EAAC,wBAAwB;UAAA6E,QAAA,gBACvClF,OAAA,CAACH,QAAQ;YACPwH,OAAO,EAAE9F,UAAU,CAACE,aAAc;YAClC6F,QAAQ,EAAGD,OAAO,IAAK7F,aAAa,CAAC+F,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE9F,aAAa,EAAE4F;YAAQ,CAAC,CAAC,CAAE;YACpFhH,SAAS,EAAC;UAAM;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFtF,OAAA;YAAAkF,QAAA,gBACElF,OAAA;cAAKK,SAAS,EAAC,6BAA6B;cAAA6E,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnEtF,OAAA;cAAKK,SAAS,EAAC,+BAA+B;cAAA6E,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAERtF,OAAA;UAAOK,SAAS,EAAC,wBAAwB;UAAA6E,QAAA,gBACvClF,OAAA,CAACH,QAAQ;YACPwH,OAAO,EAAE9F,UAAU,CAACG,cAAe;YACnC4F,QAAQ,EAAGD,OAAO,IAAK7F,aAAa,CAAC+F,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE7F,cAAc,EAAE2F;YAAQ,CAAC,CAAC,CAAE;YACrFhH,SAAS,EAAC;UAAM;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFtF,OAAA;YAAAkF,QAAA,gBACElF,OAAA;cAAKK,SAAS,EAAC,6BAA6B;cAAA6E,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC5EtF,OAAA;cAAKK,SAAS,EAAC,+BAA+B;cAAA6E,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAERtF,OAAA;UAAOK,SAAS,EAAC,wBAAwB;UAAA6E,QAAA,gBACvClF,OAAA,CAACH,QAAQ;YACPwH,OAAO,EAAE9F,UAAU,CAACI,gBAAiB;YACrC2F,QAAQ,EAAGD,OAAO,IAAK7F,aAAa,CAAC+F,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAE5F,gBAAgB,EAAE0F;YAAQ,CAAC,CAAC,CAAE;YACvFhH,SAAS,EAAC;UAAM;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFtF,OAAA;YAAAkF,QAAA,gBACElF,OAAA;cAAKK,SAAS,EAAC,6BAA6B;cAAA6E,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpEtF,OAAA;cAAKK,SAAS,EAAC,+BAA+B;cAAA6E,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAKK,SAAS,EAAC,oFAAoF;MAAA6E,QAAA,eACjGlF,OAAA;QAAKK,SAAS,EAAC,uBAAuB;QAAA6E,QAAA,gBACpClF,OAAA;UAAIK,SAAS,EAAC,uCAAuC;UAAA6E,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxFtF,OAAA;UAAGK,SAAS,EAAC,uBAAuB;UAAA6E,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJtF,OAAA;UAAKK,SAAS,EAAC,wCAAwC;UAAA6E,QAAA,eACrDlF,OAAA;YAAKK,SAAS,EAAC,iCAAiC;YAAA6E,QAAA,gBAC9ClF,OAAA,CAACF,IAAI;cACH4F,IAAI,EAAE9D,WAAW,GAAG,aAAa,GAAG,aAAc;cAClDvB,SAAS,EAAEX,EAAE,CAAC,SAAS,EAAEkC,WAAW,GAAG,gBAAgB,GAAG,cAAc;YAAE;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eACFtF,OAAA;cAAMK,SAAS,EAAEuB,WAAW,GAAG,gBAAgB,GAAG,cAAe;cAAAsD,QAAA,EAC9DtD,WAAW,GAAG,sBAAsB,GAAG;YAA8B;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAKK,SAAS,EAAC,wCAAwC;MAAA6E,QAAA,gBACrDlF,OAAA,CAACL,MAAM;QACL6H,OAAO,EAAC,SAAS;QACjBC,OAAO,EAAEtH,MAAO;QAChBuH,QAAQ,EAAC,WAAW;QACpBC,YAAY,EAAC,MAAM;QAAAzC,QAAA,EACpB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETtF,OAAA;QAAKK,SAAS,EAAC,yBAAyB;QAAA6E,QAAA,gBACtClF,OAAA;UAAKK,SAAS,EAAC,+BAA+B;UAAA6E,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtF,OAAA,CAACL,MAAM;UACL8H,OAAO,EAAE3C,cAAe;UACxB8C,QAAQ,EAAE,CAAChG,WAAY;UACvB8F,QAAQ,EAAC,QAAQ;UACjBC,YAAY,EAAC,OAAO;UACpBE,IAAI,EAAC,IAAI;UACTxH,SAAS,EAAC,uFAAuF;UAAA6E,QAAA,EAClG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChF,EAAA,CAphBIL,0BAA0B;AAAA6H,EAAA,GAA1B7H,0BAA0B;AAshBhC,eAAeA,0BAA0B;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}