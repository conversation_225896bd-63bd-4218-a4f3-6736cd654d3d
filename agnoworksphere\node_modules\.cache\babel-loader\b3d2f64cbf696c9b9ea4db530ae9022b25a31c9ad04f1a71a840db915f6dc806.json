{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\ui\\\\DatePicker.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { cn } from '../../utils/cn';\nimport Icon from '../AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DatePicker = /*#__PURE__*/_s(/*#__PURE__*/React.forwardRef(_c = _s(({\n  className,\n  value,\n  onChange,\n  label,\n  description,\n  error,\n  required = false,\n  disabled = false,\n  placeholder = \"Select date\",\n  minDate,\n  maxDate,\n  ...props\n}, ref) => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedDate, setSelectedDate] = useState(value || '');\n  const handleDateChange = e => {\n    const newDate = e.target.value;\n    setSelectedDate(newDate);\n    onChange === null || onChange === void 0 ? void 0 : onChange(newDate);\n  };\n  const formatDisplayDate = dateString => {\n    if (!dateString) return placeholder;\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn(\"space-y-2\", className),\n    children: [label && /*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"text-sm font-medium text-foreground\",\n      children: [label, required && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-destructive ml-1\",\n        children: \"*\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 24\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        ref: ref,\n        type: \"date\",\n        value: selectedDate,\n        onChange: handleDateChange,\n        disabled: disabled,\n        min: minDate,\n        max: maxDate,\n        className: cn(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm\", \"ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium\", \"placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2\", \"focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", error && \"border-destructive focus-visible:ring-destructive\", \"appearance-none\"),\n        ...props\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Calendar\",\n          className: cn(\"h-4 w-4 text-muted-foreground\", disabled && \"opacity-50\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), description && !error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-xs text-muted-foreground\",\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-xs text-destructive\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n}, \"82DyXAfaDTGB/sHhLVbQZHbiMoY=\")), \"82DyXAfaDTGB/sHhLVbQZHbiMoY=\");\n_c2 = DatePicker;\nDatePicker.displayName = \"DatePicker\";\nexport default DatePicker;\nvar _c, _c2;\n$RefreshReg$(_c, \"DatePicker$React.forwardRef\");\n$RefreshReg$(_c2, \"DatePicker\");", "map": {"version": 3, "names": ["React", "useState", "cn", "Icon", "jsxDEV", "_jsxDEV", "DatePicker", "_s", "forwardRef", "_c", "className", "value", "onChange", "label", "description", "error", "required", "disabled", "placeholder", "minDate", "maxDate", "props", "ref", "isOpen", "setIsOpen", "selectedDate", "setSelectedDate", "handleDateChange", "e", "newDate", "target", "formatDisplayDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "min", "max", "name", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/ui/DatePicker.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { cn } from '../../utils/cn';\nimport Icon from '../AppIcon';\n\nconst DatePicker = React.forwardRef(({\n  className,\n  value,\n  onChange,\n  label,\n  description,\n  error,\n  required = false,\n  disabled = false,\n  placeholder = \"Select date\",\n  minDate,\n  maxDate,\n  ...props\n}, ref) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedDate, setSelectedDate] = useState(value || '');\n\n  const handleDateChange = (e) => {\n    const newDate = e.target.value;\n    setSelectedDate(newDate);\n    onChange?.(newDate);\n  };\n\n  const formatDisplayDate = (dateString) => {\n    if (!dateString) return placeholder;\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <div className={cn(\"space-y-2\", className)}>\n      {label && (\n        <label className=\"text-sm font-medium text-foreground\">\n          {label}\n          {required && <span className=\"text-destructive ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        <input\n          ref={ref}\n          type=\"date\"\n          value={selectedDate}\n          onChange={handleDateChange}\n          disabled={disabled}\n          min={minDate}\n          max={maxDate}\n          className={cn(\n            \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm\",\n            \"ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium\",\n            \"placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2\",\n            \"focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n            error && \"border-destructive focus-visible:ring-destructive\",\n            \"appearance-none\"\n          )}\n          {...props}\n        />\n        \n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n          <Icon \n            name=\"Calendar\" \n            className={cn(\n              \"h-4 w-4 text-muted-foreground\",\n              disabled && \"opacity-50\"\n            )} \n          />\n        </div>\n      </div>\n      \n      {description && !error && (\n        <p className=\"text-xs text-muted-foreground\">\n          {description}\n        </p>\n      )}\n      \n      {error && (\n        <p className=\"text-xs text-destructive\">\n          {error}\n        </p>\n      )}\n    </div>\n  );\n});\n\nDatePicker.displayName = \"DatePicker\";\n\nexport default DatePicker;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,EAAE,QAAQ,gBAAgB;AACnC,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,UAAU,gBAAAC,EAAA,cAAGP,KAAK,CAACQ,UAAU,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EACnCG,SAAS;EACTC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,WAAW;EACXC,KAAK;EACLC,QAAQ,GAAG,KAAK;EAChBC,QAAQ,GAAG,KAAK;EAChBC,WAAW,GAAG,aAAa;EAC3BC,OAAO;EACPC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EAAAf,EAAA;EACT,MAAM,CAACgB,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAACU,KAAK,IAAI,EAAE,CAAC;EAE7D,MAAMgB,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,OAAO,GAAGD,CAAC,CAACE,MAAM,CAACnB,KAAK;IAC9Be,eAAe,CAACG,OAAO,CAAC;IACxBjB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGiB,OAAO,CAAC;EACrB,CAAC;EAED,MAAME,iBAAiB,GAAIC,UAAU,IAAK;IACxC,IAAI,CAACA,UAAU,EAAE,OAAOd,WAAW;IACnC,MAAMe,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjC,OAAA;IAAKK,SAAS,EAAER,EAAE,CAAC,WAAW,EAAEQ,SAAS,CAAE;IAAA6B,QAAA,GACxC1B,KAAK,iBACJR,OAAA;MAAOK,SAAS,EAAC,qCAAqC;MAAA6B,QAAA,GACnD1B,KAAK,EACLG,QAAQ,iBAAIX,OAAA;QAAMK,SAAS,EAAC,uBAAuB;QAAA6B,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CACR,eAEDtC,OAAA;MAAKK,SAAS,EAAC,UAAU;MAAA6B,QAAA,gBACvBlC,OAAA;QACEiB,GAAG,EAAEA,GAAI;QACTsB,IAAI,EAAC,MAAM;QACXjC,KAAK,EAAEc,YAAa;QACpBb,QAAQ,EAAEe,gBAAiB;QAC3BV,QAAQ,EAAEA,QAAS;QACnB4B,GAAG,EAAE1B,OAAQ;QACb2B,GAAG,EAAE1B,OAAQ;QACbV,SAAS,EAAER,EAAE,CACX,iFAAiF,EACjF,wFAAwF,EACxF,mFAAmF,EACnF,qGAAqG,EACrGa,KAAK,IAAI,mDAAmD,EAC5D,iBACF,CAAE;QAAA,GACEM;MAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEFtC,OAAA;QAAKK,SAAS,EAAC,uEAAuE;QAAA6B,QAAA,eACpFlC,OAAA,CAACF,IAAI;UACH4C,IAAI,EAAC,UAAU;UACfrC,SAAS,EAAER,EAAE,CACX,+BAA+B,EAC/Be,QAAQ,IAAI,YACd;QAAE;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL7B,WAAW,IAAI,CAACC,KAAK,iBACpBV,OAAA;MAAGK,SAAS,EAAC,+BAA+B;MAAA6B,QAAA,EACzCzB;IAAW;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACJ,EAEA5B,KAAK,iBACJV,OAAA;MAAGK,SAAS,EAAC,0BAA0B;MAAA6B,QAAA,EACpCxB;IAAK;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC,kCAAC;AAACK,GAAA,GAtFG1C,UAAU;AAwFhBA,UAAU,CAAC2C,WAAW,GAAG,YAAY;AAErC,eAAe3C,UAAU;AAAC,IAAAG,EAAA,EAAAuC,GAAA;AAAAE,YAAA,CAAAzC,EAAA;AAAAyC,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}