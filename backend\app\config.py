"""
Configuration settings for the Agno WorkSphere API
"""
import os
from typing import List, Optional


class Settings:
    """Application settings"""

    def __init__(self):
        # Load from environment variables with defaults
        self.app_name = os.getenv("APP_NAME", "Agno WorkSphere API")
        self.app_version = os.getenv("APP_VERSION", "1.0.0")
        self.debug = os.getenv("DEBUG", "True").lower() == "true"
        self.environment = os.getenv("ENVIRONMENT", "development")

        # Database
        self.database_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@localhost:5432/agno_worksphere")
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")

        # Authentication
        self.jwt_secret = os.getenv("JWT_SECRET", "agno-worksphere-super-secret-jwt-key-for-development-only")
        self.jwt_algorithm = os.getenv("JWT_ALGORITHM", "HS256")
        self.jwt_expires_in = int(os.getenv("JWT_EXPIRES_IN", "15"))  # minutes
        self.refresh_token_expires_in = int(os.getenv("REFRESH_TOKEN_EXPIRES_IN", "7"))  # days

        # Security
        self.bcrypt_rounds = int(os.getenv("BCRYPT_ROUNDS", "12"))
        self.password_min_length = int(os.getenv("PASSWORD_MIN_LENGTH", "8"))

        # CORS
        origins_str = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:3001")
        self.allowed_origins = [origin.strip() for origin in origins_str.split(',')]

        # Rate Limiting
        self.rate_limit_per_minute = int(os.getenv("RATE_LIMIT_PER_MINUTE", "100"))

        # Email
        self.smtp_host = os.getenv("SMTP_HOST", "smtp.gmail.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_user = os.getenv("SMTP_USER", "")
        self.smtp_pass = os.getenv("SMTP_PASS", "")
        self.from_email = os.getenv("FROM_EMAIL", "<EMAIL>")
        self.from_name = os.getenv("FROM_NAME", "Agno WorkSphere")

        # File Upload
        self.max_file_size = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB
        file_types_str = os.getenv("ALLOWED_FILE_TYPES", "jpg,jpeg,png,gif,pdf,doc,docx,zip,rar")
        self.allowed_file_types = [file_type.strip() for file_type in file_types_str.split(',')]

        # AI Integration
        self.openai_api_key = os.getenv("OPENAI_API_KEY", "")
        self.openai_model = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
        self.ai_enabled = os.getenv("AI_ENABLED", "True").lower() == "true"
        self.ai_prediction_enabled = os.getenv("AI_PREDICTION_ENABLED", "True").lower() == "true"
        self.ai_insights_enabled = os.getenv("AI_INSIGHTS_ENABLED", "True").lower() == "true"
        self.ai_smart_notifications = os.getenv("AI_SMART_NOTIFICATIONS", "True").lower() == "true"

        # Live Testing
        self.live_testing_mode = os.getenv("LIVE_TESTING_MODE", "False").lower() == "true"
        self.reset_db_on_start = os.getenv("RESET_DB_ON_START", "False").lower() == "true"
        self.demo_data_enabled = os.getenv("DEMO_DATA_ENABLED", "False").lower() == "true"

        # Local file storage for development
        self.upload_directory = os.getenv("UPLOAD_DIRECTORY", "uploads")


# Load environment variables from .env file if it exists
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Global settings instance
settings = Settings()
