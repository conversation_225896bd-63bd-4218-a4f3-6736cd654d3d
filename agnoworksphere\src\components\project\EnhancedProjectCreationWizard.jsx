import React, { useState, useEffect } from 'react';
import { cn } from '../../utils/cn';
import Icon from '../AppIcon';
import ProjectConfigurationInterface from './ProjectConfigurationInterface';
import ProjectOverviewEditor from './ProjectOverviewEditor';
import TechStackDisplay from './TechStackDisplay';
import WorkflowManagement from './WorkflowManagement';
import TaskChecklistSystem from './TaskChecklistSystem';
import ProjectConfirmationSummary from './ProjectConfirmationSummary';

const EnhancedProjectCreationWizard = ({ 
  isOpen, 
  onClose, 
  onCreateProject, 
  organizationId, 
  organizationName,
  className 
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [projectData, setProjectData] = useState({
    configuration: {},
    overview: {},
    techStack: {},
    workflow: {},
    tasks: []
  });
  const [isAnimating, setIsAnimating] = useState(false);

  const steps = [
    {
      id: 'configuration',
      title: 'Configuration',
      description: 'Set up project basics',
      icon: 'Settings',
      component: ProjectConfigurationInterface
    },
    {
      id: 'overview',
      title: 'Overview',
      description: 'Define project details',
      icon: 'FileText',
      component: ProjectOverviewEditor
    },
    {
      id: 'techstack',
      title: 'Tech Stack',
      description: 'Choose technologies',
      icon: 'Code',
      component: TechStackDisplay
    },
    {
      id: 'workflow',
      title: 'Workflow',
      description: 'Plan project phases',
      icon: 'GitBranch',
      component: WorkflowManagement
    },
    {
      id: 'tasks',
      title: 'Tasks',
      description: 'Create task checklist',
      icon: 'CheckSquare',
      component: TaskChecklistSystem
    },
    {
      id: 'summary',
      title: 'Summary',
      description: 'Review and launch',
      icon: 'Rocket',
      component: ProjectConfirmationSummary
    }
  ];

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleNext = (stepData) => {
    const stepKey = steps[currentStep].id;
    setProjectData(prev => ({
      ...prev,
      [stepKey]: stepData
    }));

    if (currentStep < steps.length - 1) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep(prev => prev + 1);
        setIsAnimating(false);
      }, 200);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep(prev => prev - 1);
        setIsAnimating(false);
      }, 200);
    }
  };

  const handleFinalize = async (finalData) => {
    try {
      const completeProjectData = {
        ...projectData,
        ...finalData,
        organizationId,
        createdAt: new Date().toISOString()
      };

      await onCreateProject?.(completeProjectData);
      onClose?.();
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const handleStepClick = (stepIndex) => {
    if (stepIndex < currentStep) {
      setIsAnimating(true);
      setTimeout(() => {
        setCurrentStep(stepIndex);
        setIsAnimating(false);
      }, 200);
    }
  };

  if (!isOpen) return null;

  const CurrentStepComponent = steps[currentStep].component;
  const currentStepData = projectData[steps[currentStep].id];

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
      <div className={cn(
        "h-full w-full bg-background overflow-hidden",
        "animate-in fade-in-0 duration-300",
        className
      )}>
        {/* Header with Progress */}
        <div className="bg-card border-b border-border p-6">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-foreground">Create New Project</h1>
                <p className="text-muted-foreground">
                  {organizationName ? `for ${organizationName}` : 'Enhanced project creation wizard'}
                </p>
              </div>
              
              <button
                onClick={onClose}
                className="p-2 hover:bg-secondary rounded-lg transition-colors duration-200"
              >
                <Icon name="X" className="h-6 w-6" />
              </button>
            </div>

            {/* Progress Steps */}
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <button
                    onClick={() => handleStepClick(index)}
                    disabled={index > currentStep}
                    className={cn(
                      "flex items-center gap-3 p-3 rounded-lg transition-all duration-200",
                      "hover:bg-secondary/50 disabled:cursor-not-allowed",
                      index === currentStep && "bg-primary/10 border border-primary/20",
                      index < currentStep && "text-muted-foreground hover:text-foreground cursor-pointer",
                      index > currentStep && "text-muted-foreground/50"
                    )}
                  >
                    <div className={cn(
                      "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200",
                      index === currentStep && "border-primary bg-primary text-primary-foreground",
                      index < currentStep && "border-green-500 bg-green-500 text-white",
                      index > currentStep && "border-border bg-background"
                    )}>
                      {index < currentStep ? (
                        <Icon name="Check" className="h-5 w-5" />
                      ) : (
                        <Icon name={step.icon} className="h-5 w-5" />
                      )}
                    </div>
                    
                    <div className="text-left hidden md:block">
                      <div className="font-medium">{step.title}</div>
                      <div className="text-xs text-muted-foreground">{step.description}</div>
                    </div>
                  </button>
                  
                  {index < steps.length - 1 && (
                    <div className={cn(
                      "w-12 h-0.5 mx-2 transition-all duration-300",
                      index < currentStep ? "bg-green-500" : "bg-border"
                    )} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Step Content */}
        <div className="h-[calc(100vh-200px)] overflow-y-auto">
          <div className={cn(
            "transition-all duration-200",
            isAnimating && "opacity-50 scale-95"
          )}>
            <CurrentStepComponent
              onNext={handleNext}
              onBack={handleBack}
              onFinalize={handleFinalize}
              initialData={currentStepData}
              projectData={projectData}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="bg-card border-t border-border p-4">
          <div className="max-w-6xl mx-auto flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Step {currentStep + 1} of {steps.length}
            </div>
            
            <div className="flex items-center gap-2">
              <div className="w-48 bg-secondary rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                />
              </div>
              <span className="text-sm font-medium">
                {Math.round(((currentStep + 1) / steps.length) * 100)}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedProjectCreationWizard;
