"""
Project schemas
"""
from pydantic import BaseModel, validator
from typing import Optional, List
from datetime import datetime, date


class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None
    status: str = "active"
    priority: str = "medium"
    start_date: Optional[date] = None
    due_date: Optional[date] = None
    
    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('Project name cannot be empty')
        if len(v.strip()) < 2:
            raise ValueError('Project name must be at least 2 characters long')
        return v.strip()
    
    @validator('status')
    def validate_status(cls, v):
        if v not in ['active', 'completed', 'on_hold', 'cancelled']:
            raise ValueError('Status must be active, completed, on_hold, or cancelled')
        return v
    
    @validator('priority')
    def validate_priority(cls, v):
        if v not in ['low', 'medium', 'high', 'urgent']:
            raise ValueError('Priority must be low, medium, high, or urgent')
        return v


class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    priority: Optional[str] = None
    start_date: Optional[date] = None
    due_date: Optional[date] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None:
            if not v.strip():
                raise ValueError('Project name cannot be empty')
            if len(v.strip()) < 2:
                raise ValueError('Project name must be at least 2 characters long')
            return v.strip()
        return v
    
    @validator('status')
    def validate_status(cls, v):
        if v is not None and v not in ['active', 'completed', 'on_hold', 'cancelled']:
            raise ValueError('Status must be active, completed, on_hold, or cancelled')
        return v
    
    @validator('priority')
    def validate_priority(cls, v):
        if v is not None and v not in ['low', 'medium', 'high', 'urgent']:
            raise ValueError('Priority must be low, medium, high, or urgent')
        return v


class ProjectResponse(BaseModel):
    id: str
    organization_id: str
    name: str
    description: Optional[str] = None
    status: str
    priority: str
    start_date: Optional[date] = None
    due_date: Optional[date] = None
    created_by: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


class BoardCreate(BaseModel):
    name: str
    description: Optional[str] = None
    
    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('Board name cannot be empty')
        if len(v.strip()) < 2:
            raise ValueError('Board name must be at least 2 characters long')
        return v.strip()


class BoardUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None:
            if not v.strip():
                raise ValueError('Board name cannot be empty')
            if len(v.strip()) < 2:
                raise ValueError('Board name must be at least 2 characters long')
            return v.strip()
        return v


class BoardResponse(BaseModel):
    id: str
    project_id: str
    name: str
    description: Optional[str] = None
    created_by: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


class ColumnCreate(BaseModel):
    name: str
    position: int
    color: Optional[str] = None
    
    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('Column name cannot be empty')
        return v.strip()
    
    @validator('position')
    def validate_position(cls, v):
        if v < 0:
            raise ValueError('Position must be non-negative')
        return v


class ColumnUpdate(BaseModel):
    name: Optional[str] = None
    position: Optional[int] = None
    color: Optional[str] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and not v.strip():
            raise ValueError('Column name cannot be empty')
        return v.strip() if v else v
    
    @validator('position')
    def validate_position(cls, v):
        if v is not None and v < 0:
            raise ValueError('Position must be non-negative')
        return v


class ColumnOrderUpdate(BaseModel):
    column_orders: List[dict]  # [{"id": "col_id", "position": 0}, ...]


class ColumnResponse(BaseModel):
    id: str
    board_id: str
    name: str
    position: int
    color: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True
