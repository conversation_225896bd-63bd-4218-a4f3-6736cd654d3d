{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\modals\\\\CreateProjectModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport { Textarea } from '../ui/Textarea';\nimport Select from '../ui/Select';\nimport authService from '../../utils/authService';\nimport teamService from '../../utils/teamService';\nimport EnhancedProjectCreationWizard from '../project/EnhancedProjectCreationWizard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreateProjectModal = ({\n  isOpen,\n  onClose,\n  onCreateProject,\n  organizationId,\n  organizationName\n}) => {\n  _s();\n  const [useEnhancedWizard, setUseEnhancedWizard] = useState(true);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    budget: '',\n    startDate: '',\n    endDate: '',\n    projectManager: '',\n    teamMembers: []\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [teamMembers, setTeamMembers] = useState([]);\n  const [currentUserRole, setCurrentUserRole] = useState('member');\n\n  // Load team members and user role when modal opens\n  useEffect(() => {\n    if (isOpen && organizationId) {\n      loadTeamMembers();\n      loadUserRole();\n    }\n  }, [isOpen, organizationId]);\n  const loadTeamMembers = async () => {\n    try {\n      const members = await teamService.getTeamMembers(organizationId);\n      const memberOptions = members.map(member => ({\n        value: member.id,\n        label: `${member.name} (${member.email})`,\n        role: member.role\n      }));\n      setTeamMembers(memberOptions);\n    } catch (error) {\n      console.error('Failed to load team members:', error);\n    }\n  };\n  const loadUserRole = async () => {\n    try {\n      const userResponse = await authService.getCurrentUser();\n      if (userResponse.data && userResponse.data.user) {\n        setCurrentUserRole(userResponse.data.user.role || 'member');\n      }\n    } catch (error) {\n      console.error('Failed to load user role:', error);\n    }\n  };\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Validation\n    if (!formData.name.trim()) {\n      setError('Project name is required');\n      return;\n    }\n    if (formData.startDate && formData.endDate && new Date(formData.startDate) > new Date(formData.endDate)) {\n      setError('End date must be after start date');\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const projectData = {\n        name: formData.name.trim(),\n        description: formData.description.trim(),\n        startDate: formData.startDate || null,\n        endDate: formData.endDate || null,\n        projectManager: formData.projectManager || null,\n        teamMembers: formData.teamMembers || []\n      };\n\n      // Only include budget if user is owner\n      if (currentUserRole === 'owner' && formData.budget) {\n        projectData.budget = parseFloat(formData.budget);\n      }\n      await onCreateProject(projectData);\n\n      // Reset form and close modal\n      setFormData({\n        name: '',\n        description: '',\n        budget: '',\n        startDate: '',\n        endDate: '',\n        projectManager: '',\n        teamMembers: []\n      });\n      onClose();\n    } catch (err) {\n      setError(err.message || 'Failed to create project');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleClose = () => {\n    if (!isLoading) {\n      setFormData({\n        name: '',\n        description: '',\n        budget: '',\n        startDate: '',\n        endDate: '',\n        projectManager: '',\n        teamMembers: []\n      });\n      setError('');\n      onClose();\n    }\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900\",\n          children: \"Create New Project\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClose,\n          disabled: isLoading,\n          className: \"text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-blue-50 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-blue-800\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Organization:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), \" \", organizationName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"projectName\",\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Project Name *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"projectName\",\n            type: \"text\",\n            value: formData.name,\n            onChange: e => handleInputChange('name', e.target.value),\n            placeholder: \"Enter project name\",\n            disabled: isLoading,\n            className: \"w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"projectDescription\",\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Textarea, {\n            id: \"projectDescription\",\n            value: formData.description,\n            onChange: e => handleInputChange('description', e.target.value),\n            placeholder: \"Enter project description (optional)\",\n            disabled: isLoading,\n            rows: 3,\n            className: \"w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), currentUserRole === 'owner' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"projectBudget\",\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Budget (USD)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"projectBudget\",\n            type: \"number\",\n            value: formData.budget,\n            onChange: e => handleInputChange('budget', e.target.value),\n            placeholder: \"Enter project budget\",\n            disabled: isLoading,\n            className: \"w-full\",\n            min: \"0\",\n            step: \"0.01\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"startDate\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Start Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"startDate\",\n              type: \"date\",\n              value: formData.startDate,\n              onChange: e => handleInputChange('startDate', e.target.value),\n              disabled: isLoading,\n              className: \"w-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"endDate\",\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"End Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"endDate\",\n              type: \"date\",\n              value: formData.endDate,\n              onChange: e => handleInputChange('endDate', e.target.value),\n              disabled: isLoading,\n              className: \"w-full\",\n              min: formData.startDate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"projectManager\",\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Project Manager\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            id: \"projectManager\",\n            options: [{\n              value: '',\n              label: 'Select project manager (optional)'\n            }, ...teamMembers.filter(member => ['admin', 'owner'].includes(member.role))],\n            value: formData.projectManager,\n            onChange: value => handleInputChange('projectManager', value),\n            disabled: isLoading,\n            className: \"w-full\",\n            placeholder: \"Select project manager\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"teamMembers\",\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Team Members\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            id: \"teamMembers\",\n            options: teamMembers,\n            value: formData.teamMembers,\n            onChange: value => handleInputChange('teamMembers', value),\n            disabled: isLoading,\n            multiple: true,\n            className: \"w-full\",\n            placeholder: \"Select team members (optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-red-600\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-end gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"button\",\n            variant: \"outline\",\n            onClick: handleClose,\n            disabled: isLoading,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"default\",\n            disabled: isLoading || !formData.name.trim(),\n            iconName: isLoading ? \"Loader2\" : \"Plus\",\n            iconPosition: \"left\",\n            className: isLoading ? \"animate-spin\" : \"\",\n            children: isLoading ? 'Creating...' : 'Create Project'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateProjectModal, \"D/HUQfPQUH4CcTlIkEoBNw/G3P4=\");\n_c = CreateProjectModal;\nexport default CreateProjectModal;\nvar _c;\n$RefreshReg$(_c, \"CreateProjectModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "Input", "Textarea", "Select", "authService", "teamService", "EnhancedProjectCreationWizard", "jsxDEV", "_jsxDEV", "CreateProjectModal", "isOpen", "onClose", "onCreateProject", "organizationId", "organizationName", "_s", "useEnhancedWizard", "setUseEnhancedWizard", "formData", "setFormData", "name", "description", "budget", "startDate", "endDate", "projectManager", "teamMembers", "isLoading", "setIsLoading", "error", "setError", "setTeamMembers", "currentUserRole", "setCurrentUserRole", "loadTeamMembers", "loadUserRole", "members", "getTeamMembers", "memberOptions", "map", "member", "value", "id", "label", "email", "role", "console", "userResponse", "getCurrentUser", "data", "user", "handleInputChange", "field", "prev", "handleSubmit", "e", "preventDefault", "trim", "Date", "projectData", "parseFloat", "err", "message", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onSubmit", "htmlFor", "type", "onChange", "target", "placeholder", "rows", "min", "step", "options", "filter", "includes", "multiple", "variant", "iconName", "iconPosition", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/modals/CreateProjectModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport { Textarea } from '../ui/Textarea';\nimport Select from '../ui/Select';\nimport authService from '../../utils/authService';\nimport teamService from '../../utils/teamService';\nimport EnhancedProjectCreationWizard from '../project/EnhancedProjectCreationWizard';\n\nconst CreateProjectModal = ({ isOpen, onClose, onCreateProject, organizationId, organizationName }) => {\n  const [useEnhancedWizard, setUseEnhancedWizard] = useState(true);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    budget: '',\n    startDate: '',\n    endDate: '',\n    projectManager: '',\n    teamMembers: []\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [teamMembers, setTeamMembers] = useState([]);\n  const [currentUserRole, setCurrentUserRole] = useState('member');\n\n  // Load team members and user role when modal opens\n  useEffect(() => {\n    if (isOpen && organizationId) {\n      loadTeamMembers();\n      loadUserRole();\n    }\n  }, [isOpen, organizationId]);\n\n  const loadTeamMembers = async () => {\n    try {\n      const members = await teamService.getTeamMembers(organizationId);\n      const memberOptions = members.map(member => ({\n        value: member.id,\n        label: `${member.name} (${member.email})`,\n        role: member.role\n      }));\n      setTeamMembers(memberOptions);\n    } catch (error) {\n      console.error('Failed to load team members:', error);\n    }\n  };\n\n  const loadUserRole = async () => {\n    try {\n      const userResponse = await authService.getCurrentUser();\n      if (userResponse.data && userResponse.data.user) {\n        setCurrentUserRole(userResponse.data.user.role || 'member');\n      }\n    } catch (error) {\n      console.error('Failed to load user role:', error);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    // Validation\n    if (!formData.name.trim()) {\n      setError('Project name is required');\n      return;\n    }\n\n    if (formData.startDate && formData.endDate && new Date(formData.startDate) > new Date(formData.endDate)) {\n      setError('End date must be after start date');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const projectData = {\n        name: formData.name.trim(),\n        description: formData.description.trim(),\n        startDate: formData.startDate || null,\n        endDate: formData.endDate || null,\n        projectManager: formData.projectManager || null,\n        teamMembers: formData.teamMembers || []\n      };\n\n      // Only include budget if user is owner\n      if (currentUserRole === 'owner' && formData.budget) {\n        projectData.budget = parseFloat(formData.budget);\n      }\n\n      await onCreateProject(projectData);\n\n      // Reset form and close modal\n      setFormData({\n        name: '',\n        description: '',\n        budget: '',\n        startDate: '',\n        endDate: '',\n        projectManager: '',\n        teamMembers: []\n      });\n      onClose();\n    } catch (err) {\n      setError(err.message || 'Failed to create project');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isLoading) {\n      setFormData({\n        name: '',\n        description: '',\n        budget: '',\n        startDate: '',\n        endDate: '',\n        projectManager: '',\n        teamMembers: []\n      });\n      setError('');\n      onClose();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">Create New Project</h2>\n          <button\n            onClick={handleClose}\n            disabled={isLoading}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} className=\"p-6\">\n          {/* Organization Info */}\n          <div className=\"mb-4 p-3 bg-blue-50 rounded-lg\">\n            <p className=\"text-sm text-blue-800\">\n              <span className=\"font-medium\">Organization:</span> {organizationName}\n            </p>\n          </div>\n\n          {/* Project Name */}\n          <div className=\"mb-4\">\n            <label htmlFor=\"projectName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Project Name *\n            </label>\n            <Input\n              id=\"projectName\"\n              type=\"text\"\n              value={formData.name}\n              onChange={(e) => handleInputChange('name', e.target.value)}\n              placeholder=\"Enter project name\"\n              disabled={isLoading}\n              className=\"w-full\"\n            />\n          </div>\n\n          {/* Project Description */}\n          <div className=\"mb-4\">\n            <label htmlFor=\"projectDescription\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Description\n            </label>\n            <Textarea\n              id=\"projectDescription\"\n              value={formData.description}\n              onChange={(e) => handleInputChange('description', e.target.value)}\n              placeholder=\"Enter project description (optional)\"\n              disabled={isLoading}\n              rows={3}\n              className=\"w-full\"\n            />\n          </div>\n\n          {/* Budget - Only visible to owners */}\n          {currentUserRole === 'owner' && (\n            <div className=\"mb-4\">\n              <label htmlFor=\"projectBudget\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Budget (USD)\n              </label>\n              <Input\n                id=\"projectBudget\"\n                type=\"number\"\n                value={formData.budget}\n                onChange={(e) => handleInputChange('budget', e.target.value)}\n                placeholder=\"Enter project budget\"\n                disabled={isLoading}\n                className=\"w-full\"\n                min=\"0\"\n                step=\"0.01\"\n              />\n            </div>\n          )}\n\n          {/* Date Range */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n            <div>\n              <label htmlFor=\"startDate\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Start Date\n              </label>\n              <Input\n                id=\"startDate\"\n                type=\"date\"\n                value={formData.startDate}\n                onChange={(e) => handleInputChange('startDate', e.target.value)}\n                disabled={isLoading}\n                className=\"w-full\"\n              />\n            </div>\n            <div>\n              <label htmlFor=\"endDate\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                End Date\n              </label>\n              <Input\n                id=\"endDate\"\n                type=\"date\"\n                value={formData.endDate}\n                onChange={(e) => handleInputChange('endDate', e.target.value)}\n                disabled={isLoading}\n                className=\"w-full\"\n                min={formData.startDate}\n              />\n            </div>\n          </div>\n\n          {/* Project Manager */}\n          <div className=\"mb-4\">\n            <label htmlFor=\"projectManager\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Project Manager\n            </label>\n            <Select\n              id=\"projectManager\"\n              options={[\n                { value: '', label: 'Select project manager (optional)' },\n                ...teamMembers.filter(member => ['admin', 'owner'].includes(member.role))\n              ]}\n              value={formData.projectManager}\n              onChange={(value) => handleInputChange('projectManager', value)}\n              disabled={isLoading}\n              className=\"w-full\"\n              placeholder=\"Select project manager\"\n            />\n          </div>\n\n          {/* Team Members */}\n          <div className=\"mb-6\">\n            <label htmlFor=\"teamMembers\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Team Members\n            </label>\n            <Select\n              id=\"teamMembers\"\n              options={teamMembers}\n              value={formData.teamMembers}\n              onChange={(value) => handleInputChange('teamMembers', value)}\n              disabled={isLoading}\n              multiple={true}\n              className=\"w-full\"\n              placeholder=\"Select team members (optional)\"\n            />\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n              <p className=\"text-sm text-red-600\">{error}</p>\n            </div>\n          )}\n\n          {/* Actions */}\n          <div className=\"flex items-center justify-end gap-3\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={handleClose}\n              disabled={isLoading}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              variant=\"default\"\n              disabled={isLoading || !formData.name.trim()}\n              iconName={isLoading ? \"Loader2\" : \"Plus\"}\n              iconPosition=\"left\"\n              className={isLoading ? \"animate-spin\" : \"\"}\n            >\n              {isLoading ? 'Creating...' : 'Create Project'}\n            </Button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default CreateProjectModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,6BAA6B,MAAM,0CAA0C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,eAAe;EAAEC,cAAc;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACrG,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC;IACvCsB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,WAAW,EAAEK,cAAc,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,QAAQ,CAAC;;EAEhE;EACAC,SAAS,CAAC,MAAM;IACd,IAAIW,MAAM,IAAIG,cAAc,EAAE;MAC5BqB,eAAe,CAAC,CAAC;MACjBC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACzB,MAAM,EAAEG,cAAc,CAAC,CAAC;EAE5B,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAME,OAAO,GAAG,MAAM/B,WAAW,CAACgC,cAAc,CAACxB,cAAc,CAAC;MAChE,MAAMyB,aAAa,GAAGF,OAAO,CAACG,GAAG,CAACC,MAAM,KAAK;QAC3CC,KAAK,EAAED,MAAM,CAACE,EAAE;QAChBC,KAAK,EAAE,GAAGH,MAAM,CAACpB,IAAI,KAAKoB,MAAM,CAACI,KAAK,GAAG;QACzCC,IAAI,EAAEL,MAAM,CAACK;MACf,CAAC,CAAC,CAAC;MACHd,cAAc,CAACO,aAAa,CAAC;IAC/B,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMY,YAAY,GAAG,MAAM3C,WAAW,CAAC4C,cAAc,CAAC,CAAC;MACvD,IAAID,YAAY,CAACE,IAAI,IAAIF,YAAY,CAACE,IAAI,CAACC,IAAI,EAAE;QAC/CjB,kBAAkB,CAACc,YAAY,CAACE,IAAI,CAACC,IAAI,CAACL,IAAI,IAAI,QAAQ,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAMsB,iBAAiB,GAAGA,CAACC,KAAK,EAAEX,KAAK,KAAK;IAC1CtB,WAAW,CAACkC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACD,KAAK,GAAGX;IAAM,CAAC,CAAC,CAAC;IAClD,IAAIZ,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMwB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAACtC,QAAQ,CAACE,IAAI,CAACqC,IAAI,CAAC,CAAC,EAAE;MACzB3B,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;IAEA,IAAIZ,QAAQ,CAACK,SAAS,IAAIL,QAAQ,CAACM,OAAO,IAAI,IAAIkC,IAAI,CAACxC,QAAQ,CAACK,SAAS,CAAC,GAAG,IAAImC,IAAI,CAACxC,QAAQ,CAACM,OAAO,CAAC,EAAE;MACvGM,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEAF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAM6B,WAAW,GAAG;QAClBvC,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACqC,IAAI,CAAC,CAAC;QAC1BpC,WAAW,EAAEH,QAAQ,CAACG,WAAW,CAACoC,IAAI,CAAC,CAAC;QACxClC,SAAS,EAAEL,QAAQ,CAACK,SAAS,IAAI,IAAI;QACrCC,OAAO,EAAEN,QAAQ,CAACM,OAAO,IAAI,IAAI;QACjCC,cAAc,EAAEP,QAAQ,CAACO,cAAc,IAAI,IAAI;QAC/CC,WAAW,EAAER,QAAQ,CAACQ,WAAW,IAAI;MACvC,CAAC;;MAED;MACA,IAAIM,eAAe,KAAK,OAAO,IAAId,QAAQ,CAACI,MAAM,EAAE;QAClDqC,WAAW,CAACrC,MAAM,GAAGsC,UAAU,CAAC1C,QAAQ,CAACI,MAAM,CAAC;MAClD;MAEA,MAAMV,eAAe,CAAC+C,WAAW,CAAC;;MAElC;MACAxC,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,EAAE;QACbC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE;MACf,CAAC,CAAC;MACFf,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOkD,GAAG,EAAE;MACZ/B,QAAQ,CAAC+B,GAAG,CAACC,OAAO,IAAI,0BAA0B,CAAC;IACrD,CAAC,SAAS;MACRlC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACpC,SAAS,EAAE;MACdR,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE,EAAE;QACbC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE;MACf,CAAC,CAAC;MACFI,QAAQ,CAAC,EAAE,CAAC;MACZnB,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKwD,SAAS,EAAC,gFAAgF;IAAAC,QAAA,eAC7FzD,OAAA;MAAKwD,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBAE1FzD,OAAA;QAAKwD,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC7EzD,OAAA;UAAIwD,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E7D,OAAA;UACE8D,OAAO,EAAEP,WAAY;UACrBQ,QAAQ,EAAE5C,SAAU;UACpBqC,SAAS,EAAC,yEAAyE;UAAAC,QAAA,eAEnFzD,OAAA;YAAKwD,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAT,QAAA,eAC5EzD,OAAA;cAAMmE,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN7D,OAAA;QAAMuE,QAAQ,EAAEzB,YAAa;QAACU,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAE3CzD,OAAA;UAAKwD,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC7CzD,OAAA;YAAGwD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClCzD,OAAA;cAAMwD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC,EAACvD,gBAAgB;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGN7D,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzD,OAAA;YAAOwE,OAAO,EAAC,aAAa;YAAChB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEtF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR7D,OAAA,CAACP,KAAK;YACJyC,EAAE,EAAC,aAAa;YAChBuC,IAAI,EAAC,MAAM;YACXxC,KAAK,EAAEvB,QAAQ,CAACE,IAAK;YACrB8D,QAAQ,EAAG3B,CAAC,IAAKJ,iBAAiB,CAAC,MAAM,EAAEI,CAAC,CAAC4B,MAAM,CAAC1C,KAAK,CAAE;YAC3D2C,WAAW,EAAC,oBAAoB;YAChCb,QAAQ,EAAE5C,SAAU;YACpBqC,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN7D,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzD,OAAA;YAAOwE,OAAO,EAAC,oBAAoB;YAAChB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAE7F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR7D,OAAA,CAACN,QAAQ;YACPwC,EAAE,EAAC,oBAAoB;YACvBD,KAAK,EAAEvB,QAAQ,CAACG,WAAY;YAC5B6D,QAAQ,EAAG3B,CAAC,IAAKJ,iBAAiB,CAAC,aAAa,EAAEI,CAAC,CAAC4B,MAAM,CAAC1C,KAAK,CAAE;YAClE2C,WAAW,EAAC,sCAAsC;YAClDb,QAAQ,EAAE5C,SAAU;YACpB0D,IAAI,EAAE,CAAE;YACRrB,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGLrC,eAAe,KAAK,OAAO,iBAC1BxB,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzD,OAAA;YAAOwE,OAAO,EAAC,eAAe;YAAChB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAExF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR7D,OAAA,CAACP,KAAK;YACJyC,EAAE,EAAC,eAAe;YAClBuC,IAAI,EAAC,QAAQ;YACbxC,KAAK,EAAEvB,QAAQ,CAACI,MAAO;YACvB4D,QAAQ,EAAG3B,CAAC,IAAKJ,iBAAiB,CAAC,QAAQ,EAAEI,CAAC,CAAC4B,MAAM,CAAC1C,KAAK,CAAE;YAC7D2C,WAAW,EAAC,sBAAsB;YAClCb,QAAQ,EAAE5C,SAAU;YACpBqC,SAAS,EAAC,QAAQ;YAClBsB,GAAG,EAAC,GAAG;YACPC,IAAI,EAAC;UAAM;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGD7D,OAAA;UAAKwD,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzDzD,OAAA;YAAAyD,QAAA,gBACEzD,OAAA;cAAOwE,OAAO,EAAC,WAAW;cAAChB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEpF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7D,OAAA,CAACP,KAAK;cACJyC,EAAE,EAAC,WAAW;cACduC,IAAI,EAAC,MAAM;cACXxC,KAAK,EAAEvB,QAAQ,CAACK,SAAU;cAC1B2D,QAAQ,EAAG3B,CAAC,IAAKJ,iBAAiB,CAAC,WAAW,EAAEI,CAAC,CAAC4B,MAAM,CAAC1C,KAAK,CAAE;cAChE8B,QAAQ,EAAE5C,SAAU;cACpBqC,SAAS,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN7D,OAAA;YAAAyD,QAAA,gBACEzD,OAAA;cAAOwE,OAAO,EAAC,SAAS;cAAChB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAElF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7D,OAAA,CAACP,KAAK;cACJyC,EAAE,EAAC,SAAS;cACZuC,IAAI,EAAC,MAAM;cACXxC,KAAK,EAAEvB,QAAQ,CAACM,OAAQ;cACxB0D,QAAQ,EAAG3B,CAAC,IAAKJ,iBAAiB,CAAC,SAAS,EAAEI,CAAC,CAAC4B,MAAM,CAAC1C,KAAK,CAAE;cAC9D8B,QAAQ,EAAE5C,SAAU;cACpBqC,SAAS,EAAC,QAAQ;cAClBsB,GAAG,EAAEpE,QAAQ,CAACK;YAAU;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7D,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzD,OAAA;YAAOwE,OAAO,EAAC,gBAAgB;YAAChB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEzF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR7D,OAAA,CAACL,MAAM;YACLuC,EAAE,EAAC,gBAAgB;YACnB8C,OAAO,EAAE,CACP;cAAE/C,KAAK,EAAE,EAAE;cAAEE,KAAK,EAAE;YAAoC,CAAC,EACzD,GAAGjB,WAAW,CAAC+D,MAAM,CAACjD,MAAM,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAACkD,QAAQ,CAAClD,MAAM,CAACK,IAAI,CAAC,CAAC,CACzE;YACFJ,KAAK,EAAEvB,QAAQ,CAACO,cAAe;YAC/ByD,QAAQ,EAAGzC,KAAK,IAAKU,iBAAiB,CAAC,gBAAgB,EAAEV,KAAK,CAAE;YAChE8B,QAAQ,EAAE5C,SAAU;YACpBqC,SAAS,EAAC,QAAQ;YAClBoB,WAAW,EAAC;UAAwB;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN7D,OAAA;UAAKwD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzD,OAAA;YAAOwE,OAAO,EAAC,aAAa;YAAChB,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEtF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR7D,OAAA,CAACL,MAAM;YACLuC,EAAE,EAAC,aAAa;YAChB8C,OAAO,EAAE9D,WAAY;YACrBe,KAAK,EAAEvB,QAAQ,CAACQ,WAAY;YAC5BwD,QAAQ,EAAGzC,KAAK,IAAKU,iBAAiB,CAAC,aAAa,EAAEV,KAAK,CAAE;YAC7D8B,QAAQ,EAAE5C,SAAU;YACpBgE,QAAQ,EAAE,IAAK;YACf3B,SAAS,EAAC,QAAQ;YAClBoB,WAAW,EAAC;UAAgC;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGLxC,KAAK,iBACJrB,OAAA;UAAKwD,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAClEzD,OAAA;YAAGwD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEpC;UAAK;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACN,eAGD7D,OAAA;UAAKwD,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClDzD,OAAA,CAACR,MAAM;YACLiF,IAAI,EAAC,QAAQ;YACbW,OAAO,EAAC,SAAS;YACjBtB,OAAO,EAAEP,WAAY;YACrBQ,QAAQ,EAAE5C,SAAU;YAAAsC,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7D,OAAA,CAACR,MAAM;YACLiF,IAAI,EAAC,QAAQ;YACbW,OAAO,EAAC,SAAS;YACjBrB,QAAQ,EAAE5C,SAAS,IAAI,CAACT,QAAQ,CAACE,IAAI,CAACqC,IAAI,CAAC,CAAE;YAC7CoC,QAAQ,EAAElE,SAAS,GAAG,SAAS,GAAG,MAAO;YACzCmE,YAAY,EAAC,MAAM;YACnB9B,SAAS,EAAErC,SAAS,GAAG,cAAc,GAAG,EAAG;YAAAsC,QAAA,EAE1CtC,SAAS,GAAG,aAAa,GAAG;UAAgB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CA7SIN,kBAAkB;AAAAsF,EAAA,GAAlBtF,kBAAkB;AA+SxB,eAAeA,kBAAkB;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}