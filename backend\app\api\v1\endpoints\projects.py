"""
Project management endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.deps import get_current_active_user, get_organization_member, require_member
from app.core.exceptions import ValidationError, ResourceNotFoundError, InsufficientPermissionsError
from app.models.user import User
from app.models.organization import OrganizationMember
from app.models.project import Project
from app.schemas.project import ProjectCreate, ProjectUpdate, ProjectResponse

router = APIRouter()


@router.get("", response_model=List[ProjectResponse])
async def get_projects(
    organization_id: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get projects for user"""
    offset = (page - 1) * limit
    
    if organization_id:
        # Check if user is member of organization
        org_member = await get_organization_member(organization_id, current_user, db)
        if not org_member:
            raise InsufficientPermissionsError("Not a member of this organization")
        
        # Get projects for organization
        result = await db.execute(
            select(Project)
            .where(Project.organization_id == organization_id)
            .offset(offset)
            .limit(limit)
            .order_by(Project.created_at.desc())
        )
    else:
        # Get all projects for user's organizations
        result = await db.execute(
            select(Project)
            .join(OrganizationMember, Project.organization_id == OrganizationMember.organization_id)
            .where(OrganizationMember.user_id == current_user.id)
            .offset(offset)
            .limit(limit)
            .order_by(Project.created_at.desc())
        )
    
    projects = result.scalars().all()
    return [ProjectResponse.from_orm(project) for project in projects]


@router.post("", response_model=ProjectResponse)
async def create_project(
    project_data: ProjectCreate,
    organization_id: str = Query(...),
    current_user: User = Depends(get_current_active_user),
    org_member: OrganizationMember = Depends(require_member),
    db: AsyncSession = Depends(get_db)
):
    """Create a new project"""
    project = Project(
        organization_id=organization_id,
        name=project_data.name,
        description=project_data.description,
        status=project_data.status,
        priority=project_data.priority,
        start_date=project_data.start_date,
        due_date=project_data.due_date,
        created_by=current_user.id
    )
    
    db.add(project)
    await db.commit()
    await db.refresh(project)
    
    return ProjectResponse.from_orm(project)


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get project by ID"""
    result = await db.execute(
        select(Project).where(Project.id == project_id)
    )
    project = result.scalar_one_or_none()
    if not project:
        raise ResourceNotFoundError("Project not found")
    
    # Check if user has access to this project's organization
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    if not org_member_result.scalar_one_or_none():
        raise InsufficientPermissionsError("Access denied")
    
    return ProjectResponse.from_orm(project)


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: str,
    project_data: ProjectUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update project"""
    result = await db.execute(
        select(Project).where(Project.id == project_id)
    )
    project = result.scalar_one_or_none()
    if not project:
        raise ResourceNotFoundError("Project not found")
    
    # Check if user has access to this project's organization
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")
    
    # Check permissions (member or above can edit)
    if org_member.role not in ['member', 'admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions to edit project")
    
    # Update fields if provided
    if project_data.name is not None:
        project.name = project_data.name
    if project_data.description is not None:
        project.description = project_data.description
    if project_data.status is not None:
        project.status = project_data.status
    if project_data.priority is not None:
        project.priority = project_data.priority
    if project_data.start_date is not None:
        project.start_date = project_data.start_date
    if project_data.due_date is not None:
        project.due_date = project_data.due_date
    
    await db.commit()
    await db.refresh(project)
    
    return ProjectResponse.from_orm(project)


@router.delete("/{project_id}")
async def delete_project(
    project_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete project"""
    result = await db.execute(
        select(Project).where(Project.id == project_id)
    )
    project = result.scalar_one_or_none()
    if not project:
        raise ResourceNotFoundError("Project not found")
    
    # Check if user has access to this project's organization
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")
    
    # Check permissions (admin or above can delete)
    if org_member.role not in ['admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions to delete project")
    
    # Delete project (cascade will handle boards, columns, cards, etc.)
    await db.delete(project)
    await db.commit()
    
    return {"success": True, "message": "Project deleted successfully"}
