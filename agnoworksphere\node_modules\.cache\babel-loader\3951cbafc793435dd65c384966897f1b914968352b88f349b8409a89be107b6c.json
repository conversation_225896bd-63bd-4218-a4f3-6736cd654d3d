{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\project\\\\ProjectOverviewEditor.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport RichTextEditor from '../ui/RichTextEditor';\nimport DatePicker from '../ui/DatePicker';\nimport Icon from '../AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProjectOverviewEditor = ({\n  onNext,\n  onBack,\n  initialData = {},\n  className\n}) => {\n  _s();\n  const [overview, setOverview] = useState({\n    title: '',\n    description: '',\n    objectives: [''],\n    deliverables: [''],\n    kpis: [{\n      name: '',\n      target: '',\n      measurement: ''\n    }],\n    timeline: {\n      startDate: '',\n      endDate: '',\n      milestones: [{\n        name: '',\n        date: '',\n        description: ''\n      }]\n    },\n    stakeholders: [''],\n    riskAssessment: '',\n    successCriteria: '',\n    ...initialData\n  });\n  const [errors, setErrors] = useState({});\n  const [isValid, setIsValid] = useState(false);\n  useEffect(() => {\n    validateOverview();\n  }, [overview]);\n  const validateOverview = () => {\n    const newErrors = {};\n    if (!overview.title.trim()) {\n      newErrors.title = 'Project title is required';\n    }\n    if (!overview.description.trim()) {\n      newErrors.description = 'Project description is required';\n    }\n    if (overview.description.length > 10000) {\n      newErrors.description = 'Description must be under 10,000 characters';\n    }\n    if (overview.objectives.filter(obj => obj.trim()).length === 0) {\n      newErrors.objectives = 'At least one objective is required';\n    }\n    if (overview.deliverables.filter(del => del.trim()).length === 0) {\n      newErrors.deliverables = 'At least one deliverable is required';\n    }\n    setErrors(newErrors);\n    setIsValid(Object.keys(newErrors).length === 0);\n  };\n  const updateOverview = (path, value) => {\n    setOverview(prev => {\n      const newOverview = {\n        ...prev\n      };\n      const keys = path.split('.');\n      let current = newOverview;\n      for (let i = 0; i < keys.length - 1; i++) {\n        current = current[keys[i]];\n      }\n      current[keys[keys.length - 1]] = value;\n      return newOverview;\n    });\n  };\n  const addArrayItem = (path, defaultValue = '') => {\n    const current = path.split('.').reduce((obj, key) => obj[key], overview);\n    const newArray = [...current, defaultValue];\n    updateOverview(path, newArray);\n  };\n  const removeArrayItem = (path, index) => {\n    const current = path.split('.').reduce((obj, key) => obj[key], overview);\n    const newArray = current.filter((_, i) => i !== index);\n    updateOverview(path, newArray);\n  };\n  const updateArrayItem = (path, index, value) => {\n    const current = path.split('.').reduce((obj, key) => obj[key], overview);\n    const newArray = [...current];\n    newArray[index] = value;\n    updateOverview(path, newArray);\n  };\n  const handleNext = () => {\n    if (isValid) {\n      onNext === null || onNext === void 0 ? void 0 : onNext(overview);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn(\"max-w-4xl mx-auto p-6 space-y-8\", className),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-3xl font-bold text-foreground\",\n        children: \"Project Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-muted-foreground\",\n        children: \"Define your project's vision, goals, and success metrics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"FileText\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), \"Basic Information\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          label: \"Project Title\",\n          value: overview.title,\n          onChange: e => updateOverview('title', e.target.value),\n          placeholder: \"Enter project title\",\n          error: errors.title,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RichTextEditor, {\n          label: \"Project Description\",\n          value: overview.description,\n          onChange: value => updateOverview('description', value),\n          placeholder: \"Provide a comprehensive description of your project...\",\n          maxLength: 10000,\n          error: errors.description,\n          description: \"Describe the project scope, background, and context (up to 10,000 characters)\",\n          showWordCount: true,\n          allowMedia: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-foreground flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Target\",\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), \"Project Objectives\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          size: \"sm\",\n          onClick: () => addArrayItem('objectives'),\n          iconName: \"Plus\",\n          iconPosition: \"left\",\n          children: \"Add Objective\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: overview.objectives.map((objective, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            value: objective,\n            onChange: e => updateArrayItem('objectives', index, e.target.value),\n            placeholder: `Objective ${index + 1}`,\n            className: \"flex-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this), overview.objectives.length > 1 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"ghost\",\n            size: \"sm\",\n            onClick: () => removeArrayItem('objectives', index),\n            iconName: \"X\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), errors.objectives && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-destructive text-sm\",\n        children: errors.objectives\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 31\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-foreground flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Package\",\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), \"Key Deliverables\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          size: \"sm\",\n          onClick: () => addArrayItem('deliverables'),\n          iconName: \"Plus\",\n          iconPosition: \"left\",\n          children: \"Add Deliverable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: overview.deliverables.map((deliverable, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            value: deliverable,\n            onChange: e => updateArrayItem('deliverables', index, e.target.value),\n            placeholder: `Deliverable ${index + 1}`,\n            className: \"flex-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this), overview.deliverables.length > 1 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"ghost\",\n            size: \"sm\",\n            onClick: () => removeArrayItem('deliverables', index),\n            iconName: \"X\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), errors.deliverables && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-destructive text-sm\",\n        children: errors.deliverables\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-semibold text-foreground flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"BarChart3\",\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), \"Key Performance Indicators\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          size: \"sm\",\n          onClick: () => addArrayItem('kpis', {\n            name: '',\n            target: '',\n            measurement: ''\n          }),\n          iconName: \"Plus\",\n          iconPosition: \"left\",\n          children: \"Add KPI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: overview.kpis.map((kpi, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-3 p-4 border border-border rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            label: \"KPI Name\",\n            value: kpi.name,\n            onChange: e => updateArrayItem('kpis', index, {\n              ...kpi,\n              name: e.target.value\n            }),\n            placeholder: \"e.g., User Satisfaction\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Target Value\",\n            value: kpi.target,\n            onChange: e => updateArrayItem('kpis', index, {\n              ...kpi,\n              target: e.target.value\n            }),\n            placeholder: \"e.g., 95%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              label: \"Measurement Method\",\n              value: kpi.measurement,\n              onChange: e => updateArrayItem('kpis', index, {\n                ...kpi,\n                measurement: e.target.value\n              }),\n              placeholder: \"e.g., Survey scores\",\n              className: \"flex-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), overview.kpis.length > 1 && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"ghost\",\n              size: \"sm\",\n              onClick: () => removeArrayItem('kpis', index),\n              iconName: \"X\",\n              className: \"mt-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-xl font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Calendar\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), \"Project Timeline\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(DatePicker, {\n          label: \"Start Date\",\n          value: overview.timeline.startDate,\n          onChange: value => updateOverview('timeline.startDate', value),\n          description: \"Project kickoff date\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n          label: \"End Date\",\n          value: overview.timeline.endDate,\n          onChange: value => updateOverview('timeline.endDate', value),\n          description: \"Expected completion date\",\n          minDate: overview.timeline.startDate\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-lg font-medium text-foreground\",\n            children: \"Milestones\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            size: \"sm\",\n            onClick: () => addArrayItem('timeline.milestones', {\n              name: '',\n              date: '',\n              description: ''\n            }),\n            iconName: \"Plus\",\n            iconPosition: \"left\",\n            children: \"Add Milestone\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), overview.timeline.milestones.map((milestone, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-3 p-4 border border-border rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            label: \"Milestone Name\",\n            value: milestone.name,\n            onChange: e => updateArrayItem('timeline.milestones', index, {\n              ...milestone,\n              name: e.target.value\n            }),\n            placeholder: \"e.g., MVP Release\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n            label: \"Target Date\",\n            value: milestone.date,\n            onChange: value => updateArrayItem('timeline.milestones', index, {\n              ...milestone,\n              date: value\n            }),\n            minDate: overview.timeline.startDate,\n            maxDate: overview.timeline.endDate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              label: \"Description\",\n              value: milestone.description,\n              onChange: e => updateArrayItem('timeline.milestones', index, {\n                ...milestone,\n                description: e.target.value\n              }),\n              placeholder: \"Brief description\",\n              className: \"flex-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this), overview.timeline.milestones.length > 1 && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"ghost\",\n              size: \"sm\",\n              onClick: () => removeArrayItem('timeline.milestones', index),\n              iconName: \"X\",\n              className: \"mt-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Users\",\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), \"Key Stakeholders\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [overview.stakeholders.map((stakeholder, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              value: stakeholder,\n              onChange: e => updateArrayItem('stakeholders', index, e.target.value),\n              placeholder: `Stakeholder ${index + 1}`,\n              className: \"flex-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), overview.stakeholders.length > 1 && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"ghost\",\n              size: \"sm\",\n              onClick: () => removeArrayItem('stakeholders', index),\n              iconName: \"X\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            size: \"sm\",\n            onClick: () => addArrayItem('stakeholders'),\n            iconName: \"Plus\",\n            iconPosition: \"left\",\n            className: \"w-full\",\n            children: \"Add Stakeholder\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"AlertTriangle\",\n            className: \"h-5 w-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), \"Risk Assessment\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RichTextEditor, {\n          value: overview.riskAssessment,\n          onChange: value => updateOverview('riskAssessment', value),\n          placeholder: \"Identify potential risks and mitigation strategies...\",\n          maxLength: 2000,\n          showToolbar: false,\n          showWordCount: true,\n          className: \"min-h-[120px]\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-foreground flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"CheckCircle\",\n          className: \"h-5 w-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), \"Success Criteria\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RichTextEditor, {\n        value: overview.successCriteria,\n        onChange: value => updateOverview('successCriteria', value),\n        placeholder: \"Define what success looks like for this project...\",\n        maxLength: 2000,\n        showToolbar: false,\n        showWordCount: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center pt-6\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        onClick: onBack,\n        iconName: \"ArrowLeft\",\n        iconPosition: \"left\",\n        children: \"Back to Configuration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-muted-foreground\",\n          children: \"Step 2 of 6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          disabled: !isValid,\n          iconName: \"ArrowRight\",\n          iconPosition: \"right\",\n          children: \"Continue to Tech Stack\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectOverviewEditor, \"6BecOm2uSiqyhDZx1gDb9Ye04sE=\");\n_c = ProjectOverviewEditor;\nexport default ProjectOverviewEditor;\nvar _c;\n$RefreshReg$(_c, \"ProjectOverviewEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "cn", "<PERSON><PERSON>", "Input", "RichTextEditor", "DatePicker", "Icon", "jsxDEV", "_jsxDEV", "ProjectOverviewEditor", "onNext", "onBack", "initialData", "className", "_s", "overview", "setOverview", "title", "description", "objectives", "deliverables", "kpis", "name", "target", "measurement", "timeline", "startDate", "endDate", "milestones", "date", "stakeholders", "riskAssessment", "successCriteria", "errors", "setErrors", "<PERSON><PERSON><PERSON><PERSON>", "setIsValid", "validateOverview", "newErrors", "trim", "length", "filter", "obj", "del", "Object", "keys", "updateOverview", "path", "value", "prev", "newOverview", "split", "current", "i", "addArrayItem", "defaultValue", "reduce", "key", "newArray", "removeArrayItem", "index", "_", "updateArrayItem", "handleNext", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "onChange", "e", "placeholder", "error", "required", "max<PERSON><PERSON><PERSON>", "showWordCount", "allowMedia", "variant", "size", "onClick", "iconName", "iconPosition", "map", "objective", "deliverable", "kpi", "minDate", "milestone", "maxDate", "stakeholder", "showToolbar", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/project/ProjectOverviewEditor.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport RichTextEditor from '../ui/RichTextEditor';\nimport DatePicker from '../ui/DatePicker';\nimport Icon from '../AppIcon';\n\nconst ProjectOverviewEditor = ({ \n  onNext, \n  onBack, \n  initialData = {}, \n  className \n}) => {\n  const [overview, setOverview] = useState({\n    title: '',\n    description: '',\n    objectives: [''],\n    deliverables: [''],\n    kpis: [{ name: '', target: '', measurement: '' }],\n    timeline: {\n      startDate: '',\n      endDate: '',\n      milestones: [{ name: '', date: '', description: '' }]\n    },\n    stakeholders: [''],\n    riskAssessment: '',\n    successCriteria: '',\n    ...initialData\n  });\n\n  const [errors, setErrors] = useState({});\n  const [isValid, setIsValid] = useState(false);\n\n  useEffect(() => {\n    validateOverview();\n  }, [overview]);\n\n  const validateOverview = () => {\n    const newErrors = {};\n    \n    if (!overview.title.trim()) {\n      newErrors.title = 'Project title is required';\n    }\n    \n    if (!overview.description.trim()) {\n      newErrors.description = 'Project description is required';\n    }\n    \n    if (overview.description.length > 10000) {\n      newErrors.description = 'Description must be under 10,000 characters';\n    }\n    \n    if (overview.objectives.filter(obj => obj.trim()).length === 0) {\n      newErrors.objectives = 'At least one objective is required';\n    }\n    \n    if (overview.deliverables.filter(del => del.trim()).length === 0) {\n      newErrors.deliverables = 'At least one deliverable is required';\n    }\n\n    setErrors(newErrors);\n    setIsValid(Object.keys(newErrors).length === 0);\n  };\n\n  const updateOverview = (path, value) => {\n    setOverview(prev => {\n      const newOverview = { ...prev };\n      const keys = path.split('.');\n      let current = newOverview;\n      \n      for (let i = 0; i < keys.length - 1; i++) {\n        current = current[keys[i]];\n      }\n      \n      current[keys[keys.length - 1]] = value;\n      return newOverview;\n    });\n  };\n\n  const addArrayItem = (path, defaultValue = '') => {\n    const current = path.split('.').reduce((obj, key) => obj[key], overview);\n    const newArray = [...current, defaultValue];\n    updateOverview(path, newArray);\n  };\n\n  const removeArrayItem = (path, index) => {\n    const current = path.split('.').reduce((obj, key) => obj[key], overview);\n    const newArray = current.filter((_, i) => i !== index);\n    updateOverview(path, newArray);\n  };\n\n  const updateArrayItem = (path, index, value) => {\n    const current = path.split('.').reduce((obj, key) => obj[key], overview);\n    const newArray = [...current];\n    newArray[index] = value;\n    updateOverview(path, newArray);\n  };\n\n  const handleNext = () => {\n    if (isValid) {\n      onNext?.(overview);\n    }\n  };\n\n  return (\n    <div className={cn(\"max-w-4xl mx-auto p-6 space-y-8\", className)}>\n      {/* Header */}\n      <div className=\"text-center space-y-2\">\n        <h2 className=\"text-3xl font-bold text-foreground\">Project Overview</h2>\n        <p className=\"text-muted-foreground\">\n          Define your project's vision, goals, and success metrics\n        </p>\n      </div>\n\n      {/* Basic Information */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-6\">\n        <h3 className=\"text-xl font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"FileText\" className=\"h-5 w-5\" />\n          Basic Information\n        </h3>\n        \n        <div className=\"space-y-4\">\n          <Input\n            label=\"Project Title\"\n            value={overview.title}\n            onChange={(e) => updateOverview('title', e.target.value)}\n            placeholder=\"Enter project title\"\n            error={errors.title}\n            required\n          />\n          \n          <RichTextEditor\n            label=\"Project Description\"\n            value={overview.description}\n            onChange={(value) => updateOverview('description', value)}\n            placeholder=\"Provide a comprehensive description of your project...\"\n            maxLength={10000}\n            error={errors.description}\n            description=\"Describe the project scope, background, and context (up to 10,000 characters)\"\n            showWordCount\n            allowMedia\n          />\n        </div>\n      </div>\n\n      {/* Objectives Section */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-6\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-xl font-semibold text-foreground flex items-center gap-2\">\n            <Icon name=\"Target\" className=\"h-5 w-5\" />\n            Project Objectives\n          </h3>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => addArrayItem('objectives')}\n            iconName=\"Plus\"\n            iconPosition=\"left\"\n          >\n            Add Objective\n          </Button>\n        </div>\n        \n        <div className=\"space-y-3\">\n          {overview.objectives.map((objective, index) => (\n            <div key={index} className=\"flex gap-2\">\n              <Input\n                value={objective}\n                onChange={(e) => updateArrayItem('objectives', index, e.target.value)}\n                placeholder={`Objective ${index + 1}`}\n                className=\"flex-1\"\n              />\n              {overview.objectives.length > 1 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => removeArrayItem('objectives', index)}\n                  iconName=\"X\"\n                />\n              )}\n            </div>\n          ))}\n        </div>\n        \n        {errors.objectives && <p className=\"text-destructive text-sm\">{errors.objectives}</p>}\n      </div>\n\n      {/* Deliverables Section */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-6\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-xl font-semibold text-foreground flex items-center gap-2\">\n            <Icon name=\"Package\" className=\"h-5 w-5\" />\n            Key Deliverables\n          </h3>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => addArrayItem('deliverables')}\n            iconName=\"Plus\"\n            iconPosition=\"left\"\n          >\n            Add Deliverable\n          </Button>\n        </div>\n        \n        <div className=\"space-y-3\">\n          {overview.deliverables.map((deliverable, index) => (\n            <div key={index} className=\"flex gap-2\">\n              <Input\n                value={deliverable}\n                onChange={(e) => updateArrayItem('deliverables', index, e.target.value)}\n                placeholder={`Deliverable ${index + 1}`}\n                className=\"flex-1\"\n              />\n              {overview.deliverables.length > 1 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => removeArrayItem('deliverables', index)}\n                  iconName=\"X\"\n                />\n              )}\n            </div>\n          ))}\n        </div>\n        \n        {errors.deliverables && <p className=\"text-destructive text-sm\">{errors.deliverables}</p>}\n      </div>\n\n      {/* KPIs Section */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-6\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-xl font-semibold text-foreground flex items-center gap-2\">\n            <Icon name=\"BarChart3\" className=\"h-5 w-5\" />\n            Key Performance Indicators\n          </h3>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => addArrayItem('kpis', { name: '', target: '', measurement: '' })}\n            iconName=\"Plus\"\n            iconPosition=\"left\"\n          >\n            Add KPI\n          </Button>\n        </div>\n        \n        <div className=\"space-y-4\">\n          {overview.kpis.map((kpi, index) => (\n            <div key={index} className=\"grid grid-cols-1 md:grid-cols-3 gap-3 p-4 border border-border rounded-lg\">\n              <Input\n                label=\"KPI Name\"\n                value={kpi.name}\n                onChange={(e) => updateArrayItem('kpis', index, { ...kpi, name: e.target.value })}\n                placeholder=\"e.g., User Satisfaction\"\n              />\n              <Input\n                label=\"Target Value\"\n                value={kpi.target}\n                onChange={(e) => updateArrayItem('kpis', index, { ...kpi, target: e.target.value })}\n                placeholder=\"e.g., 95%\"\n              />\n              <div className=\"flex gap-2\">\n                <Input\n                  label=\"Measurement Method\"\n                  value={kpi.measurement}\n                  onChange={(e) => updateArrayItem('kpis', index, { ...kpi, measurement: e.target.value })}\n                  placeholder=\"e.g., Survey scores\"\n                  className=\"flex-1\"\n                />\n                {overview.kpis.length > 1 && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => removeArrayItem('kpis', index)}\n                    iconName=\"X\"\n                    className=\"mt-6\"\n                  />\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Timeline Section */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-6\">\n        <h3 className=\"text-xl font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"Calendar\" className=\"h-5 w-5\" />\n          Project Timeline\n        </h3>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <DatePicker\n            label=\"Start Date\"\n            value={overview.timeline.startDate}\n            onChange={(value) => updateOverview('timeline.startDate', value)}\n            description=\"Project kickoff date\"\n          />\n          <DatePicker\n            label=\"End Date\"\n            value={overview.timeline.endDate}\n            onChange={(value) => updateOverview('timeline.endDate', value)}\n            description=\"Expected completion date\"\n            minDate={overview.timeline.startDate}\n          />\n        </div>\n\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <h4 className=\"text-lg font-medium text-foreground\">Milestones</h4>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => addArrayItem('timeline.milestones', { name: '', date: '', description: '' })}\n              iconName=\"Plus\"\n              iconPosition=\"left\"\n            >\n              Add Milestone\n            </Button>\n          </div>\n\n          {overview.timeline.milestones.map((milestone, index) => (\n            <div key={index} className=\"grid grid-cols-1 md:grid-cols-3 gap-3 p-4 border border-border rounded-lg\">\n              <Input\n                label=\"Milestone Name\"\n                value={milestone.name}\n                onChange={(e) => updateArrayItem('timeline.milestones', index, { ...milestone, name: e.target.value })}\n                placeholder=\"e.g., MVP Release\"\n              />\n              <DatePicker\n                label=\"Target Date\"\n                value={milestone.date}\n                onChange={(value) => updateArrayItem('timeline.milestones', index, { ...milestone, date: value })}\n                minDate={overview.timeline.startDate}\n                maxDate={overview.timeline.endDate}\n              />\n              <div className=\"flex gap-2\">\n                <Input\n                  label=\"Description\"\n                  value={milestone.description}\n                  onChange={(e) => updateArrayItem('timeline.milestones', index, { ...milestone, description: e.target.value })}\n                  placeholder=\"Brief description\"\n                  className=\"flex-1\"\n                />\n                {overview.timeline.milestones.length > 1 && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => removeArrayItem('timeline.milestones', index)}\n                    iconName=\"X\"\n                    className=\"mt-6\"\n                  />\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Additional Sections */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n          <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n            <Icon name=\"Users\" className=\"h-5 w-5\" />\n            Key Stakeholders\n          </h3>\n\n          <div className=\"space-y-3\">\n            {overview.stakeholders.map((stakeholder, index) => (\n              <div key={index} className=\"flex gap-2\">\n                <Input\n                  value={stakeholder}\n                  onChange={(e) => updateArrayItem('stakeholders', index, e.target.value)}\n                  placeholder={`Stakeholder ${index + 1}`}\n                  className=\"flex-1\"\n                />\n                {overview.stakeholders.length > 1 && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => removeArrayItem('stakeholders', index)}\n                    iconName=\"X\"\n                  />\n                )}\n              </div>\n            ))}\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => addArrayItem('stakeholders')}\n              iconName=\"Plus\"\n              iconPosition=\"left\"\n              className=\"w-full\"\n            >\n              Add Stakeholder\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n          <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n            <Icon name=\"AlertTriangle\" className=\"h-5 w-5\" />\n            Risk Assessment\n          </h3>\n\n          <RichTextEditor\n            value={overview.riskAssessment}\n            onChange={(value) => updateOverview('riskAssessment', value)}\n            placeholder=\"Identify potential risks and mitigation strategies...\"\n            maxLength={2000}\n            showToolbar={false}\n            showWordCount\n            className=\"min-h-[120px]\"\n          />\n        </div>\n      </div>\n\n      {/* Success Criteria */}\n      <div className=\"bg-card rounded-lg p-6 border border-border space-y-4\">\n        <h3 className=\"text-lg font-semibold text-foreground flex items-center gap-2\">\n          <Icon name=\"CheckCircle\" className=\"h-5 w-5\" />\n          Success Criteria\n        </h3>\n\n        <RichTextEditor\n          value={overview.successCriteria}\n          onChange={(value) => updateOverview('successCriteria', value)}\n          placeholder=\"Define what success looks like for this project...\"\n          maxLength={2000}\n          showToolbar={false}\n          showWordCount\n        />\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex justify-between items-center pt-6\">\n        <Button\n          variant=\"outline\"\n          onClick={onBack}\n          iconName=\"ArrowLeft\"\n          iconPosition=\"left\"\n        >\n          Back to Configuration\n        </Button>\n\n        <div className=\"flex items-center gap-2\">\n          <div className=\"text-sm text-muted-foreground\">\n            Step 2 of 6\n          </div>\n          <Button\n            onClick={handleNext}\n            disabled={!isValid}\n            iconName=\"ArrowRight\"\n            iconPosition=\"right\"\n          >\n            Continue to Tech Stack\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProjectOverviewEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,EAAE,QAAQ,gBAAgB;AACnC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,MAAM;EACNC,MAAM;EACNC,WAAW,GAAG,CAAC,CAAC;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,CAAC,EAAE,CAAC;IAChBC,YAAY,EAAE,CAAC,EAAE,CAAC;IAClBC,IAAI,EAAE,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAG,CAAC,CAAC;IACjDC,QAAQ,EAAE;MACRC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,CAAC;QAAEN,IAAI,EAAE,EAAE;QAAEO,IAAI,EAAE,EAAE;QAAEX,WAAW,EAAE;MAAG,CAAC;IACtD,CAAC;IACDY,YAAY,EAAE,CAAC,EAAE,CAAC;IAClBC,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE,EAAE;IACnB,GAAGpB;EACL,CAAC,CAAC;EAEF,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACdqC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACtB,QAAQ,CAAC,CAAC;EAEd,MAAMsB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACvB,QAAQ,CAACE,KAAK,CAACsB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACrB,KAAK,GAAG,2BAA2B;IAC/C;IAEA,IAAI,CAACF,QAAQ,CAACG,WAAW,CAACqB,IAAI,CAAC,CAAC,EAAE;MAChCD,SAAS,CAACpB,WAAW,GAAG,iCAAiC;IAC3D;IAEA,IAAIH,QAAQ,CAACG,WAAW,CAACsB,MAAM,GAAG,KAAK,EAAE;MACvCF,SAAS,CAACpB,WAAW,GAAG,6CAA6C;IACvE;IAEA,IAAIH,QAAQ,CAACI,UAAU,CAACsB,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;MAC9DF,SAAS,CAACnB,UAAU,GAAG,oCAAoC;IAC7D;IAEA,IAAIJ,QAAQ,CAACK,YAAY,CAACqB,MAAM,CAACE,GAAG,IAAIA,GAAG,CAACJ,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;MAChEF,SAAS,CAAClB,YAAY,GAAG,sCAAsC;IACjE;IAEAc,SAAS,CAACI,SAAS,CAAC;IACpBF,UAAU,CAACQ,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC,CAAC;EACjD,CAAC;EAED,MAAMM,cAAc,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACtChC,WAAW,CAACiC,IAAI,IAAI;MAClB,MAAMC,WAAW,GAAG;QAAE,GAAGD;MAAK,CAAC;MAC/B,MAAMJ,IAAI,GAAGE,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC;MAC5B,IAAIC,OAAO,GAAGF,WAAW;MAEzB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACL,MAAM,GAAG,CAAC,EAAEa,CAAC,EAAE,EAAE;QACxCD,OAAO,GAAGA,OAAO,CAACP,IAAI,CAACQ,CAAC,CAAC,CAAC;MAC5B;MAEAD,OAAO,CAACP,IAAI,CAACA,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGQ,KAAK;MACtC,OAAOE,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,YAAY,GAAGA,CAACP,IAAI,EAAEQ,YAAY,GAAG,EAAE,KAAK;IAChD,MAAMH,OAAO,GAAGL,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC,CAACK,MAAM,CAAC,CAACd,GAAG,EAAEe,GAAG,KAAKf,GAAG,CAACe,GAAG,CAAC,EAAE1C,QAAQ,CAAC;IACxE,MAAM2C,QAAQ,GAAG,CAAC,GAAGN,OAAO,EAAEG,YAAY,CAAC;IAC3CT,cAAc,CAACC,IAAI,EAAEW,QAAQ,CAAC;EAChC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACZ,IAAI,EAAEa,KAAK,KAAK;IACvC,MAAMR,OAAO,GAAGL,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC,CAACK,MAAM,CAAC,CAACd,GAAG,EAAEe,GAAG,KAAKf,GAAG,CAACe,GAAG,CAAC,EAAE1C,QAAQ,CAAC;IACxE,MAAM2C,QAAQ,GAAGN,OAAO,CAACX,MAAM,CAAC,CAACoB,CAAC,EAAER,CAAC,KAAKA,CAAC,KAAKO,KAAK,CAAC;IACtDd,cAAc,CAACC,IAAI,EAAEW,QAAQ,CAAC;EAChC,CAAC;EAED,MAAMI,eAAe,GAAGA,CAACf,IAAI,EAAEa,KAAK,EAAEZ,KAAK,KAAK;IAC9C,MAAMI,OAAO,GAAGL,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC,CAACK,MAAM,CAAC,CAACd,GAAG,EAAEe,GAAG,KAAKf,GAAG,CAACe,GAAG,CAAC,EAAE1C,QAAQ,CAAC;IACxE,MAAM2C,QAAQ,GAAG,CAAC,GAAGN,OAAO,CAAC;IAC7BM,QAAQ,CAACE,KAAK,CAAC,GAAGZ,KAAK;IACvBF,cAAc,CAACC,IAAI,EAAEW,QAAQ,CAAC;EAChC,CAAC;EAED,MAAMK,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI5B,OAAO,EAAE;MACXzB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGK,QAAQ,CAAC;IACpB;EACF,CAAC;EAED,oBACEP,OAAA;IAAKK,SAAS,EAAEZ,EAAE,CAAC,iCAAiC,EAAEY,SAAS,CAAE;IAAAmD,QAAA,gBAE/DxD,OAAA;MAAKK,SAAS,EAAC,uBAAuB;MAAAmD,QAAA,gBACpCxD,OAAA;QAAIK,SAAS,EAAC,oCAAoC;QAAAmD,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxE5D,OAAA;QAAGK,SAAS,EAAC,uBAAuB;QAAAmD,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN5D,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAAmD,QAAA,gBACpExD,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAAmD,QAAA,gBAC3ExD,OAAA,CAACF,IAAI;UAACgB,IAAI,EAAC,UAAU;UAACT,SAAS,EAAC;QAAS;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAE9C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL5D,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAmD,QAAA,gBACxBxD,OAAA,CAACL,KAAK;UACJkE,KAAK,EAAC,eAAe;UACrBrB,KAAK,EAAEjC,QAAQ,CAACE,KAAM;UACtBqD,QAAQ,EAAGC,CAAC,IAAKzB,cAAc,CAAC,OAAO,EAAEyB,CAAC,CAAChD,MAAM,CAACyB,KAAK,CAAE;UACzDwB,WAAW,EAAC,qBAAqB;UACjCC,KAAK,EAAExC,MAAM,CAAChB,KAAM;UACpByD,QAAQ;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEF5D,OAAA,CAACJ,cAAc;UACbiE,KAAK,EAAC,qBAAqB;UAC3BrB,KAAK,EAAEjC,QAAQ,CAACG,WAAY;UAC5BoD,QAAQ,EAAGtB,KAAK,IAAKF,cAAc,CAAC,aAAa,EAAEE,KAAK,CAAE;UAC1DwB,WAAW,EAAC,wDAAwD;UACpEG,SAAS,EAAE,KAAM;UACjBF,KAAK,EAAExC,MAAM,CAACf,WAAY;UAC1BA,WAAW,EAAC,+EAA+E;UAC3F0D,aAAa;UACbC,UAAU;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAAmD,QAAA,gBACpExD,OAAA;QAAKK,SAAS,EAAC,mCAAmC;QAAAmD,QAAA,gBAChDxD,OAAA;UAAIK,SAAS,EAAC,+DAA+D;UAAAmD,QAAA,gBAC3ExD,OAAA,CAACF,IAAI;YAACgB,IAAI,EAAC,QAAQ;YAACT,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5D,OAAA,CAACN,MAAM;UACL4E,OAAO,EAAC,SAAS;UACjBC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAAC,YAAY,CAAE;UAC1C2B,QAAQ,EAAC,MAAM;UACfC,YAAY,EAAC,MAAM;UAAAlB,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5D,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAmD,QAAA,EACvBjD,QAAQ,CAACI,UAAU,CAACgE,GAAG,CAAC,CAACC,SAAS,EAAExB,KAAK,kBACxCpD,OAAA;UAAiBK,SAAS,EAAC,YAAY;UAAAmD,QAAA,gBACrCxD,OAAA,CAACL,KAAK;YACJ6C,KAAK,EAAEoC,SAAU;YACjBd,QAAQ,EAAGC,CAAC,IAAKT,eAAe,CAAC,YAAY,EAAEF,KAAK,EAAEW,CAAC,CAAChD,MAAM,CAACyB,KAAK,CAAE;YACtEwB,WAAW,EAAE,aAAaZ,KAAK,GAAG,CAAC,EAAG;YACtC/C,SAAS,EAAC;UAAQ;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACDrD,QAAQ,CAACI,UAAU,CAACqB,MAAM,GAAG,CAAC,iBAC7BhC,OAAA,CAACN,MAAM;YACL4E,OAAO,EAAC,OAAO;YACfC,IAAI,EAAC,IAAI;YACTC,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,YAAY,EAAEC,KAAK,CAAE;YACpDqB,QAAQ,EAAC;UAAG;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACF;QAAA,GAdOR,KAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELnC,MAAM,CAACd,UAAU,iBAAIX,OAAA;QAAGK,SAAS,EAAC,0BAA0B;QAAAmD,QAAA,EAAE/B,MAAM,CAACd;MAAU;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClF,CAAC,eAGN5D,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAAmD,QAAA,gBACpExD,OAAA;QAAKK,SAAS,EAAC,mCAAmC;QAAAmD,QAAA,gBAChDxD,OAAA;UAAIK,SAAS,EAAC,+DAA+D;UAAAmD,QAAA,gBAC3ExD,OAAA,CAACF,IAAI;YAACgB,IAAI,EAAC,SAAS;YAACT,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5D,OAAA,CAACN,MAAM;UACL4E,OAAO,EAAC,SAAS;UACjBC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAAC,cAAc,CAAE;UAC5C2B,QAAQ,EAAC,MAAM;UACfC,YAAY,EAAC,MAAM;UAAAlB,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5D,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAmD,QAAA,EACvBjD,QAAQ,CAACK,YAAY,CAAC+D,GAAG,CAAC,CAACE,WAAW,EAAEzB,KAAK,kBAC5CpD,OAAA;UAAiBK,SAAS,EAAC,YAAY;UAAAmD,QAAA,gBACrCxD,OAAA,CAACL,KAAK;YACJ6C,KAAK,EAAEqC,WAAY;YACnBf,QAAQ,EAAGC,CAAC,IAAKT,eAAe,CAAC,cAAc,EAAEF,KAAK,EAAEW,CAAC,CAAChD,MAAM,CAACyB,KAAK,CAAE;YACxEwB,WAAW,EAAE,eAAeZ,KAAK,GAAG,CAAC,EAAG;YACxC/C,SAAS,EAAC;UAAQ;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACDrD,QAAQ,CAACK,YAAY,CAACoB,MAAM,GAAG,CAAC,iBAC/BhC,OAAA,CAACN,MAAM;YACL4E,OAAO,EAAC,OAAO;YACfC,IAAI,EAAC,IAAI;YACTC,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,cAAc,EAAEC,KAAK,CAAE;YACtDqB,QAAQ,EAAC;UAAG;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACF;QAAA,GAdOR,KAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAELnC,MAAM,CAACb,YAAY,iBAAIZ,OAAA;QAAGK,SAAS,EAAC,0BAA0B;QAAAmD,QAAA,EAAE/B,MAAM,CAACb;MAAY;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC,eAGN5D,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAAmD,QAAA,gBACpExD,OAAA;QAAKK,SAAS,EAAC,mCAAmC;QAAAmD,QAAA,gBAChDxD,OAAA;UAAIK,SAAS,EAAC,+DAA+D;UAAAmD,QAAA,gBAC3ExD,OAAA,CAACF,IAAI;YAACgB,IAAI,EAAC,WAAW;YAACT,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,8BAE/C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL5D,OAAA,CAACN,MAAM;UACL4E,OAAO,EAAC,SAAS;UACjBC,IAAI,EAAC,IAAI;UACTC,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAAC,MAAM,EAAE;YAAEhC,IAAI,EAAE,EAAE;YAAEC,MAAM,EAAE,EAAE;YAAEC,WAAW,EAAE;UAAG,CAAC,CAAE;UAC/EyD,QAAQ,EAAC,MAAM;UACfC,YAAY,EAAC,MAAM;UAAAlB,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5D,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAmD,QAAA,EACvBjD,QAAQ,CAACM,IAAI,CAAC8D,GAAG,CAAC,CAACG,GAAG,EAAE1B,KAAK,kBAC5BpD,OAAA;UAAiBK,SAAS,EAAC,2EAA2E;UAAAmD,QAAA,gBACpGxD,OAAA,CAACL,KAAK;YACJkE,KAAK,EAAC,UAAU;YAChBrB,KAAK,EAAEsC,GAAG,CAAChE,IAAK;YAChBgD,QAAQ,EAAGC,CAAC,IAAKT,eAAe,CAAC,MAAM,EAAEF,KAAK,EAAE;cAAE,GAAG0B,GAAG;cAAEhE,IAAI,EAAEiD,CAAC,CAAChD,MAAM,CAACyB;YAAM,CAAC,CAAE;YAClFwB,WAAW,EAAC;UAAyB;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACF5D,OAAA,CAACL,KAAK;YACJkE,KAAK,EAAC,cAAc;YACpBrB,KAAK,EAAEsC,GAAG,CAAC/D,MAAO;YAClB+C,QAAQ,EAAGC,CAAC,IAAKT,eAAe,CAAC,MAAM,EAAEF,KAAK,EAAE;cAAE,GAAG0B,GAAG;cAAE/D,MAAM,EAAEgD,CAAC,CAAChD,MAAM,CAACyB;YAAM,CAAC,CAAE;YACpFwB,WAAW,EAAC;UAAW;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACF5D,OAAA;YAAKK,SAAS,EAAC,YAAY;YAAAmD,QAAA,gBACzBxD,OAAA,CAACL,KAAK;cACJkE,KAAK,EAAC,oBAAoB;cAC1BrB,KAAK,EAAEsC,GAAG,CAAC9D,WAAY;cACvB8C,QAAQ,EAAGC,CAAC,IAAKT,eAAe,CAAC,MAAM,EAAEF,KAAK,EAAE;gBAAE,GAAG0B,GAAG;gBAAE9D,WAAW,EAAE+C,CAAC,CAAChD,MAAM,CAACyB;cAAM,CAAC,CAAE;cACzFwB,WAAW,EAAC,qBAAqB;cACjC3D,SAAS,EAAC;YAAQ;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACDrD,QAAQ,CAACM,IAAI,CAACmB,MAAM,GAAG,CAAC,iBACvBhC,OAAA,CAACN,MAAM;cACL4E,OAAO,EAAC,OAAO;cACfC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,MAAM,EAAEC,KAAK,CAAE;cAC9CqB,QAAQ,EAAC,GAAG;cACZpE,SAAS,EAAC;YAAM;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GA9BER,KAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+BV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAAmD,QAAA,gBACpExD,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAAmD,QAAA,gBAC3ExD,OAAA,CAACF,IAAI;UAACgB,IAAI,EAAC,UAAU;UAACT,SAAS,EAAC;QAAS;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAE9C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL5D,OAAA;QAAKK,SAAS,EAAC,uCAAuC;QAAAmD,QAAA,gBACpDxD,OAAA,CAACH,UAAU;UACTgE,KAAK,EAAC,YAAY;UAClBrB,KAAK,EAAEjC,QAAQ,CAACU,QAAQ,CAACC,SAAU;UACnC4C,QAAQ,EAAGtB,KAAK,IAAKF,cAAc,CAAC,oBAAoB,EAAEE,KAAK,CAAE;UACjE9B,WAAW,EAAC;QAAsB;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACF5D,OAAA,CAACH,UAAU;UACTgE,KAAK,EAAC,UAAU;UAChBrB,KAAK,EAAEjC,QAAQ,CAACU,QAAQ,CAACE,OAAQ;UACjC2C,QAAQ,EAAGtB,KAAK,IAAKF,cAAc,CAAC,kBAAkB,EAAEE,KAAK,CAAE;UAC/D9B,WAAW,EAAC,0BAA0B;UACtCqE,OAAO,EAAExE,QAAQ,CAACU,QAAQ,CAACC;QAAU;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN5D,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAmD,QAAA,gBACxBxD,OAAA;UAAKK,SAAS,EAAC,mCAAmC;UAAAmD,QAAA,gBAChDxD,OAAA;YAAIK,SAAS,EAAC,qCAAqC;YAAAmD,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnE5D,OAAA,CAACN,MAAM;YACL4E,OAAO,EAAC,SAAS;YACjBC,IAAI,EAAC,IAAI;YACTC,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAAC,qBAAqB,EAAE;cAAEhC,IAAI,EAAE,EAAE;cAAEO,IAAI,EAAE,EAAE;cAAEX,WAAW,EAAE;YAAG,CAAC,CAAE;YAC5F+D,QAAQ,EAAC,MAAM;YACfC,YAAY,EAAC,MAAM;YAAAlB,QAAA,EACpB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELrD,QAAQ,CAACU,QAAQ,CAACG,UAAU,CAACuD,GAAG,CAAC,CAACK,SAAS,EAAE5B,KAAK,kBACjDpD,OAAA;UAAiBK,SAAS,EAAC,2EAA2E;UAAAmD,QAAA,gBACpGxD,OAAA,CAACL,KAAK;YACJkE,KAAK,EAAC,gBAAgB;YACtBrB,KAAK,EAAEwC,SAAS,CAAClE,IAAK;YACtBgD,QAAQ,EAAGC,CAAC,IAAKT,eAAe,CAAC,qBAAqB,EAAEF,KAAK,EAAE;cAAE,GAAG4B,SAAS;cAAElE,IAAI,EAAEiD,CAAC,CAAChD,MAAM,CAACyB;YAAM,CAAC,CAAE;YACvGwB,WAAW,EAAC;UAAmB;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACF5D,OAAA,CAACH,UAAU;YACTgE,KAAK,EAAC,aAAa;YACnBrB,KAAK,EAAEwC,SAAS,CAAC3D,IAAK;YACtByC,QAAQ,EAAGtB,KAAK,IAAKc,eAAe,CAAC,qBAAqB,EAAEF,KAAK,EAAE;cAAE,GAAG4B,SAAS;cAAE3D,IAAI,EAAEmB;YAAM,CAAC,CAAE;YAClGuC,OAAO,EAAExE,QAAQ,CAACU,QAAQ,CAACC,SAAU;YACrC+D,OAAO,EAAE1E,QAAQ,CAACU,QAAQ,CAACE;UAAQ;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACF5D,OAAA;YAAKK,SAAS,EAAC,YAAY;YAAAmD,QAAA,gBACzBxD,OAAA,CAACL,KAAK;cACJkE,KAAK,EAAC,aAAa;cACnBrB,KAAK,EAAEwC,SAAS,CAACtE,WAAY;cAC7BoD,QAAQ,EAAGC,CAAC,IAAKT,eAAe,CAAC,qBAAqB,EAAEF,KAAK,EAAE;gBAAE,GAAG4B,SAAS;gBAAEtE,WAAW,EAAEqD,CAAC,CAAChD,MAAM,CAACyB;cAAM,CAAC,CAAE;cAC9GwB,WAAW,EAAC,mBAAmB;cAC/B3D,SAAS,EAAC;YAAQ;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACDrD,QAAQ,CAACU,QAAQ,CAACG,UAAU,CAACY,MAAM,GAAG,CAAC,iBACtChC,OAAA,CAACN,MAAM;cACL4E,OAAO,EAAC,OAAO;cACfC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,qBAAqB,EAAEC,KAAK,CAAE;cAC7DqB,QAAQ,EAAC,GAAG;cACZpE,SAAS,EAAC;YAAM;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GA/BER,KAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCV,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKK,SAAS,EAAC,uCAAuC;MAAAmD,QAAA,gBACpDxD,OAAA;QAAKK,SAAS,EAAC,uDAAuD;QAAAmD,QAAA,gBACpExD,OAAA;UAAIK,SAAS,EAAC,+DAA+D;UAAAmD,QAAA,gBAC3ExD,OAAA,CAACF,IAAI;YAACgB,IAAI,EAAC,OAAO;YAACT,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAE3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL5D,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAAmD,QAAA,GACvBjD,QAAQ,CAACe,YAAY,CAACqD,GAAG,CAAC,CAACO,WAAW,EAAE9B,KAAK,kBAC5CpD,OAAA;YAAiBK,SAAS,EAAC,YAAY;YAAAmD,QAAA,gBACrCxD,OAAA,CAACL,KAAK;cACJ6C,KAAK,EAAE0C,WAAY;cACnBpB,QAAQ,EAAGC,CAAC,IAAKT,eAAe,CAAC,cAAc,EAAEF,KAAK,EAAEW,CAAC,CAAChD,MAAM,CAACyB,KAAK,CAAE;cACxEwB,WAAW,EAAE,eAAeZ,KAAK,GAAG,CAAC,EAAG;cACxC/C,SAAS,EAAC;YAAQ;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACDrD,QAAQ,CAACe,YAAY,CAACU,MAAM,GAAG,CAAC,iBAC/BhC,OAAA,CAACN,MAAM;cACL4E,OAAO,EAAC,OAAO;cACfC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,cAAc,EAAEC,KAAK,CAAE;cACtDqB,QAAQ,EAAC;YAAG;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACF;UAAA,GAdOR,KAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeV,CACN,CAAC,eACF5D,OAAA,CAACN,MAAM;YACL4E,OAAO,EAAC,SAAS;YACjBC,IAAI,EAAC,IAAI;YACTC,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAAC,cAAc,CAAE;YAC5C2B,QAAQ,EAAC,MAAM;YACfC,YAAY,EAAC,MAAM;YACnBrE,SAAS,EAAC,QAAQ;YAAAmD,QAAA,EACnB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5D,OAAA;QAAKK,SAAS,EAAC,uDAAuD;QAAAmD,QAAA,gBACpExD,OAAA;UAAIK,SAAS,EAAC,+DAA+D;UAAAmD,QAAA,gBAC3ExD,OAAA,CAACF,IAAI;YAACgB,IAAI,EAAC,eAAe;YAACT,SAAS,EAAC;UAAS;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL5D,OAAA,CAACJ,cAAc;UACb4C,KAAK,EAAEjC,QAAQ,CAACgB,cAAe;UAC/BuC,QAAQ,EAAGtB,KAAK,IAAKF,cAAc,CAAC,gBAAgB,EAAEE,KAAK,CAAE;UAC7DwB,WAAW,EAAC,uDAAuD;UACnEG,SAAS,EAAE,IAAK;UAChBgB,WAAW,EAAE,KAAM;UACnBf,aAAa;UACb/D,SAAS,EAAC;QAAe;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKK,SAAS,EAAC,uDAAuD;MAAAmD,QAAA,gBACpExD,OAAA;QAAIK,SAAS,EAAC,+DAA+D;QAAAmD,QAAA,gBAC3ExD,OAAA,CAACF,IAAI;UAACgB,IAAI,EAAC,aAAa;UAACT,SAAS,EAAC;QAAS;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEjD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL5D,OAAA,CAACJ,cAAc;QACb4C,KAAK,EAAEjC,QAAQ,CAACiB,eAAgB;QAChCsC,QAAQ,EAAGtB,KAAK,IAAKF,cAAc,CAAC,iBAAiB,EAAEE,KAAK,CAAE;QAC9DwB,WAAW,EAAC,oDAAoD;QAChEG,SAAS,EAAE,IAAK;QAChBgB,WAAW,EAAE,KAAM;QACnBf,aAAa;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN5D,OAAA;MAAKK,SAAS,EAAC,wCAAwC;MAAAmD,QAAA,gBACrDxD,OAAA,CAACN,MAAM;QACL4E,OAAO,EAAC,SAAS;QACjBE,OAAO,EAAErE,MAAO;QAChBsE,QAAQ,EAAC,WAAW;QACpBC,YAAY,EAAC,MAAM;QAAAlB,QAAA,EACpB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET5D,OAAA;QAAKK,SAAS,EAAC,yBAAyB;QAAAmD,QAAA,gBACtCxD,OAAA;UAAKK,SAAS,EAAC,+BAA+B;UAAAmD,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN5D,OAAA,CAACN,MAAM;UACL8E,OAAO,EAAEjB,UAAW;UACpB6B,QAAQ,EAAE,CAACzD,OAAQ;UACnB8C,QAAQ,EAAC,YAAY;UACrBC,YAAY,EAAC,OAAO;UAAAlB,QAAA,EACrB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CAvcIL,qBAAqB;AAAAoF,EAAA,GAArBpF,qBAAqB;AAyc3B,eAAeA,qBAAqB;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}