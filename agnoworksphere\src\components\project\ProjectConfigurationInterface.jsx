import React, { useState, useEffect } from 'react';
import { cn } from '../../utils/cn';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Select from '../ui/Select';
import Slider from '../ui/Slider';
import Toggle from '../ui/Toggle';
import DatePicker from '../ui/DatePicker';
import { Textarea } from '../ui/Textarea';
import Icon from '../AppIcon';

const ProjectConfigurationInterface = ({ 
  onNext, 
  onBack, 
  initialData = {}, 
  className 
}) => {
  const [config, setConfig] = useState({
    budget: 50000,
    duration: 12,
    teamSize: 5,
    priority: 'medium',
    methodology: 'agile',
    tools: {
      projectManagement: true,
      versionControl: true,
      cicd: false,
      monitoring: false,
      testing: true,
      documentation: true
    },
    resources: {
      frontend: 2,
      backend: 2,
      design: 1,
      qa: 1,
      devops: 0
    },
    features: {
      realTimeCollaboration: false,
      aiAssistance: false,
      advancedAnalytics: false,
      mobileApp: false,
      apiIntegration: true
    },
    ...initialData
  });

  const [errors, setErrors] = useState({});
  const [isValid, setIsValid] = useState(false);

  const methodologyOptions = [
    { value: 'agile', label: 'Agile/Scrum' },
    { value: 'waterfall', label: 'Waterfall' },
    { value: 'kanban', label: 'Kanban' },
    { value: 'hybrid', label: 'Hybrid' }
  ];

  const priorityOptions = [
    { value: 'low', label: 'Low Priority' },
    { value: 'medium', label: 'Medium Priority' },
    { value: 'high', label: 'High Priority' },
    { value: 'urgent', label: 'Urgent' }
  ];

  const toolCategories = [
    { key: 'projectManagement', label: 'Project Management', description: 'Task tracking and team coordination' },
    { key: 'versionControl', label: 'Version Control', description: 'Git-based source code management' },
    { key: 'cicd', label: 'CI/CD Pipeline', description: 'Automated testing and deployment' },
    { key: 'monitoring', label: 'Monitoring & Logging', description: 'Application performance monitoring' },
    { key: 'testing', label: 'Testing Framework', description: 'Automated testing tools' },
    { key: 'documentation', label: 'Documentation', description: 'Technical documentation platform' }
  ];

  const resourceTypes = [
    { key: 'frontend', label: 'Frontend Developers', max: 10 },
    { key: 'backend', label: 'Backend Developers', max: 10 },
    { key: 'design', label: 'UI/UX Designers', max: 5 },
    { key: 'qa', label: 'QA Engineers', max: 5 },
    { key: 'devops', label: 'DevOps Engineers', max: 3 }
  ];

  const featureOptions = [
    { key: 'realTimeCollaboration', label: 'Real-time Collaboration', description: 'Live editing and updates' },
    { key: 'aiAssistance', label: 'AI Assistance', description: 'AI-powered project insights' },
    { key: 'advancedAnalytics', label: 'Advanced Analytics', description: 'Detailed project metrics' },
    { key: 'mobileApp', label: 'Mobile Application', description: 'Native mobile app development' },
    { key: 'apiIntegration', label: 'API Integration', description: 'Third-party service integration' }
  ];

  useEffect(() => {
    validateConfiguration();
  }, [config]);

  const validateConfiguration = () => {
    const newErrors = {};
    
    if (config.budget < 1000) {
      newErrors.budget = 'Budget must be at least $1,000';
    }
    
    if (config.duration < 1) {
      newErrors.duration = 'Duration must be at least 1 week';
    }
    
    const totalResources = Object.values(config.resources).reduce((sum, count) => sum + count, 0);
    if (totalResources === 0) {
      newErrors.resources = 'At least one team member is required';
    }

    setErrors(newErrors);
    setIsValid(Object.keys(newErrors).length === 0);
  };

  const updateConfig = (path, value) => {
    setConfig(prev => {
      const newConfig = { ...prev };
      const keys = path.split('.');
      let current = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatDuration = (weeks) => {
    if (weeks < 4) return `${weeks} week${weeks !== 1 ? 's' : ''}`;
    const months = Math.round(weeks / 4.33);
    return `${months} month${months !== 1 ? 's' : ''}`;
  };

  const handleNext = () => {
    if (isValid) {
      onNext?.(config);
    }
  };

  return (
    <div className={cn("max-w-4xl mx-auto p-6 space-y-8", className)}>
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold text-foreground">Project Configuration</h2>
        <p className="text-muted-foreground">
          Customize your project settings and resource allocation
        </p>
      </div>

      {/* Budget & Timeline Section */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-6">
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Icon name="DollarSign" className="h-5 w-5" />
          Budget & Timeline
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Slider
            label="Project Budget"
            min={1000}
            max={500000}
            step={1000}
            value={config.budget}
            onChange={(value) => updateConfig('budget', value)}
            formatValue={formatCurrency}
            description="Total project budget allocation"
          />
          
          <Slider
            label="Project Duration"
            min={1}
            max={52}
            step={1}
            value={config.duration}
            onChange={(value) => updateConfig('duration', value)}
            formatValue={formatDuration}
            description="Estimated project timeline"
          />
        </div>
        
        {errors.budget && <p className="text-destructive text-sm">{errors.budget}</p>}
        {errors.duration && <p className="text-destructive text-sm">{errors.duration}</p>}
      </div>

      {/* Methodology & Priority Section */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-6">
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Icon name="Settings" className="h-5 w-5" />
          Project Approach
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Select
            label="Development Methodology"
            value={config.methodology}
            onValueChange={(value) => updateConfig('methodology', value)}
            options={methodologyOptions}
            description="Choose your preferred development approach"
          />
          
          <Select
            label="Project Priority"
            value={config.priority}
            onValueChange={(value) => updateConfig('priority', value)}
            options={priorityOptions}
            description="Set the project priority level"
          />
        </div>
      </div>

      {/* Team Resources Section */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-6">
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Icon name="Users" className="h-5 w-5" />
          Team Resources
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {resourceTypes.map(({ key, label, max }) => (
            <div key={key} className="space-y-2">
              <Slider
                label={label}
                min={0}
                max={max}
                step={1}
                value={config.resources[key]}
                onChange={(value) => updateConfig(`resources.${key}`, value)}
                description={`0-${max} team members`}
              />
            </div>
          ))}
        </div>

        {errors.resources && <p className="text-destructive text-sm">{errors.resources}</p>}

        <div className="bg-secondary/50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Total Team Size:</span>
            <span className="text-lg font-bold text-primary">
              {Object.values(config.resources).reduce((sum, count) => sum + count, 0)} members
            </span>
          </div>
        </div>
      </div>

      {/* Tools & Technologies Section */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-6">
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Icon name="Wrench" className="h-5 w-5" />
          Development Tools
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {toolCategories.map(({ key, label, description }) => (
            <Toggle
              key={key}
              label={label}
              description={description}
              checked={config.tools[key]}
              onChange={(checked) => updateConfig(`tools.${key}`, checked)}
            />
          ))}
        </div>
      </div>

      {/* Advanced Features Section */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-6">
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Icon name="Zap" className="h-5 w-5" />
          Advanced Features
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {featureOptions.map(({ key, label, description }) => (
            <Toggle
              key={key}
              label={label}
              description={description}
              checked={config.features[key]}
              onChange={(checked) => updateConfig(`features.${key}`, checked)}
            />
          ))}
        </div>
      </div>

      {/* Configuration Preview */}
      <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg p-6 border border-border">
        <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
          <Icon name="Eye" className="h-5 w-5" />
          Configuration Preview
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="space-y-2">
            <div className="font-medium text-foreground">Budget & Timeline</div>
            <div className="text-muted-foreground">
              Budget: {formatCurrency(config.budget)}<br/>
              Duration: {formatDuration(config.duration)}<br/>
              Priority: {priorityOptions.find(p => p.value === config.priority)?.label}
            </div>
          </div>

          <div className="space-y-2">
            <div className="font-medium text-foreground">Team Composition</div>
            <div className="text-muted-foreground">
              {resourceTypes.map(({ key, label }) =>
                config.resources[key] > 0 && (
                  <div key={key}>{label}: {config.resources[key]}</div>
                )
              )}
            </div>
          </div>

          <div className="space-y-2">
            <div className="font-medium text-foreground">Selected Tools</div>
            <div className="text-muted-foreground">
              {toolCategories.filter(({ key }) => config.tools[key]).map(({ label }) => (
                <div key={label}>• {label}</div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6">
        <Button
          variant="outline"
          onClick={onBack}
          iconName="ArrowLeft"
          iconPosition="left"
        >
          Back
        </Button>

        <div className="flex items-center gap-2">
          <div className="text-sm text-muted-foreground">
            Step 1 of 6
          </div>
          <Button
            onClick={handleNext}
            disabled={!isValid}
            iconName="ArrowRight"
            iconPosition="right"
          >
            Continue to Overview
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProjectConfigurationInterface;
