{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\role-based-dashboard\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport authService from '../../utils/realAuthService';\nimport apiService from '../../utils/realApiService';\nimport teamService from '../../utils/teamService';\nimport notificationService from '../../utils/notificationService';\nimport CreateProjectModal from '../../components/modals/CreateProjectModal';\nimport CreateOrganizationModal from '../../components/modals/CreateOrganizationModal';\nimport OwnerInviteMemberModal from '../../components/modals/InviteMemberModal';\nimport CreateAIProjectModal from '../../components/modals/CreateAIProjectModal';\nimport AdminInviteMemberModal from '../team-members/components/InviteMemberModal';\nimport { listenForProjectUpdates } from '../../utils/projectEventService';\nimport { useUserProfile } from '../../hooks/useUserProfile';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoleBasedDashboard = () => {\n  _s();\n  var _organizations$0$orga, _organizations$0$orga2, _currentUser$email, _organizations$0$orga3, _currentUser$email2;\n  const location = useLocation();\n\n  // Use centralized user profile hook\n  const {\n    userProfile,\n    currentOrganization: hookCurrentOrganization,\n    availableOrganizations,\n    loading: profileLoading\n  } = useUserProfile();\n  const [userRole, setUserRole] = useState('member'); // Default role\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [projects, setProjects] = useState([]);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [organizations, setOrganizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showWelcome, setShowWelcome] = useState(false);\n  const [welcomeMessage, setWelcomeMessage] = useState('');\n  const [showCreateProject, setShowCreateProject] = useState(false);\n  const [showCreateOrganization, setShowCreateOrganization] = useState(false);\n  const [showInviteMember, setShowInviteMember] = useState(false);\n  const [showCreateAIProject, setShowCreateAIProject] = useState(false);\n\n  // Real team members data\n  const [teamMembers, setTeamMembers] = useState([]);\n\n  // Real notifications data\n  const [notifications, setNotifications] = useState([]);\n  const [notificationsLoading, setNotificationsLoading] = useState(true);\n\n  // Load dashboard data from backend\n  useEffect(() => {\n    const loadDashboardData = async () => {\n      try {\n        var _location$state, _location$state2;\n        setLoading(true);\n\n        // Check for welcome message from navigation state\n        if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message && ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.type) === 'success') {\n          setWelcomeMessage(location.state.message);\n          setShowWelcome(true);\n          // Clear the state to prevent showing on refresh\n          window.history.replaceState({}, document.title);\n        }\n\n        // Get current user and role\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || authService.getUserRole());\n          setOrganizations(userResult.data.organizations || []);\n        } else {\n          // If no user, set fallback data instead of redirecting\n          console.warn('No user data available, using fallback');\n          setCurrentUser({\n            id: 'fallback-user',\n            firstName: 'User',\n            lastName: '',\n            email: '<EMAIL>',\n            role: 'member'\n          });\n          setUserRole('member');\n          setOrganizations([{\n            id: 'fallback-org',\n            name: 'Default Organization',\n            domain: 'example.com'\n          }]);\n        }\n\n        // Get dashboard stats\n        const statsResult = await authService.getDashboardStats();\n        if (statsResult.data) {\n          setDashboardData(statsResult.data);\n        }\n\n        // Get projects\n        const organizationId = authService.getOrganizationId();\n        if (organizationId) {\n          const projectsResult = await apiService.projects.getAll(organizationId);\n          setProjects(projectsResult || []);\n\n          // Get team members\n          try {\n            const teamMembersResult = await teamService.getTeamMembers(organizationId);\n            setTeamMembers(teamMembersResult || []);\n          } catch (teamError) {\n            console.error('Failed to load team members:', teamError);\n            setTeamMembers([]); // Clear team members on error\n          }\n\n          // Get notifications and check for first-time user\n          try {\n            var _userResult$data$orga, _userResult$data$orga2;\n            setNotificationsLoading(true);\n            const notificationsResult = await notificationService.getNotifications({\n              limit: 10\n            });\n\n            // Handle the new response structure\n            if (notificationsResult && notificationsResult.data && Array.isArray(notificationsResult.data)) {\n              setNotifications(notificationsResult.data);\n            } else if (Array.isArray(notificationsResult)) {\n              setNotifications(notificationsResult);\n            } else {\n              setNotifications([]);\n            }\n\n            // Check if this is a first-time user and create welcome notification\n            const isFirstTime = await notificationService.checkFirstTimeUser();\n            if (isFirstTime && (_userResult$data$orga = userResult.data.organizations) !== null && _userResult$data$orga !== void 0 && (_userResult$data$orga2 = _userResult$data$orga[0]) !== null && _userResult$data$orga2 !== void 0 && _userResult$data$orga2.name) {\n              await notificationService.createWelcomeNotification(userResult.data.user.id, userResult.data.organizations[0].name);\n\n              // Reload notifications to include the welcome notification\n              const updatedNotifications = await notificationService.getNotifications({\n                limit: 10\n              });\n              if (updatedNotifications && updatedNotifications.data && Array.isArray(updatedNotifications.data)) {\n                setNotifications(updatedNotifications.data);\n              } else if (Array.isArray(updatedNotifications)) {\n                setNotifications(updatedNotifications);\n              } else {\n                setNotifications([]);\n              }\n            }\n          } catch (notificationError) {\n            console.error('Failed to load notifications:', notificationError);\n            setNotifications([]); // Clear notifications on error\n          } finally {\n            setNotificationsLoading(false);\n          }\n        }\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load dashboard data:', err);\n\n        // Don't set error state that would prevent the page from working\n        // Instead, set fallback data and let the page render\n        if (!currentUser) {\n          setCurrentUser({\n            id: 'fallback-user',\n            firstName: 'User',\n            lastName: '',\n            email: '<EMAIL>',\n            role: 'member'\n          });\n          setUserRole('member');\n        }\n        if (organizations.length === 0) {\n          setOrganizations([{\n            id: 'fallback-org',\n            name: 'Default Organization',\n            domain: 'example.com'\n          }]);\n        }\n\n        // Set empty arrays for other data\n        setProjects([]);\n        setTeamMembers([]);\n        setNotifications([]);\n        setNotificationsLoading(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadDashboardData();\n  }, [location.state]);\n\n  // Listen for global project updates\n  useEffect(() => {\n    const cleanup = listenForProjectUpdates(async updateData => {\n      const {\n        action,\n        project,\n        organizationId\n      } = updateData;\n\n      // Refresh projects list when projects are created, updated, or deleted\n      if (action === 'created' || action === 'updated' || action === 'deleted' || action === 'refresh') {\n        try {\n          const currentOrgId = authService.getOrganizationId();\n          if (currentOrgId && (organizationId === currentOrgId || !organizationId)) {\n            console.log('Refreshing projects due to update:', action, project);\n            const projectsResult = await apiService.projects.getAll(currentOrgId);\n            setProjects(projectsResult || []);\n          }\n        } catch (error) {\n          console.error('Failed to refresh projects:', error);\n        }\n      }\n    });\n    return cleanup;\n  }, []);\n\n  // Project creation handler\n  const handleCreateProject = async projectData => {\n    try {\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n      console.log('Creating project with data:', projectData);\n      console.log('Organization ID:', organizationId);\n\n      // Create via API\n      const newProject = await apiService.projects.create(organizationId, projectData);\n      console.log('Project creation response:', newProject);\n\n      // Refresh projects list\n      console.log('Refreshing projects list...');\n      const projectsResult = await apiService.projects.getAll(organizationId);\n      console.log('Updated projects list:', projectsResult);\n      setProjects(projectsResult || []);\n\n      // Notify header to refresh projects\n      window.dispatchEvent(new CustomEvent('projectsUpdated', {\n        detail: {\n          organizationId,\n          projects: projectsResult\n        }\n      }));\n\n      // Send notification for project creation\n      try {\n        var _currentUser$data;\n        const notificationService = (await import('../../utils/notificationService')).default;\n        const currentUser = await authService.getCurrentUser();\n        if ((_currentUser$data = currentUser.data) !== null && _currentUser$data !== void 0 && _currentUser$data.user) {\n          await notificationService.notifyProjectCreated(newProject.data || newProject, currentUser.data.user.id);\n        }\n      } catch (notificationError) {\n        console.error('Failed to send project creation notification:', notificationError);\n      }\n\n      // Refresh dashboard stats\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n      console.log('Project created successfully:', newProject);\n    } catch (error) {\n      console.error('Failed to create project:', error);\n      throw error;\n    }\n  };\n\n  // Quick action handlers\n  const handleOpenCreateProject = () => {\n    setShowCreateProject(true);\n  };\n  const handleCloseCreateProject = () => {\n    setShowCreateProject(false);\n  };\n\n  // AI Project creation handlers\n  const handleOpenCreateAIProject = () => {\n    setShowCreateAIProject(true);\n  };\n  const handleCloseCreateAIProject = () => {\n    setShowCreateAIProject(false);\n  };\n  const handleCreateAIProject = async projectData => {\n    try {\n      console.log('Creating AI project with data:', projectData);\n\n      // Create via AI API endpoint\n      const response = await fetch('http://localhost:3001/api/v1/projects/ai-generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authService.getAccessToken()}`\n        },\n        body: JSON.stringify(projectData)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || 'Failed to create AI project');\n      }\n      const result = await response.json();\n      console.log('AI Project creation response:', result);\n      if (!result.success) {\n        throw new Error(result.message || 'Failed to create AI project');\n      }\n\n      // Refresh projects list\n      const organizationId = authService.getOrganizationId();\n      if (organizationId) {\n        const projectsResult = await apiService.projects.getAll(organizationId);\n        setProjects(projectsResult || []);\n\n        // Notify header to refresh projects\n        window.dispatchEvent(new CustomEvent('projectsUpdated', {\n          detail: {\n            organizationId,\n            projects: projectsResult\n          }\n        }));\n      }\n\n      // Refresh dashboard stats\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n      console.log('AI Project created successfully:', result.data);\n\n      // Show success message\n      setWelcomeMessage(`🎉 AI Project \"${projectData.name}\" created successfully with ${result.data.tasks_created} tasks!`);\n      setShowWelcome(true);\n    } catch (error) {\n      console.error('Failed to create AI project:', error);\n      throw error;\n    }\n  };\n\n  // Organization creation handlers\n  const handleCreateOrganization = async (organizationData, logoFile) => {\n    try {\n      const result = await apiService.organizations.create(organizationData, logoFile);\n      if (result.error) {\n        throw new Error(result.error);\n      }\n\n      // Show success message\n      setWelcomeMessage(result.message || `Organization \"${organizationData.name}\" created successfully!`);\n      setShowWelcome(true);\n\n      // Refresh organizations list if needed\n      // You might want to update the current organization context here\n      console.log('Organization created successfully:', result.data);\n      return result.data;\n    } catch (error) {\n      console.error('Failed to create organization:', error);\n      throw error;\n    }\n  };\n  const handleOpenCreateOrganization = () => {\n    setShowCreateOrganization(true);\n  };\n  const handleCloseCreateOrganization = () => {\n    setShowCreateOrganization(false);\n  };\n  const handleManageUsers = () => {\n    // Navigate to team members page\n    window.location.href = '/team-members';\n  };\n  const handleInviteMembers = () => {\n    setShowInviteMember(true);\n  };\n  const handleCloseInviteMember = () => {\n    setShowInviteMember(false);\n  };\n  const handleSendInvitation = async inviteData => {\n    try {\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n\n      // Call the backend API to send invitation\n      const response = await fetch(`http://localhost:3001/api/v1/organizations/${organizationId}/invite`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authService.getAccessToken()}`\n        },\n        body: JSON.stringify({\n          email: inviteData.email,\n          role: inviteData.role\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to send invitation');\n      }\n      const result = await response.json();\n      console.log('Invitation sent successfully:', result);\n\n      // Refresh dashboard stats to show updated member count\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n    } catch (error) {\n      console.error('Failed to send invitation:', error);\n      throw error;\n    }\n  };\n\n  // Handler for Admin (bulk invite)\n  const handleAdminInviteSubmit = async inviteData => {\n    const organizationId = authService.getOrganizationId();\n    if (!organizationId) {\n      console.error('No organization selected');\n      return;\n    }\n    try {\n      console.log('Admin inviting members:', inviteData);\n\n      // Send invitations through API\n      for (const email of inviteData.emails) {\n        await teamService.inviteTeamMember(organizationId, {\n          email,\n          role: inviteData.role,\n          message: inviteData.welcomeMessage\n        });\n      }\n      console.log('Invitations sent successfully');\n\n      // Refresh dashboard stats to show updated member count\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n    } catch (error) {\n      console.error('Failed to invite members:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  // Show loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600\",\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-red-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-lg mb-2\",\n          children: \"Failed to load dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600 text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Real projects data is loaded from API in useEffect\n\n  // Real activities and tasks data will be loaded from API when available\n\n  // Role-based KPI data with real data integration\n  const getKPIData = () => {\n    var _realData$total_organ, _realData$total_proje, _realData$total_membe, _realData$recent_acti, _realData$recent_acti2, _realData$total_proje2, _realData$total_membe2, _realData$total_organ2, _realData$recent_acti3, _realData$recent_acti4, _realData$total_proje3, _realData$total_organ3, _realData$total_membe3, _realData$recent_acti5, _realData$recent_acti6, _realData$total_proje4, _realData$total_organ4, _realData$total_membe4, _realData$recent_acti7, _realData$recent_acti8;\n    const realData = dashboardData || {};\n    switch (userRole) {\n      case 'owner':\n        return [{\n          title: \"Organizations\",\n          value: ((_realData$total_organ = realData.total_organizations) === null || _realData$total_organ === void 0 ? void 0 : _realData$total_organ.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Active Projects\",\n          value: ((_realData$total_proje = realData.total_projects) === null || _realData$total_proje === void 0 ? void 0 : _realData$total_proje.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe = realData.total_members) === null || _realData$total_membe === void 0 ? void 0 : _realData$total_membe.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti = realData.recent_activity) === null || _realData$recent_acti === void 0 ? void 0 : (_realData$recent_acti2 = _realData$recent_acti.length) === null || _realData$recent_acti2 === void 0 ? void 0 : _realData$recent_acti2.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'admin':\n        return [{\n          title: \"Active Projects\",\n          value: ((_realData$total_proje2 = realData.total_projects) === null || _realData$total_proje2 === void 0 ? void 0 : _realData$total_proje2.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe2 = realData.total_members) === null || _realData$total_membe2 === void 0 ? void 0 : _realData$total_membe2.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"success\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ2 = realData.total_organizations) === null || _realData$total_organ2 === void 0 ? void 0 : _realData$total_organ2.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti3 = realData.recent_activity) === null || _realData$recent_acti3 === void 0 ? void 0 : (_realData$recent_acti4 = _realData$recent_acti3.length) === null || _realData$recent_acti4 === void 0 ? void 0 : _realData$recent_acti4.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'member':\n        return [{\n          title: \"My Projects\",\n          value: ((_realData$total_proje3 = realData.total_projects) === null || _realData$total_proje3 === void 0 ? void 0 : _realData$total_proje3.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ3 = realData.total_organizations) === null || _realData$total_organ3 === void 0 ? void 0 : _realData$total_organ3.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe3 = realData.total_members) === null || _realData$total_membe3 === void 0 ? void 0 : _realData$total_membe3.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti5 = realData.recent_activity) === null || _realData$recent_acti5 === void 0 ? void 0 : (_realData$recent_acti6 = _realData$recent_acti5.length) === null || _realData$recent_acti6 === void 0 ? void 0 : _realData$recent_acti6.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'viewer':\n        return [{\n          title: \"Projects Viewed\",\n          value: ((_realData$total_proje4 = realData.total_projects) === null || _realData$total_proje4 === void 0 ? void 0 : _realData$total_proje4.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Eye\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ4 = realData.total_organizations) === null || _realData$total_organ4 === void 0 ? void 0 : _realData$total_organ4.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"accent\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe4 = realData.total_members) === null || _realData$total_membe4 === void 0 ? void 0 : _realData$total_membe4.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"success\"\n        }, {\n          title: \"Activity Items\",\n          value: ((_realData$recent_acti7 = realData.recent_activity) === null || _realData$recent_acti7 === void 0 ? void 0 : (_realData$recent_acti8 = _realData$recent_acti7.length) === null || _realData$recent_acti8 === void 0 ? void 0 : _realData$recent_acti8.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"BarChart3\",\n          color: \"warning\"\n        }];\n      default:\n        return [{\n          title: \"Projects\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Members\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Activity\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n    }\n  };\n\n  // Filter projects based on search, filter values, and role-based access\n  const filteredProjects = projects.filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) || (project.description || '').toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' || (project.status || 'active').toLowerCase() === filterValue.toLowerCase();\n\n    // Role-based project access control\n    const hasProjectAccess = () => {\n      if (!(currentUser !== null && currentUser !== void 0 && currentUser.role)) return true; // Default access if role not set\n\n      const userRole = currentUser.role.toLowerCase();\n\n      // Viewers should only see projects they are specifically invited to\n      if (userRole === 'viewer') {\n        // For now, viewers can see projects from their organization\n        // In a real implementation, this would check project-specific invitations\n        return project.organization_id === currentUser.organization_id;\n      }\n\n      // Members, admins, and owners can see all projects in their organization\n      return true;\n    };\n    return matchesSearch && matchesFilter && hasProjectAccess();\n  });\n\n  // Get current organization data - use hook data if available, otherwise fallback to local state\n  const currentOrganization = hookCurrentOrganization || (organizations.length > 0 ? {\n    name: ((_organizations$0$orga = organizations[0].organization) === null || _organizations$0$orga === void 0 ? void 0 : _organizations$0$orga.name) || organizations[0].name || 'Loading...',\n    domain: ((_organizations$0$orga2 = organizations[0].organization) === null || _organizations$0$orga2 === void 0 ? void 0 : _organizations$0$orga2.domain) || organizations[0].domain || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$email = currentUser.email) === null || _currentUser$email === void 0 ? void 0 : _currentUser$email.split('@')[1]) || 'company.com',\n    logo: ((_organizations$0$orga3 = organizations[0].organization) === null || _organizations$0$orga3 === void 0 ? void 0 : _organizations$0$orga3.logo_url) || organizations[0].logo || '/assets/images/org-logo.png'\n  } : {\n    name: 'Loading...',\n    domain: 'company.com',\n    logo: '/assets/images/org-logo.png'\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser ? {\n        name: `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() || ((_currentUser$email2 = currentUser.email) === null || _currentUser$email2 === void 0 ? void 0 : _currentUser$email2.split('@')[0]) || 'User',\n        email: currentUser.email,\n        avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n        role: userRole\n      } : {\n        name: 'Loading...',\n        email: '',\n        avatar: '/assets/images/avatar.jpg',\n        role: userRole\n      },\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 587,\n      columnNumber: 7\n    }, this), showWelcome && welcomeMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-green-50 to-blue-50 border-l-4 border-green-400 p-6 mt-16 mx-4 rounded-lg shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6 text-green-400\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-3 flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-green-800\",\n            children: \"Welcome to Agno WorkSphere!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 text-sm text-green-700\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: welcomeMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowWelcome(false),\n              className: \"bg-green-100 hover:bg-green-200 text-green-800 px-4 py-2 rounded-md text-sm font-medium transition-colors\",\n              children: \"Got it, thanks!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 605,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `glass-effect border-b border-white/20 p-4 ${showWelcome ? 'mt-4' : 'mt-16'}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-slate-700\",\n              children: currentUser ? `Welcome, ${currentUser.firstName}!` : 'Loading...'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 text-sm text-slate-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-3 py-1 bg-blue-100 text-blue-800 rounded-full font-medium capitalize\",\n              children: userRole\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this), currentOrganization && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-slate-500\",\n              children: [\"\\u2022 \", currentOrganization.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 text-sm text-slate-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Organizations: \", (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.total_organizations) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Projects: \", (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.total_projects) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Members: \", (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.total_members) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 631,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\",\n        children: getKPIData().map((kpi, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in\",\n          style: {\n            animationDelay: `${index * 0.1}s`\n          },\n          children: /*#__PURE__*/_jsxDEV(KPICard, {\n            title: kpi.title,\n            value: kpi.value,\n            change: kpi.change,\n            changeType: kpi.changeType,\n            icon: kpi.icon,\n            color: kpi.color\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2 space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-semibold text-slate-800 tracking-tight\",\n                  children: userRole === 'Viewer' ? 'Available Projects' : 'My Projects'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-slate-600 mt-1\",\n                  children: \"Manage and track your active projects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/project-overview-analytics\",\n                className: \"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\",\n                children: [\"View Analytics\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5l7 7-7 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n              children: filteredProjects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-fade-in\",\n                style: {\n                  animationDelay: `${index * 0.1}s`\n                },\n                children: /*#__PURE__*/_jsxDEV(ProjectCard, {\n                  project: project,\n                  userRole: userRole\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 708,\n                  columnNumber: 21\n                }, this)\n              }, project.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 15\n            }, this), filteredProjects.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-slate-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-600 text-lg\",\n                children: \"No projects match your current filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-500 text-sm mt-1\",\n                children: \"Try adjusting your search or filter criteria\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(QuickActions, {\n            userRole: userRole,\n            onCreateProject: handleOpenCreateProject,\n            onCreateOrganization: handleOpenCreateOrganization,\n            onManageUsers: handleManageUsers,\n            onInviteMembers: handleInviteMembers,\n            onCreateAIProject: handleOpenCreateAIProject\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(ActivityFeed, {\n            activities: [],\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NotificationPanel, {\n            notifications: notifications,\n            userRole: userRole,\n            loading: notificationsLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 682,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(TaskSummary, {\n          tasks: [],\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 749,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TeamOverview, {\n          teamMembers: teamMembers,\n          userRole: userRole,\n          onInviteMembers: handleInviteMembers\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 748,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreateProjectModal, {\n      isOpen: showCreateProject,\n      onClose: handleCloseCreateProject,\n      onCreateProject: handleCreateProject,\n      organizationId: authService.getOrganizationId(),\n      organizationName: (currentOrganization === null || currentOrganization === void 0 ? void 0 : currentOrganization.name) || 'Organization'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 759,\n      columnNumber: 7\n    }, this), (userRole === null || userRole === void 0 ? void 0 : userRole.toLowerCase()) === 'owner' && /*#__PURE__*/_jsxDEV(CreateAIProjectModal, {\n      isOpen: showCreateAIProject,\n      onClose: handleCloseCreateAIProject,\n      onCreateAIProject: handleCreateAIProject,\n      organizationId: authService.getOrganizationId(),\n      organizationName: (currentOrganization === null || currentOrganization === void 0 ? void 0 : currentOrganization.name) || 'Organization'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 769,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CreateOrganizationModal, {\n      isOpen: showCreateOrganization,\n      onClose: handleCloseCreateOrganization,\n      onCreateOrganization: handleCreateOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 779,\n      columnNumber: 7\n    }, this), (userRole === null || userRole === void 0 ? void 0 : userRole.toLowerCase()) === 'owner' ? /*#__PURE__*/_jsxDEV(OwnerInviteMemberModal, {\n      isOpen: showInviteMember,\n      onClose: handleCloseInviteMember,\n      onInviteMember: handleSendInvitation,\n      organizationId: authService.getOrganizationId(),\n      organizationName: (currentOrganization === null || currentOrganization === void 0 ? void 0 : currentOrganization.name) || 'Organization'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 787,\n      columnNumber: 9\n    }, this) : (userRole === null || userRole === void 0 ? void 0 : userRole.toLowerCase()) === 'admin' ? /*#__PURE__*/_jsxDEV(AdminInviteMemberModal, {\n      isOpen: showInviteMember,\n      onClose: handleCloseInviteMember,\n      onInvite: handleAdminInviteSubmit\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 795,\n      columnNumber: 9\n    }, this) : null]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 585,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleBasedDashboard, \"6HnNvCMz5+4iqBIMz1Z/OkNowOk=\", false, function () {\n  return [useLocation, useUserProfile];\n});\n_c = RoleBasedDashboard;\nexport default RoleBasedDashboard;\nvar _c;\n$RefreshReg$(_c, \"RoleBasedDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "RoleBasedHeader", "KPICard", "ProjectCard", "ActivityFeed", "QuickActions", "TaskSummary", "TeamOverview", "NotificationPanel", "authService", "apiService", "teamService", "notificationService", "CreateProjectModal", "CreateOrganizationModal", "OwnerInviteMemberModal", "CreateAIProjectModal", "AdminInviteMemberModal", "listenForProjectUpdates", "useUserProfile", "jsxDEV", "_jsxDEV", "RoleBasedDashboard", "_s", "_organizations$0$orga", "_organizations$0$orga2", "_currentUser$email", "_organizations$0$orga3", "_currentUser$email2", "location", "userProfile", "currentOrganization", "hookCurrentOrganization", "availableOrganizations", "loading", "profileLoading", "userRole", "setUserRole", "searchValue", "setSearchValue", "filterValue", "setFilterValue", "dashboardData", "setDashboardData", "projects", "setProjects", "currentUser", "setCurrentUser", "organizations", "setOrganizations", "setLoading", "error", "setError", "showWelcome", "setShowWelcome", "welcomeMessage", "setWelcomeMessage", "showCreateProject", "setShowCreateProject", "showCreateOrganization", "setShowCreateOrganization", "showInviteMember", "setShowInviteMember", "showCreateAIProject", "setShowCreateAIProject", "teamMembers", "setTeamMembers", "notifications", "setNotifications", "notificationsLoading", "setNotificationsLoading", "loadDashboardData", "_location$state", "_location$state2", "state", "message", "type", "window", "history", "replaceState", "document", "title", "userResult", "getCurrentUser", "data", "user", "role", "getUserRole", "console", "warn", "id", "firstName", "lastName", "email", "name", "domain", "statsResult", "getDashboardStats", "organizationId", "getOrganizationId", "projectsResult", "getAll", "teamMembersResult", "getTeamMembers", "teamError", "_userResult$data$orga", "_userResult$data$orga2", "notificationsResult", "getNotifications", "limit", "Array", "isArray", "isFirstTime", "checkFirstTimeUser", "createWelcomeNotification", "updatedNotifications", "notificationError", "err", "length", "cleanup", "updateData", "action", "project", "currentOrgId", "log", "handleCreateProject", "projectData", "Error", "newProject", "create", "dispatchEvent", "CustomEvent", "detail", "_currentUser$data", "default", "notifyProjectCreated", "handleOpenCreateProject", "handleCloseCreateProject", "handleOpenCreateAIProject", "handleCloseCreateAIProject", "handleCreateAIProject", "response", "fetch", "method", "headers", "getAccessToken", "body", "JSON", "stringify", "ok", "errorData", "json", "result", "success", "tasks_created", "handleCreateOrganization", "organizationData", "logoFile", "handleOpenCreateOrganization", "handleCloseCreateOrganization", "handleManageUsers", "href", "handleInviteMembers", "handleCloseInviteMember", "handleSendInvitation", "inviteData", "handleAdminInviteSubmit", "emails", "inviteTeamMember", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "reload", "getKPIData", "_realData$total_organ", "_realData$total_proje", "_realData$total_membe", "_realData$recent_acti", "_realData$recent_acti2", "_realData$total_proje2", "_realData$total_membe2", "_realData$total_organ2", "_realData$recent_acti3", "_realData$recent_acti4", "_realData$total_proje3", "_realData$total_organ3", "_realData$total_membe3", "_realData$recent_acti5", "_realData$recent_acti6", "_realData$total_proje4", "_realData$total_organ4", "_realData$total_membe4", "_realData$recent_acti7", "_realData$recent_acti8", "realData", "value", "total_organizations", "toString", "change", "changeType", "icon", "color", "total_projects", "total_members", "recent_activity", "filteredProjects", "filter", "matchesSearch", "toLowerCase", "includes", "description", "matchesFilter", "status", "hasProjectAccess", "organization_id", "organization", "split", "logo", "logo_url", "trim", "avatar", "map", "kpi", "index", "style", "animationDelay", "to", "onCreateProject", "onCreateOrganization", "onManageUsers", "onInviteMembers", "onCreateAIProject", "activities", "tasks", "isOpen", "onClose", "organizationName", "onInviteMember", "onInvite", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/role-based-dashboard/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport authService from '../../utils/realAuthService';\nimport apiService from '../../utils/realApiService';\nimport teamService from '../../utils/teamService';\nimport notificationService from '../../utils/notificationService';\nimport CreateProjectModal from '../../components/modals/CreateProjectModal';\nimport CreateOrganizationModal from '../../components/modals/CreateOrganizationModal';\nimport OwnerInviteMemberModal from '../../components/modals/InviteMemberModal';\nimport CreateAIProjectModal from '../../components/modals/CreateAIProjectModal';\nimport AdminInviteMemberModal from '../team-members/components/InviteMemberModal';\nimport { listenForProjectUpdates } from '../../utils/projectEventService';\nimport { useUserProfile } from '../../hooks/useUserProfile';\n\nconst RoleBasedDashboard = () => {\n  const location = useLocation();\n\n  // Use centralized user profile hook\n  const {\n    userProfile,\n    currentOrganization: hookCurrentOrganization,\n    availableOrganizations,\n    loading: profileLoading\n  } = useUserProfile();\n\n  const [userRole, setUserRole] = useState('member'); // Default role\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [projects, setProjects] = useState([]);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [organizations, setOrganizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showWelcome, setShowWelcome] = useState(false);\n  const [welcomeMessage, setWelcomeMessage] = useState('');\n  const [showCreateProject, setShowCreateProject] = useState(false);\n  const [showCreateOrganization, setShowCreateOrganization] = useState(false);\n  const [showInviteMember, setShowInviteMember] = useState(false);\n  const [showCreateAIProject, setShowCreateAIProject] = useState(false);\n\n  // Real team members data\n  const [teamMembers, setTeamMembers] = useState([]);\n\n  // Real notifications data\n  const [notifications, setNotifications] = useState([]);\n  const [notificationsLoading, setNotificationsLoading] = useState(true);\n\n  // Load dashboard data from backend\n  useEffect(() => {\n    const loadDashboardData = async () => {\n      try {\n        setLoading(true);\n\n        // Check for welcome message from navigation state\n        if (location.state?.message && location.state?.type === 'success') {\n          setWelcomeMessage(location.state.message);\n          setShowWelcome(true);\n          // Clear the state to prevent showing on refresh\n          window.history.replaceState({}, document.title);\n        }\n\n        // Get current user and role\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || authService.getUserRole());\n          setOrganizations(userResult.data.organizations || []);\n        } else {\n          // If no user, set fallback data instead of redirecting\n          console.warn('No user data available, using fallback');\n          setCurrentUser({\n            id: 'fallback-user',\n            firstName: 'User',\n            lastName: '',\n            email: '<EMAIL>',\n            role: 'member'\n          });\n          setUserRole('member');\n          setOrganizations([{\n            id: 'fallback-org',\n            name: 'Default Organization',\n            domain: 'example.com'\n          }]);\n        }\n\n        // Get dashboard stats\n        const statsResult = await authService.getDashboardStats();\n        if (statsResult.data) {\n          setDashboardData(statsResult.data);\n        }\n\n        // Get projects\n        const organizationId = authService.getOrganizationId();\n        if (organizationId) {\n          const projectsResult = await apiService.projects.getAll(organizationId);\n          setProjects(projectsResult || []);\n\n          // Get team members\n          try {\n            const teamMembersResult = await teamService.getTeamMembers(organizationId);\n            setTeamMembers(teamMembersResult || []);\n          } catch (teamError) {\n            console.error('Failed to load team members:', teamError);\n            setTeamMembers([]); // Clear team members on error\n          }\n\n          // Get notifications and check for first-time user\n          try {\n            setNotificationsLoading(true);\n            const notificationsResult = await notificationService.getNotifications({ limit: 10 });\n\n            // Handle the new response structure\n            if (notificationsResult && notificationsResult.data && Array.isArray(notificationsResult.data)) {\n              setNotifications(notificationsResult.data);\n            } else if (Array.isArray(notificationsResult)) {\n              setNotifications(notificationsResult);\n            } else {\n              setNotifications([]);\n            }\n\n            // Check if this is a first-time user and create welcome notification\n            const isFirstTime = await notificationService.checkFirstTimeUser();\n            if (isFirstTime && userResult.data.organizations?.[0]?.name) {\n              await notificationService.createWelcomeNotification(\n                userResult.data.user.id,\n                userResult.data.organizations[0].name\n              );\n\n              // Reload notifications to include the welcome notification\n              const updatedNotifications = await notificationService.getNotifications({ limit: 10 });\n              if (updatedNotifications && updatedNotifications.data && Array.isArray(updatedNotifications.data)) {\n                setNotifications(updatedNotifications.data);\n              } else if (Array.isArray(updatedNotifications)) {\n                setNotifications(updatedNotifications);\n              } else {\n                setNotifications([]);\n              }\n            }\n          } catch (notificationError) {\n            console.error('Failed to load notifications:', notificationError);\n            setNotifications([]); // Clear notifications on error\n          } finally {\n            setNotificationsLoading(false);\n          }\n        }\n\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load dashboard data:', err);\n\n        // Don't set error state that would prevent the page from working\n        // Instead, set fallback data and let the page render\n        if (!currentUser) {\n          setCurrentUser({\n            id: 'fallback-user',\n            firstName: 'User',\n            lastName: '',\n            email: '<EMAIL>',\n            role: 'member'\n          });\n          setUserRole('member');\n        }\n\n        if (organizations.length === 0) {\n          setOrganizations([{\n            id: 'fallback-org',\n            name: 'Default Organization',\n            domain: 'example.com'\n          }]);\n        }\n\n        // Set empty arrays for other data\n        setProjects([]);\n        setTeamMembers([]);\n        setNotifications([]);\n        setNotificationsLoading(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadDashboardData();\n  }, [location.state]);\n\n  // Listen for global project updates\n  useEffect(() => {\n    const cleanup = listenForProjectUpdates(async (updateData) => {\n      const { action, project, organizationId } = updateData;\n\n      // Refresh projects list when projects are created, updated, or deleted\n      if (action === 'created' || action === 'updated' || action === 'deleted' || action === 'refresh') {\n        try {\n          const currentOrgId = authService.getOrganizationId();\n          if (currentOrgId && (organizationId === currentOrgId || !organizationId)) {\n            console.log('Refreshing projects due to update:', action, project);\n            const projectsResult = await apiService.projects.getAll(currentOrgId);\n            setProjects(projectsResult || []);\n          }\n        } catch (error) {\n          console.error('Failed to refresh projects:', error);\n        }\n      }\n    });\n\n    return cleanup;\n  }, []);\n\n  // Project creation handler\n  const handleCreateProject = async (projectData) => {\n    try {\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n\n      console.log('Creating project with data:', projectData);\n      console.log('Organization ID:', organizationId);\n\n      // Create via API\n      const newProject = await apiService.projects.create(organizationId, projectData);\n      console.log('Project creation response:', newProject);\n\n      // Refresh projects list\n      console.log('Refreshing projects list...');\n      const projectsResult = await apiService.projects.getAll(organizationId);\n      console.log('Updated projects list:', projectsResult);\n      setProjects(projectsResult || []);\n\n      // Notify header to refresh projects\n      window.dispatchEvent(new CustomEvent('projectsUpdated', {\n        detail: { organizationId, projects: projectsResult }\n      }));\n\n      // Send notification for project creation\n      try {\n        const notificationService = (await import('../../utils/notificationService')).default;\n        const currentUser = await authService.getCurrentUser();\n        if (currentUser.data?.user) {\n          await notificationService.notifyProjectCreated(\n            newProject.data || newProject,\n            currentUser.data.user.id\n          );\n        }\n      } catch (notificationError) {\n        console.error('Failed to send project creation notification:', notificationError);\n      }\n\n      // Refresh dashboard stats\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n\n      console.log('Project created successfully:', newProject);\n    } catch (error) {\n      console.error('Failed to create project:', error);\n      throw error;\n    }\n  };\n\n  // Quick action handlers\n  const handleOpenCreateProject = () => {\n    setShowCreateProject(true);\n  };\n\n  const handleCloseCreateProject = () => {\n    setShowCreateProject(false);\n  };\n\n  // AI Project creation handlers\n  const handleOpenCreateAIProject = () => {\n    setShowCreateAIProject(true);\n  };\n\n  const handleCloseCreateAIProject = () => {\n    setShowCreateAIProject(false);\n  };\n\n  const handleCreateAIProject = async (projectData) => {\n    try {\n      console.log('Creating AI project with data:', projectData);\n\n      // Create via AI API endpoint\n      const response = await fetch('http://localhost:3001/api/v1/projects/ai-generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authService.getAccessToken()}`\n        },\n        body: JSON.stringify(projectData)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || 'Failed to create AI project');\n      }\n\n      const result = await response.json();\n      console.log('AI Project creation response:', result);\n\n      if (!result.success) {\n        throw new Error(result.message || 'Failed to create AI project');\n      }\n\n      // Refresh projects list\n      const organizationId = authService.getOrganizationId();\n      if (organizationId) {\n        const projectsResult = await apiService.projects.getAll(organizationId);\n        setProjects(projectsResult || []);\n\n        // Notify header to refresh projects\n        window.dispatchEvent(new CustomEvent('projectsUpdated', {\n          detail: { organizationId, projects: projectsResult }\n        }));\n      }\n\n      // Refresh dashboard stats\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n\n      console.log('AI Project created successfully:', result.data);\n\n      // Show success message\n      setWelcomeMessage(`🎉 AI Project \"${projectData.name}\" created successfully with ${result.data.tasks_created} tasks!`);\n      setShowWelcome(true);\n\n    } catch (error) {\n      console.error('Failed to create AI project:', error);\n      throw error;\n    }\n  };\n\n  // Organization creation handlers\n  const handleCreateOrganization = async (organizationData, logoFile) => {\n    try {\n      const result = await apiService.organizations.create(organizationData, logoFile);\n\n      if (result.error) {\n        throw new Error(result.error);\n      }\n\n      // Show success message\n      setWelcomeMessage(result.message || `Organization \"${organizationData.name}\" created successfully!`);\n      setShowWelcome(true);\n\n      // Refresh organizations list if needed\n      // You might want to update the current organization context here\n      console.log('Organization created successfully:', result.data);\n\n      return result.data;\n    } catch (error) {\n      console.error('Failed to create organization:', error);\n      throw error;\n    }\n  };\n\n  const handleOpenCreateOrganization = () => {\n    setShowCreateOrganization(true);\n  };\n\n  const handleCloseCreateOrganization = () => {\n    setShowCreateOrganization(false);\n  };\n\n  const handleManageUsers = () => {\n    // Navigate to team members page\n    window.location.href = '/team-members';\n  };\n\n  const handleInviteMembers = () => {\n    setShowInviteMember(true);\n  };\n\n  const handleCloseInviteMember = () => {\n    setShowInviteMember(false);\n  };\n\n  const handleSendInvitation = async (inviteData) => {\n    try {\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n\n      // Call the backend API to send invitation\n      const response = await fetch(`http://localhost:3001/api/v1/organizations/${organizationId}/invite`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${authService.getAccessToken()}`\n        },\n        body: JSON.stringify({\n          email: inviteData.email,\n          role: inviteData.role\n        })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to send invitation');\n      }\n\n      const result = await response.json();\n      console.log('Invitation sent successfully:', result);\n\n      // Refresh dashboard stats to show updated member count\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n\n    } catch (error) {\n      console.error('Failed to send invitation:', error);\n      throw error;\n    }\n  };\n\n  // Handler for Admin (bulk invite)\n  const handleAdminInviteSubmit = async (inviteData) => {\n    const organizationId = authService.getOrganizationId();\n    if (!organizationId) {\n      console.error('No organization selected');\n      return;\n    }\n\n    try {\n      console.log('Admin inviting members:', inviteData);\n\n      // Send invitations through API\n      for (const email of inviteData.emails) {\n        await teamService.inviteTeamMember(organizationId, {\n          email,\n          role: inviteData.role,\n          message: inviteData.welcomeMessage\n        });\n      }\n\n      console.log('Invitations sent successfully');\n\n      // Refresh dashboard stats to show updated member count\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n    } catch (error) {\n      console.error('Failed to invite members:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-slate-600\">Loading dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n          <p className=\"text-red-600 text-lg mb-2\">Failed to load dashboard</p>\n          <p className=\"text-slate-600 text-sm\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Real projects data is loaded from API in useEffect\n\n  // Real activities and tasks data will be loaded from API when available\n\n  // Role-based KPI data with real data integration\n  const getKPIData = () => {\n    const realData = dashboardData || {};\n\n    switch (userRole) {\n      case 'owner':\n        return [\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Active Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'admin':\n        return [\n          { title: \"Active Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"success\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'member':\n        return [\n          { title: \"My Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'viewer':\n        return [\n          { title: \"Projects Viewed\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Eye\", color: \"primary\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"accent\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"success\" },\n          { title: \"Activity Items\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"BarChart3\", color: \"warning\" }\n        ];\n      default:\n        return [\n          { title: \"Projects\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Organizations\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Members\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Activity\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n    }\n  };\n\n  // Filter projects based on search, filter values, and role-based access\n  const filteredProjects = projects.filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) ||\n                         (project.description || '').toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' ||\n                         (project.status || 'active').toLowerCase() === filterValue.toLowerCase();\n\n    // Role-based project access control\n    const hasProjectAccess = () => {\n      if (!currentUser?.role) return true; // Default access if role not set\n\n      const userRole = currentUser.role.toLowerCase();\n\n      // Viewers should only see projects they are specifically invited to\n      if (userRole === 'viewer') {\n        // For now, viewers can see projects from their organization\n        // In a real implementation, this would check project-specific invitations\n        return project.organization_id === currentUser.organization_id;\n      }\n\n      // Members, admins, and owners can see all projects in their organization\n      return true;\n    };\n\n    return matchesSearch && matchesFilter && hasProjectAccess();\n  });\n\n\n\n  // Get current organization data - use hook data if available, otherwise fallback to local state\n  const currentOrganization = hookCurrentOrganization || (organizations.length > 0 ? {\n    name: organizations[0].organization?.name || organizations[0].name || 'Loading...',\n    domain: organizations[0].organization?.domain || organizations[0].domain || currentUser?.email?.split('@')[1] || 'company.com',\n    logo: organizations[0].organization?.logo_url || organizations[0].logo || '/assets/images/org-logo.png'\n  } : {\n    name: 'Loading...',\n    domain: 'company.com',\n    logo: '/assets/images/org-logo.png'\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\">\n      {/* Role-Based Header */}\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser ? {\n          name: `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() || currentUser.email?.split('@')[0] || 'User',\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }}\n        currentOrganization={currentOrganization}\n      />\n\n      {/* Welcome Message for New Users */}\n      {showWelcome && welcomeMessage && (\n        <div className=\"bg-gradient-to-r from-green-50 to-blue-50 border-l-4 border-green-400 p-6 mt-16 mx-4 rounded-lg shadow-sm\">\n          <div className=\"max-w-7xl mx-auto flex items-start\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-6 w-6 text-green-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div className=\"ml-3 flex-1\">\n              <h3 className=\"text-lg font-medium text-green-800\">Welcome to Agno WorkSphere!</h3>\n              <div className=\"mt-2 text-sm text-green-700\">\n                <p>{welcomeMessage}</p>\n              </div>\n              <div className=\"mt-4\">\n                <button\n                  onClick={() => setShowWelcome(false)}\n                  className=\"bg-green-100 hover:bg-green-200 text-green-800 px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  Got it, thanks!\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* User Info Bar */}\n      <div className={`glass-effect border-b border-white/20 p-4 ${showWelcome ? 'mt-4' : 'mt-16'}`}>\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-sm font-medium text-slate-700\">\n                {currentUser ? `Welcome, ${currentUser.firstName}!` : 'Loading...'}\n              </span>\n            </div>\n            <div className=\"flex items-center gap-2 text-sm text-slate-600\">\n              <span className=\"px-3 py-1 bg-blue-100 text-blue-800 rounded-full font-medium capitalize\">\n                {userRole}\n              </span>\n              {currentOrganization && (\n                <span className=\"text-slate-500\">\n                  • {currentOrganization.name}\n                </span>\n              )}\n            </div>\n          </div>\n          <div className=\"flex items-center gap-3 text-sm text-slate-600\">\n            <span>Organizations: {dashboardData?.total_organizations || 0}</span>\n            <span>•</span>\n            <span>Projects: {dashboardData?.total_projects || 0}</span>\n            <span>•</span>\n            <span>Members: {dashboardData?.total_members || 0}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Dashboard content - header functionality moved to RoleBasedHeader */}\n\n      {/* Main Dashboard Content */}\n      <div className=\"max-w-7xl mx-auto p-8\">\n        {/* Enhanced KPI Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\">\n          {getKPIData().map((kpi, index) => (\n            <div key={index} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n              <KPICard\n                title={kpi.title}\n                value={kpi.value}\n                change={kpi.change}\n                changeType={kpi.changeType}\n                icon={kpi.icon}\n                color={kpi.color}\n              />\n            </div>\n          ))}\n        </div>\n\n        {/* Main Content Grid with improved spacing */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\">\n          {/* Left Column - Projects and Quick Actions */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Projects Section with enhanced header */}\n            <div className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h2 className=\"text-2xl font-semibold text-slate-800 tracking-tight\">\n                    {userRole === 'Viewer' ? 'Available Projects' : 'My Projects'}\n                  </h2>\n                  <p className=\"text-slate-600 mt-1\">Manage and track your active projects</p>\n                </div>\n                <Link \n                  to=\"/project-overview-analytics\"\n                  className=\"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\"\n                >\n                  View Analytics\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n              </div>\n              \n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n                {filteredProjects.map((project, index) => (\n                  <div key={project.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                    <ProjectCard\n                      project={project}\n                      userRole={userRole}\n                    />\n                  </div>\n                ))}\n              </div>\n              \n              {filteredProjects.length === 0 && (\n                <div className=\"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\">\n                  <div className=\"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <svg className=\"w-8 h-8 text-slate-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-slate-600 text-lg\">No projects match your current filters</p>\n                  <p className=\"text-slate-500 text-sm mt-1\">Try adjusting your search or filter criteria</p>\n                </div>\n              )}\n            </div>\n\n            {/* Quick Actions */}\n            <QuickActions\n              userRole={userRole}\n              onCreateProject={handleOpenCreateProject}\n              onCreateOrganization={handleOpenCreateOrganization}\n              onManageUsers={handleManageUsers}\n              onInviteMembers={handleInviteMembers}\n              onCreateAIProject={handleOpenCreateAIProject}\n            />\n          </div>\n\n          {/* Right Column - Activity Feed and Notifications */}\n          <div className=\"space-y-8\">\n            <ActivityFeed activities={[]} userRole={userRole} />\n            <NotificationPanel notifications={notifications} userRole={userRole} loading={notificationsLoading} />\n          </div>\n        </div>\n\n        {/* Bottom Section - Tasks and Team with improved layout */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          <TaskSummary tasks={[]} userRole={userRole} />\n          <TeamOverview\n            teamMembers={teamMembers}\n            userRole={userRole}\n            onInviteMembers={handleInviteMembers}\n          />\n        </div>\n      </div>\n\n      {/* Create Project Modal */}\n      <CreateProjectModal\n        isOpen={showCreateProject}\n        onClose={handleCloseCreateProject}\n        onCreateProject={handleCreateProject}\n        organizationId={authService.getOrganizationId()}\n        organizationName={currentOrganization?.name || 'Organization'}\n      />\n\n      {/* Create AI Project Modal - Owner Only */}\n      {userRole?.toLowerCase() === 'owner' && (\n        <CreateAIProjectModal\n          isOpen={showCreateAIProject}\n          onClose={handleCloseCreateAIProject}\n          onCreateAIProject={handleCreateAIProject}\n          organizationId={authService.getOrganizationId()}\n          organizationName={currentOrganization?.name || 'Organization'}\n        />\n      )}\n\n      {/* Create Organization Modal */}\n      <CreateOrganizationModal\n        isOpen={showCreateOrganization}\n        onClose={handleCloseCreateOrganization}\n        onCreateOrganization={handleCreateOrganization}\n      />\n\n      {/* Role-based Invite Member Modal */}\n      {userRole?.toLowerCase() === 'owner' ? (\n        <OwnerInviteMemberModal\n          isOpen={showInviteMember}\n          onClose={handleCloseInviteMember}\n          onInviteMember={handleSendInvitation}\n          organizationId={authService.getOrganizationId()}\n          organizationName={currentOrganization?.name || 'Organization'}\n        />\n      ) : userRole?.toLowerCase() === 'admin' ? (\n        <AdminInviteMemberModal\n          isOpen={showInviteMember}\n          onClose={handleCloseInviteMember}\n          onInvite={handleAdminInviteSubmit}\n        />\n      ) : null}\n    </div>\n  );\n};\n\nexport default RoleBasedDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,kBAAkB,MAAM,4CAA4C;AAC3E,OAAOC,uBAAuB,MAAM,iDAAiD;AACrF,OAAOC,sBAAsB,MAAM,2CAA2C;AAC9E,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,sBAAsB,MAAM,8CAA8C;AACjF,SAASC,uBAAuB,QAAQ,iCAAiC;AACzE,SAASC,cAAc,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,mBAAA;EAC/B,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM;IACJ8B,WAAW;IACXC,mBAAmB,EAAEC,uBAAuB;IAC5CC,sBAAsB;IACtBC,OAAO,EAAEC;EACX,CAAC,GAAGhB,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqC,OAAO,EAAEgB,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC8D,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACgE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACsE,aAAa,EAAEC,gBAAgB,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMyE,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QAAA,IAAAC,eAAA,EAAAC,gBAAA;QACFvB,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,IAAI,CAAAsB,eAAA,GAAA3C,QAAQ,CAAC6C,KAAK,cAAAF,eAAA,eAAdA,eAAA,CAAgBG,OAAO,IAAI,EAAAF,gBAAA,GAAA5C,QAAQ,CAAC6C,KAAK,cAAAD,gBAAA,uBAAdA,gBAAA,CAAgBG,IAAI,MAAK,SAAS,EAAE;UACjEpB,iBAAiB,CAAC3B,QAAQ,CAAC6C,KAAK,CAACC,OAAO,CAAC;UACzCrB,cAAc,CAAC,IAAI,CAAC;UACpB;UACAuB,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,CAAC;QACjD;;QAEA;QACA,MAAMC,UAAU,GAAG,MAAMzE,WAAW,CAAC0E,cAAc,CAAC,CAAC;QACrD,IAAID,UAAU,CAACE,IAAI,CAACC,IAAI,EAAE;UACxBtC,cAAc,CAACmC,UAAU,CAACE,IAAI,CAACC,IAAI,CAAC;UACpChD,WAAW,CAAC6C,UAAU,CAACE,IAAI,CAACC,IAAI,CAACC,IAAI,IAAI7E,WAAW,CAAC8E,WAAW,CAAC,CAAC,CAAC;UACnEtC,gBAAgB,CAACiC,UAAU,CAACE,IAAI,CAACpC,aAAa,IAAI,EAAE,CAAC;QACvD,CAAC,MAAM;UACL;UACAwC,OAAO,CAACC,IAAI,CAAC,wCAAwC,CAAC;UACtD1C,cAAc,CAAC;YACb2C,EAAE,EAAE,eAAe;YACnBC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE,kBAAkB;YACzBP,IAAI,EAAE;UACR,CAAC,CAAC;UACFjD,WAAW,CAAC,QAAQ,CAAC;UACrBY,gBAAgB,CAAC,CAAC;YAChByC,EAAE,EAAE,cAAc;YAClBI,IAAI,EAAE,sBAAsB;YAC5BC,MAAM,EAAE;UACV,CAAC,CAAC,CAAC;QACL;;QAEA;QACA,MAAMC,WAAW,GAAG,MAAMvF,WAAW,CAACwF,iBAAiB,CAAC,CAAC;QACzD,IAAID,WAAW,CAACZ,IAAI,EAAE;UACpBzC,gBAAgB,CAACqD,WAAW,CAACZ,IAAI,CAAC;QACpC;;QAEA;QACA,MAAMc,cAAc,GAAGzF,WAAW,CAAC0F,iBAAiB,CAAC,CAAC;QACtD,IAAID,cAAc,EAAE;UAClB,MAAME,cAAc,GAAG,MAAM1F,UAAU,CAACkC,QAAQ,CAACyD,MAAM,CAACH,cAAc,CAAC;UACvErD,WAAW,CAACuD,cAAc,IAAI,EAAE,CAAC;;UAEjC;UACA,IAAI;YACF,MAAME,iBAAiB,GAAG,MAAM3F,WAAW,CAAC4F,cAAc,CAACL,cAAc,CAAC;YAC1EhC,cAAc,CAACoC,iBAAiB,IAAI,EAAE,CAAC;UACzC,CAAC,CAAC,OAAOE,SAAS,EAAE;YAClBhB,OAAO,CAACrC,KAAK,CAAC,8BAA8B,EAAEqD,SAAS,CAAC;YACxDtC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;UACtB;;UAEA;UACA,IAAI;YAAA,IAAAuC,qBAAA,EAAAC,sBAAA;YACFpC,uBAAuB,CAAC,IAAI,CAAC;YAC7B,MAAMqC,mBAAmB,GAAG,MAAM/F,mBAAmB,CAACgG,gBAAgB,CAAC;cAAEC,KAAK,EAAE;YAAG,CAAC,CAAC;;YAErF;YACA,IAAIF,mBAAmB,IAAIA,mBAAmB,CAACvB,IAAI,IAAI0B,KAAK,CAACC,OAAO,CAACJ,mBAAmB,CAACvB,IAAI,CAAC,EAAE;cAC9FhB,gBAAgB,CAACuC,mBAAmB,CAACvB,IAAI,CAAC;YAC5C,CAAC,MAAM,IAAI0B,KAAK,CAACC,OAAO,CAACJ,mBAAmB,CAAC,EAAE;cAC7CvC,gBAAgB,CAACuC,mBAAmB,CAAC;YACvC,CAAC,MAAM;cACLvC,gBAAgB,CAAC,EAAE,CAAC;YACtB;;YAEA;YACA,MAAM4C,WAAW,GAAG,MAAMpG,mBAAmB,CAACqG,kBAAkB,CAAC,CAAC;YAClE,IAAID,WAAW,KAAAP,qBAAA,GAAIvB,UAAU,CAACE,IAAI,CAACpC,aAAa,cAAAyD,qBAAA,gBAAAC,sBAAA,GAA7BD,qBAAA,CAAgC,CAAC,CAAC,cAAAC,sBAAA,eAAlCA,sBAAA,CAAoCZ,IAAI,EAAE;cAC3D,MAAMlF,mBAAmB,CAACsG,yBAAyB,CACjDhC,UAAU,CAACE,IAAI,CAACC,IAAI,CAACK,EAAE,EACvBR,UAAU,CAACE,IAAI,CAACpC,aAAa,CAAC,CAAC,CAAC,CAAC8C,IACnC,CAAC;;cAED;cACA,MAAMqB,oBAAoB,GAAG,MAAMvG,mBAAmB,CAACgG,gBAAgB,CAAC;gBAAEC,KAAK,EAAE;cAAG,CAAC,CAAC;cACtF,IAAIM,oBAAoB,IAAIA,oBAAoB,CAAC/B,IAAI,IAAI0B,KAAK,CAACC,OAAO,CAACI,oBAAoB,CAAC/B,IAAI,CAAC,EAAE;gBACjGhB,gBAAgB,CAAC+C,oBAAoB,CAAC/B,IAAI,CAAC;cAC7C,CAAC,MAAM,IAAI0B,KAAK,CAACC,OAAO,CAACI,oBAAoB,CAAC,EAAE;gBAC9C/C,gBAAgB,CAAC+C,oBAAoB,CAAC;cACxC,CAAC,MAAM;gBACL/C,gBAAgB,CAAC,EAAE,CAAC;cACtB;YACF;UACF,CAAC,CAAC,OAAOgD,iBAAiB,EAAE;YAC1B5B,OAAO,CAACrC,KAAK,CAAC,+BAA+B,EAAEiE,iBAAiB,CAAC;YACjEhD,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;UACxB,CAAC,SAAS;YACRE,uBAAuB,CAAC,KAAK,CAAC;UAChC;QACF;QAEAlB,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOiE,GAAG,EAAE;QACZ7B,OAAO,CAACrC,KAAK,CAAC,gCAAgC,EAAEkE,GAAG,CAAC;;QAEpD;QACA;QACA,IAAI,CAACvE,WAAW,EAAE;UAChBC,cAAc,CAAC;YACb2C,EAAE,EAAE,eAAe;YACnBC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE,kBAAkB;YACzBP,IAAI,EAAE;UACR,CAAC,CAAC;UACFjD,WAAW,CAAC,QAAQ,CAAC;QACvB;QAEA,IAAIW,aAAa,CAACsE,MAAM,KAAK,CAAC,EAAE;UAC9BrE,gBAAgB,CAAC,CAAC;YAChByC,EAAE,EAAE,cAAc;YAClBI,IAAI,EAAE,sBAAsB;YAC5BC,MAAM,EAAE;UACV,CAAC,CAAC,CAAC;QACL;;QAEA;QACAlD,WAAW,CAAC,EAAE,CAAC;QACfqB,cAAc,CAAC,EAAE,CAAC;QAClBE,gBAAgB,CAAC,EAAE,CAAC;QACpBE,uBAAuB,CAAC,KAAK,CAAC;MAChC,CAAC,SAAS;QACRpB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDqB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAC1C,QAAQ,CAAC6C,KAAK,CAAC,CAAC;;EAEpB;EACA5E,SAAS,CAAC,MAAM;IACd,MAAMyH,OAAO,GAAGrG,uBAAuB,CAAC,MAAOsG,UAAU,IAAK;MAC5D,MAAM;QAAEC,MAAM;QAAEC,OAAO;QAAExB;MAAe,CAAC,GAAGsB,UAAU;;MAEtD;MACA,IAAIC,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChG,IAAI;UACF,MAAME,YAAY,GAAGlH,WAAW,CAAC0F,iBAAiB,CAAC,CAAC;UACpD,IAAIwB,YAAY,KAAKzB,cAAc,KAAKyB,YAAY,IAAI,CAACzB,cAAc,CAAC,EAAE;YACxEV,OAAO,CAACoC,GAAG,CAAC,oCAAoC,EAAEH,MAAM,EAAEC,OAAO,CAAC;YAClE,MAAMtB,cAAc,GAAG,MAAM1F,UAAU,CAACkC,QAAQ,CAACyD,MAAM,CAACsB,YAAY,CAAC;YACrE9E,WAAW,CAACuD,cAAc,IAAI,EAAE,CAAC;UACnC;QACF,CAAC,CAAC,OAAOjD,KAAK,EAAE;UACdqC,OAAO,CAACrC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD;MACF;IACF,CAAC,CAAC;IAEF,OAAOoE,OAAO;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjD,IAAI;MACF,MAAM5B,cAAc,GAAGzF,WAAW,CAAC0F,iBAAiB,CAAC,CAAC;MACtD,IAAI,CAACD,cAAc,EAAE;QACnB,MAAM,IAAI6B,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MAEAvC,OAAO,CAACoC,GAAG,CAAC,6BAA6B,EAAEE,WAAW,CAAC;MACvDtC,OAAO,CAACoC,GAAG,CAAC,kBAAkB,EAAE1B,cAAc,CAAC;;MAE/C;MACA,MAAM8B,UAAU,GAAG,MAAMtH,UAAU,CAACkC,QAAQ,CAACqF,MAAM,CAAC/B,cAAc,EAAE4B,WAAW,CAAC;MAChFtC,OAAO,CAACoC,GAAG,CAAC,4BAA4B,EAAEI,UAAU,CAAC;;MAErD;MACAxC,OAAO,CAACoC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,MAAMxB,cAAc,GAAG,MAAM1F,UAAU,CAACkC,QAAQ,CAACyD,MAAM,CAACH,cAAc,CAAC;MACvEV,OAAO,CAACoC,GAAG,CAAC,wBAAwB,EAAExB,cAAc,CAAC;MACrDvD,WAAW,CAACuD,cAAc,IAAI,EAAE,CAAC;;MAEjC;MACAvB,MAAM,CAACqD,aAAa,CAAC,IAAIC,WAAW,CAAC,iBAAiB,EAAE;QACtDC,MAAM,EAAE;UAAElC,cAAc;UAAEtD,QAAQ,EAAEwD;QAAe;MACrD,CAAC,CAAC,CAAC;;MAEH;MACA,IAAI;QAAA,IAAAiC,iBAAA;QACF,MAAMzH,mBAAmB,GAAG,CAAC,MAAM,MAAM,CAAC,iCAAiC,CAAC,EAAE0H,OAAO;QACrF,MAAMxF,WAAW,GAAG,MAAMrC,WAAW,CAAC0E,cAAc,CAAC,CAAC;QACtD,KAAAkD,iBAAA,GAAIvF,WAAW,CAACsC,IAAI,cAAAiD,iBAAA,eAAhBA,iBAAA,CAAkBhD,IAAI,EAAE;UAC1B,MAAMzE,mBAAmB,CAAC2H,oBAAoB,CAC5CP,UAAU,CAAC5C,IAAI,IAAI4C,UAAU,EAC7BlF,WAAW,CAACsC,IAAI,CAACC,IAAI,CAACK,EACxB,CAAC;QACH;MACF,CAAC,CAAC,OAAO0B,iBAAiB,EAAE;QAC1B5B,OAAO,CAACrC,KAAK,CAAC,+CAA+C,EAAEiE,iBAAiB,CAAC;MACnF;;MAEA;MACA,MAAMpB,WAAW,GAAG,MAAMvF,WAAW,CAACwF,iBAAiB,CAAC,CAAC;MACzD,IAAID,WAAW,CAACZ,IAAI,EAAE;QACpBzC,gBAAgB,CAACqD,WAAW,CAACZ,IAAI,CAAC;MACpC;MAEAI,OAAO,CAACoC,GAAG,CAAC,+BAA+B,EAAEI,UAAU,CAAC;IAC1D,CAAC,CAAC,OAAO7E,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMqF,uBAAuB,GAAGA,CAAA,KAAM;IACpC9E,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM+E,wBAAwB,GAAGA,CAAA,KAAM;IACrC/E,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMgF,yBAAyB,GAAGA,CAAA,KAAM;IACtC1E,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM2E,0BAA0B,GAAGA,CAAA,KAAM;IACvC3E,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAM4E,qBAAqB,GAAG,MAAOd,WAAW,IAAK;IACnD,IAAI;MACFtC,OAAO,CAACoC,GAAG,CAAC,gCAAgC,EAAEE,WAAW,CAAC;;MAE1D;MACA,MAAMe,QAAQ,GAAG,MAAMC,KAAK,CAAC,mDAAmD,EAAE;QAChFC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUvI,WAAW,CAACwI,cAAc,CAAC,CAAC;QACzD,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACtB,WAAW;MAClC,CAAC,CAAC;MAEF,IAAI,CAACe,QAAQ,CAACQ,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIxB,KAAK,CAACuB,SAAS,CAAClB,MAAM,IAAI,6BAA6B,CAAC;MACpE;MAEA,MAAMoB,MAAM,GAAG,MAAMX,QAAQ,CAACU,IAAI,CAAC,CAAC;MACpC/D,OAAO,CAACoC,GAAG,CAAC,+BAA+B,EAAE4B,MAAM,CAAC;MAEpD,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;QACnB,MAAM,IAAI1B,KAAK,CAACyB,MAAM,CAAC7E,OAAO,IAAI,6BAA6B,CAAC;MAClE;;MAEA;MACA,MAAMuB,cAAc,GAAGzF,WAAW,CAAC0F,iBAAiB,CAAC,CAAC;MACtD,IAAID,cAAc,EAAE;QAClB,MAAME,cAAc,GAAG,MAAM1F,UAAU,CAACkC,QAAQ,CAACyD,MAAM,CAACH,cAAc,CAAC;QACvErD,WAAW,CAACuD,cAAc,IAAI,EAAE,CAAC;;QAEjC;QACAvB,MAAM,CAACqD,aAAa,CAAC,IAAIC,WAAW,CAAC,iBAAiB,EAAE;UACtDC,MAAM,EAAE;YAAElC,cAAc;YAAEtD,QAAQ,EAAEwD;UAAe;QACrD,CAAC,CAAC,CAAC;MACL;;MAEA;MACA,MAAMJ,WAAW,GAAG,MAAMvF,WAAW,CAACwF,iBAAiB,CAAC,CAAC;MACzD,IAAID,WAAW,CAACZ,IAAI,EAAE;QACpBzC,gBAAgB,CAACqD,WAAW,CAACZ,IAAI,CAAC;MACpC;MAEAI,OAAO,CAACoC,GAAG,CAAC,kCAAkC,EAAE4B,MAAM,CAACpE,IAAI,CAAC;;MAE5D;MACA5B,iBAAiB,CAAC,kBAAkBsE,WAAW,CAAChC,IAAI,+BAA+B0D,MAAM,CAACpE,IAAI,CAACsE,aAAa,SAAS,CAAC;MACtHpG,cAAc,CAAC,IAAI,CAAC;IAEtB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMwG,wBAAwB,GAAG,MAAAA,CAAOC,gBAAgB,EAAEC,QAAQ,KAAK;IACrE,IAAI;MACF,MAAML,MAAM,GAAG,MAAM9I,UAAU,CAACsC,aAAa,CAACiF,MAAM,CAAC2B,gBAAgB,EAAEC,QAAQ,CAAC;MAEhF,IAAIL,MAAM,CAACrG,KAAK,EAAE;QAChB,MAAM,IAAI4E,KAAK,CAACyB,MAAM,CAACrG,KAAK,CAAC;MAC/B;;MAEA;MACAK,iBAAiB,CAACgG,MAAM,CAAC7E,OAAO,IAAI,iBAAiBiF,gBAAgB,CAAC9D,IAAI,yBAAyB,CAAC;MACpGxC,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA;MACAkC,OAAO,CAACoC,GAAG,CAAC,oCAAoC,EAAE4B,MAAM,CAACpE,IAAI,CAAC;MAE9D,OAAOoE,MAAM,CAACpE,IAAI;IACpB,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAM2G,4BAA4B,GAAGA,CAAA,KAAM;IACzClG,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMmG,6BAA6B,GAAGA,CAAA,KAAM;IAC1CnG,yBAAyB,CAAC,KAAK,CAAC;EAClC,CAAC;EAED,MAAMoG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAnF,MAAM,CAAChD,QAAQ,CAACoI,IAAI,GAAG,eAAe;EACxC,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCpG,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMqG,uBAAuB,GAAGA,CAAA,KAAM;IACpCrG,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMsG,oBAAoB,GAAG,MAAOC,UAAU,IAAK;IACjD,IAAI;MACF,MAAMnE,cAAc,GAAGzF,WAAW,CAAC0F,iBAAiB,CAAC,CAAC;MACtD,IAAI,CAACD,cAAc,EAAE;QACnB,MAAM,IAAI6B,KAAK,CAAC,uBAAuB,CAAC;MAC1C;;MAEA;MACA,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAAC,8CAA8C5C,cAAc,SAAS,EAAE;QAClG6C,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUvI,WAAW,CAACwI,cAAc,CAAC,CAAC;QACzD,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBvD,KAAK,EAAEwE,UAAU,CAACxE,KAAK;UACvBP,IAAI,EAAE+E,UAAU,CAAC/E;QACnB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACuD,QAAQ,CAACQ,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIxB,KAAK,CAACuB,SAAS,CAAC3E,OAAO,IAAI,2BAA2B,CAAC;MACnE;MAEA,MAAM6E,MAAM,GAAG,MAAMX,QAAQ,CAACU,IAAI,CAAC,CAAC;MACpC/D,OAAO,CAACoC,GAAG,CAAC,+BAA+B,EAAE4B,MAAM,CAAC;;MAEpD;MACA,MAAMxD,WAAW,GAAG,MAAMvF,WAAW,CAACwF,iBAAiB,CAAC,CAAC;MACzD,IAAID,WAAW,CAACZ,IAAI,EAAE;QACpBzC,gBAAgB,CAACqD,WAAW,CAACZ,IAAI,CAAC;MACpC;IAEF,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMmH,uBAAuB,GAAG,MAAOD,UAAU,IAAK;IACpD,MAAMnE,cAAc,GAAGzF,WAAW,CAAC0F,iBAAiB,CAAC,CAAC;IACtD,IAAI,CAACD,cAAc,EAAE;MACnBV,OAAO,CAACrC,KAAK,CAAC,0BAA0B,CAAC;MACzC;IACF;IAEA,IAAI;MACFqC,OAAO,CAACoC,GAAG,CAAC,yBAAyB,EAAEyC,UAAU,CAAC;;MAElD;MACA,KAAK,MAAMxE,KAAK,IAAIwE,UAAU,CAACE,MAAM,EAAE;QACrC,MAAM5J,WAAW,CAAC6J,gBAAgB,CAACtE,cAAc,EAAE;UACjDL,KAAK;UACLP,IAAI,EAAE+E,UAAU,CAAC/E,IAAI;UACrBX,OAAO,EAAE0F,UAAU,CAAC9G;QACtB,CAAC,CAAC;MACJ;MAEAiC,OAAO,CAACoC,GAAG,CAAC,+BAA+B,CAAC;;MAE5C;MACA,MAAM5B,WAAW,GAAG,MAAMvF,WAAW,CAACwF,iBAAiB,CAAC,CAAC;MACzD,IAAID,WAAW,CAACZ,IAAI,EAAE;QACpBzC,gBAAgB,CAACqD,WAAW,CAACZ,IAAI,CAAC;MACpC;IACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;IACF;EACF,CAAC;;EAED;EACA,IAAIjB,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKoJ,SAAS,EAAC,8GAA8G;MAAAC,QAAA,eAC3HrJ,OAAA;QAAKoJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrJ,OAAA;UAAKoJ,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGzJ,OAAA;UAAGoJ,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI3H,KAAK,EAAE;IACT,oBACE9B,OAAA;MAAKoJ,SAAS,EAAC,8GAA8G;MAAAC,QAAA,eAC3HrJ,OAAA;QAAKoJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrJ,OAAA;UAAKoJ,SAAS,EAAC,iFAAiF;UAAAC,QAAA,eAC9FrJ,OAAA;YAAKoJ,SAAS,EAAC,sBAAsB;YAACM,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAP,QAAA,eACzFrJ,OAAA;cAAM6J,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAmD;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNzJ,OAAA;UAAGoJ,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrEzJ,OAAA;UAAGoJ,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAEvH;QAAK;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjDzJ,OAAA;UACEiK,OAAO,EAAEA,CAAA,KAAMzG,MAAM,CAAChD,QAAQ,CAAC0J,MAAM,CAAC,CAAE;UACxCd,SAAS,EAAC,sFAAsF;UAAAC,QAAA,EACjG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;;EAEA;;EAEA;EACA,MAAMU,UAAU,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACvB,MAAMC,QAAQ,GAAGnK,aAAa,IAAI,CAAC,CAAC;IAEpC,QAAQN,QAAQ;MACd,KAAK,OAAO;QACV,OAAO,CACL;UAAE6C,KAAK,EAAE,eAAe;UAAE6H,KAAK,EAAE,EAAArB,qBAAA,GAAAoB,QAAQ,CAACE,mBAAmB,cAAAtB,qBAAA,uBAA5BA,qBAAA,CAA8BuB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC3J;UAAEnI,KAAK,EAAE,iBAAiB;UAAE6H,KAAK,EAAE,EAAApB,qBAAA,GAAAmB,QAAQ,CAACQ,cAAc,cAAA3B,qBAAA,uBAAvBA,qBAAA,CAAyBsB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC1J;UAAEnI,KAAK,EAAE,cAAc;UAAE6H,KAAK,EAAE,EAAAnB,qBAAA,GAAAkB,QAAQ,CAACS,aAAa,cAAA3B,qBAAA,uBAAtBA,qBAAA,CAAwBqB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAChJ;UAAEnI,KAAK,EAAE,iBAAiB;UAAE6H,KAAK,EAAE,EAAAlB,qBAAA,GAAAiB,QAAQ,CAACU,eAAe,cAAA3B,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BtE,MAAM,cAAAuE,sBAAA,uBAAhCA,sBAAA,CAAkCmB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,OAAO;QACV,OAAO,CACL;UAAEnI,KAAK,EAAE,iBAAiB;UAAE6H,KAAK,EAAE,EAAAhB,sBAAA,GAAAe,QAAQ,CAACQ,cAAc,cAAAvB,sBAAA,uBAAvBA,sBAAA,CAAyBkB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC1J;UAAEnI,KAAK,EAAE,cAAc;UAAE6H,KAAK,EAAE,EAAAf,sBAAA,GAAAc,QAAQ,CAACS,aAAa,cAAAvB,sBAAA,uBAAtBA,sBAAA,CAAwBiB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjJ;UAAEnI,KAAK,EAAE,eAAe;UAAE6H,KAAK,EAAE,EAAAd,sBAAA,GAAAa,QAAQ,CAACE,mBAAmB,cAAAf,sBAAA,uBAA5BA,sBAAA,CAA8BgB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC1J;UAAEnI,KAAK,EAAE,iBAAiB;UAAE6H,KAAK,EAAE,EAAAb,sBAAA,GAAAY,QAAQ,CAACU,eAAe,cAAAtB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0B3E,MAAM,cAAA4E,sBAAA,uBAAhCA,sBAAA,CAAkCc,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEnI,KAAK,EAAE,aAAa;UAAE6H,KAAK,EAAE,EAAAX,sBAAA,GAAAU,QAAQ,CAACQ,cAAc,cAAAlB,sBAAA,uBAAvBA,sBAAA,CAAyBa,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EACtJ;UAAEnI,KAAK,EAAE,eAAe;UAAE6H,KAAK,EAAE,EAAAV,sBAAA,GAAAS,QAAQ,CAACE,mBAAmB,cAAAX,sBAAA,uBAA5BA,sBAAA,CAA8BY,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC3J;UAAEnI,KAAK,EAAE,cAAc;UAAE6H,KAAK,EAAE,EAAAT,sBAAA,GAAAQ,QAAQ,CAACS,aAAa,cAAAjB,sBAAA,uBAAtBA,sBAAA,CAAwBW,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAChJ;UAAEnI,KAAK,EAAE,iBAAiB;UAAE6H,KAAK,EAAE,EAAAR,sBAAA,GAAAO,QAAQ,CAACU,eAAe,cAAAjB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BhF,MAAM,cAAAiF,sBAAA,uBAAhCA,sBAAA,CAAkCS,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEnI,KAAK,EAAE,iBAAiB;UAAE6H,KAAK,EAAE,EAAAN,sBAAA,GAAAK,QAAQ,CAACQ,cAAc,cAAAb,sBAAA,uBAAvBA,sBAAA,CAAyBQ,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAC,EACnJ;UAAEnI,KAAK,EAAE,eAAe;UAAE6H,KAAK,EAAE,EAAAL,sBAAA,GAAAI,QAAQ,CAACE,mBAAmB,cAAAN,sBAAA,uBAA5BA,sBAAA,CAA8BO,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC1J;UAAEnI,KAAK,EAAE,cAAc;UAAE6H,KAAK,EAAE,EAAAJ,sBAAA,GAAAG,QAAQ,CAACS,aAAa,cAAAZ,sBAAA,uBAAtBA,sBAAA,CAAwBM,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjJ;UAAEnI,KAAK,EAAE,gBAAgB;UAAE6H,KAAK,EAAE,EAAAH,sBAAA,GAAAE,QAAQ,CAACU,eAAe,cAAAZ,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BrF,MAAM,cAAAsF,sBAAA,uBAAhCA,sBAAA,CAAkCI,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH;QACE,OAAO,CACL;UAAEnI,KAAK,EAAE,UAAU;UAAE6H,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC5G;UAAEnI,KAAK,EAAE,eAAe;UAAE6H,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/G;UAAEnI,KAAK,EAAE,SAAS;UAAE6H,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EACrG;UAAEnI,KAAK,EAAE,UAAU;UAAE6H,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAC3G;IACL;EACF,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAG5K,QAAQ,CAAC6K,MAAM,CAAC/F,OAAO,IAAI;IAClD,MAAMgG,aAAa,GAAGhG,OAAO,CAAC5B,IAAI,CAAC6H,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtL,WAAW,CAACqL,WAAW,CAAC,CAAC,CAAC,IAC/D,CAACjG,OAAO,CAACmG,WAAW,IAAI,EAAE,EAAEF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtL,WAAW,CAACqL,WAAW,CAAC,CAAC,CAAC;IAClG,MAAMG,aAAa,GAAGtL,WAAW,KAAK,KAAK,IACtB,CAACkF,OAAO,CAACqG,MAAM,IAAI,QAAQ,EAAEJ,WAAW,CAAC,CAAC,KAAKnL,WAAW,CAACmL,WAAW,CAAC,CAAC;;IAE7F;IACA,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,IAAI,EAAClL,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEwC,IAAI,GAAE,OAAO,IAAI,CAAC,CAAC;;MAErC,MAAMlD,QAAQ,GAAGU,WAAW,CAACwC,IAAI,CAACqI,WAAW,CAAC,CAAC;;MAE/C;MACA,IAAIvL,QAAQ,KAAK,QAAQ,EAAE;QACzB;QACA;QACA,OAAOsF,OAAO,CAACuG,eAAe,KAAKnL,WAAW,CAACmL,eAAe;MAChE;;MAEA;MACA,OAAO,IAAI;IACb,CAAC;IAED,OAAOP,aAAa,IAAII,aAAa,IAAIE,gBAAgB,CAAC,CAAC;EAC7D,CAAC,CAAC;;EAIF;EACA,MAAMjM,mBAAmB,GAAGC,uBAAuB,KAAKgB,aAAa,CAACsE,MAAM,GAAG,CAAC,GAAG;IACjFxB,IAAI,EAAE,EAAAtE,qBAAA,GAAAwB,aAAa,CAAC,CAAC,CAAC,CAACkL,YAAY,cAAA1M,qBAAA,uBAA7BA,qBAAA,CAA+BsE,IAAI,KAAI9C,aAAa,CAAC,CAAC,CAAC,CAAC8C,IAAI,IAAI,YAAY;IAClFC,MAAM,EAAE,EAAAtE,sBAAA,GAAAuB,aAAa,CAAC,CAAC,CAAC,CAACkL,YAAY,cAAAzM,sBAAA,uBAA7BA,sBAAA,CAA+BsE,MAAM,KAAI/C,aAAa,CAAC,CAAC,CAAC,CAAC+C,MAAM,KAAIjD,WAAW,aAAXA,WAAW,wBAAApB,kBAAA,GAAXoB,WAAW,CAAE+C,KAAK,cAAAnE,kBAAA,uBAAlBA,kBAAA,CAAoByM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,aAAa;IAC9HC,IAAI,EAAE,EAAAzM,sBAAA,GAAAqB,aAAa,CAAC,CAAC,CAAC,CAACkL,YAAY,cAAAvM,sBAAA,uBAA7BA,sBAAA,CAA+B0M,QAAQ,KAAIrL,aAAa,CAAC,CAAC,CAAC,CAACoL,IAAI,IAAI;EAC5E,CAAC,GAAG;IACFtI,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,aAAa;IACrBqI,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,oBACE/M,OAAA;IAAKoJ,SAAS,EAAC,6EAA6E;IAAAC,QAAA,gBAE1FrJ,OAAA,CAACpB,eAAe;MACdmC,QAAQ,EAAEA,QAAQ,CAACuL,WAAW,CAAC,CAAE;MACjC7K,WAAW,EAAEA,WAAW,GAAG;QACzBgD,IAAI,EAAE,GAAGhD,WAAW,CAAC6C,SAAS,IAAI,EAAE,IAAI7C,WAAW,CAAC8C,QAAQ,IAAI,EAAE,EAAE,CAAC0I,IAAI,CAAC,CAAC,MAAA1M,mBAAA,GAAIkB,WAAW,CAAC+C,KAAK,cAAAjE,mBAAA,uBAAjBA,mBAAA,CAAmBuM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,MAAM;QACzHtI,KAAK,EAAE/C,WAAW,CAAC+C,KAAK;QACxB0I,MAAM,EAAEzL,WAAW,CAACyL,MAAM,IAAI,2BAA2B;QACzDjJ,IAAI,EAAElD;MACR,CAAC,GAAG;QACF0D,IAAI,EAAE,YAAY;QAClBD,KAAK,EAAE,EAAE;QACT0I,MAAM,EAAE,2BAA2B;QACnCjJ,IAAI,EAAElD;MACR,CAAE;MACFL,mBAAmB,EAAEA;IAAoB;MAAA4I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,EAGDzH,WAAW,IAAIE,cAAc,iBAC5BlC,OAAA;MAAKoJ,SAAS,EAAC,2GAA2G;MAAAC,QAAA,eACxHrJ,OAAA;QAAKoJ,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDrJ,OAAA;UAAKoJ,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BrJ,OAAA;YAAKoJ,SAAS,EAAC,wBAAwB;YAACM,IAAI,EAAC,MAAM;YAACE,OAAO,EAAC,WAAW;YAACD,MAAM,EAAC,cAAc;YAAAN,QAAA,eAC3FrJ,OAAA;cAAM6J,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA+C;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNzJ,OAAA;UAAKoJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrJ,OAAA;YAAIoJ,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnFzJ,OAAA;YAAKoJ,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1CrJ,OAAA;cAAAqJ,QAAA,EAAInH;YAAc;cAAAoH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACNzJ,OAAA;YAAKoJ,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBrJ,OAAA;cACEiK,OAAO,EAAEA,CAAA,KAAMhI,cAAc,CAAC,KAAK,CAAE;cACrCmH,SAAS,EAAC,2GAA2G;cAAAC,QAAA,EACtH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDzJ,OAAA;MAAKoJ,SAAS,EAAE,6CAA6CpH,WAAW,GAAG,MAAM,GAAG,OAAO,EAAG;MAAAqH,QAAA,eAC5FrJ,OAAA;QAAKoJ,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClErJ,OAAA;UAAKoJ,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCrJ,OAAA;YAAKoJ,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCrJ,OAAA;cAAKoJ,SAAS,EAAC;YAAiD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEzJ,OAAA;cAAMoJ,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EACjD5H,WAAW,GAAG,YAAYA,WAAW,CAAC6C,SAAS,GAAG,GAAG;YAAY;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNzJ,OAAA;YAAKoJ,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DrJ,OAAA;cAAMoJ,SAAS,EAAC,yEAAyE;cAAAC,QAAA,EACtFtI;YAAQ;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EACN/I,mBAAmB,iBAClBV,OAAA;cAAMoJ,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAAC,SAC7B,EAAC3I,mBAAmB,CAAC+D,IAAI;YAAA;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNzJ,OAAA;UAAKoJ,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DrJ,OAAA;YAAAqJ,QAAA,GAAM,iBAAe,EAAC,CAAAhI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqK,mBAAmB,KAAI,CAAC;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrEzJ,OAAA;YAAAqJ,QAAA,EAAM;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACdzJ,OAAA;YAAAqJ,QAAA,GAAM,YAAU,EAAC,CAAAhI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2K,cAAc,KAAI,CAAC;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3DzJ,OAAA;YAAAqJ,QAAA,EAAM;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACdzJ,OAAA;YAAAqJ,QAAA,GAAM,WAAS,EAAC,CAAAhI,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4K,aAAa,KAAI,CAAC;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAKNzJ,OAAA;MAAKoJ,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAEpCrJ,OAAA;QAAKoJ,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxEc,UAAU,CAAC,CAAC,CAACgD,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC3BrN,OAAA;UAAiBoJ,SAAS,EAAC,iBAAiB;UAACkE,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAI,CAAE;UAAAhE,QAAA,eACxFrJ,OAAA,CAACnB,OAAO;YACN+E,KAAK,EAAEwJ,GAAG,CAACxJ,KAAM;YACjB6H,KAAK,EAAE2B,GAAG,CAAC3B,KAAM;YACjBG,MAAM,EAAEwB,GAAG,CAACxB,MAAO;YACnBC,UAAU,EAAEuB,GAAG,CAACvB,UAAW;YAC3BC,IAAI,EAAEsB,GAAG,CAACtB,IAAK;YACfC,KAAK,EAAEqB,GAAG,CAACrB;UAAM;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC,GARM4D,KAAK;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzJ,OAAA;QAAKoJ,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1DrJ,OAAA;UAAKoJ,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAEtCrJ,OAAA;YAAKoJ,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrJ,OAAA;cAAKoJ,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDrJ,OAAA;gBAAAqJ,QAAA,gBACErJ,OAAA;kBAAIoJ,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,EACjEtI,QAAQ,KAAK,QAAQ,GAAG,oBAAoB,GAAG;gBAAa;kBAAAuI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACLzJ,OAAA;kBAAGoJ,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNzJ,OAAA,CAACtB,IAAI;gBACH8O,EAAE,EAAC,6BAA6B;gBAChCpE,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,GAC3K,gBAEC,eAAArJ,OAAA;kBAAKoJ,SAAS,EAAC,SAAS;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAC5ErJ,OAAA;oBAAM6J,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAc;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENzJ,OAAA;cAAKoJ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD8C,gBAAgB,CAACgB,GAAG,CAAC,CAAC9G,OAAO,EAAEgH,KAAK,kBACnCrN,OAAA;gBAAsBoJ,SAAS,EAAC,iBAAiB;gBAACkE,KAAK,EAAE;kBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;gBAAI,CAAE;gBAAAhE,QAAA,eAC7FrJ,OAAA,CAAClB,WAAW;kBACVuH,OAAO,EAAEA,OAAQ;kBACjBtF,QAAQ,EAAEA;gBAAS;kBAAAuI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC,GAJMpD,OAAO,CAAChC,EAAE;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAEL0C,gBAAgB,CAAClG,MAAM,KAAK,CAAC,iBAC5BjG,OAAA;cAAKoJ,SAAS,EAAC,mFAAmF;cAAAC,QAAA,gBAChGrJ,OAAA;gBAAKoJ,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,eAChGrJ,OAAA;kBAAKoJ,SAAS,EAAC,wBAAwB;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAC3FrJ,OAAA;oBAAM6J,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAsH;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3L;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzJ,OAAA;gBAAGoJ,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChFzJ,OAAA;gBAAGoJ,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzJ,OAAA,CAAChB,YAAY;YACX+B,QAAQ,EAAEA,QAAS;YACnB0M,eAAe,EAAEtG,uBAAwB;YACzCuG,oBAAoB,EAAEjF,4BAA6B;YACnDkF,aAAa,EAAEhF,iBAAkB;YACjCiF,eAAe,EAAE/E,mBAAoB;YACrCgF,iBAAiB,EAAExG;UAA0B;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNzJ,OAAA;UAAKoJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrJ,OAAA,CAACjB,YAAY;YAAC+O,UAAU,EAAE,EAAG;YAAC/M,QAAQ,EAAEA;UAAS;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDzJ,OAAA,CAACb,iBAAiB;YAAC2D,aAAa,EAAEA,aAAc;YAAC/B,QAAQ,EAAEA,QAAS;YAACF,OAAO,EAAEmC;UAAqB;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzJ,OAAA;QAAKoJ,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDrJ,OAAA,CAACf,WAAW;UAAC8O,KAAK,EAAE,EAAG;UAAChN,QAAQ,EAAEA;QAAS;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CzJ,OAAA,CAACd,YAAY;UACX0D,WAAW,EAAEA,WAAY;UACzB7B,QAAQ,EAAEA,QAAS;UACnB6M,eAAe,EAAE/E;QAAoB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzJ,OAAA,CAACR,kBAAkB;MACjBwO,MAAM,EAAE5L,iBAAkB;MAC1B6L,OAAO,EAAE7G,wBAAyB;MAClCqG,eAAe,EAAEjH,mBAAoB;MACrC3B,cAAc,EAAEzF,WAAW,CAAC0F,iBAAiB,CAAC,CAAE;MAChDoJ,gBAAgB,EAAE,CAAAxN,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE+D,IAAI,KAAI;IAAe;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,EAGD,CAAA1I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuL,WAAW,CAAC,CAAC,MAAK,OAAO,iBAClCtM,OAAA,CAACL,oBAAoB;MACnBqO,MAAM,EAAEtL,mBAAoB;MAC5BuL,OAAO,EAAE3G,0BAA2B;MACpCuG,iBAAiB,EAAEtG,qBAAsB;MACzC1C,cAAc,EAAEzF,WAAW,CAAC0F,iBAAiB,CAAC,CAAE;MAChDoJ,gBAAgB,EAAE,CAAAxN,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE+D,IAAI,KAAI;IAAe;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CACF,eAGDzJ,OAAA,CAACP,uBAAuB;MACtBuO,MAAM,EAAE1L,sBAAuB;MAC/B2L,OAAO,EAAEvF,6BAA8B;MACvCgF,oBAAoB,EAAEpF;IAAyB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,EAGD,CAAA1I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuL,WAAW,CAAC,CAAC,MAAK,OAAO,gBAClCtM,OAAA,CAACN,sBAAsB;MACrBsO,MAAM,EAAExL,gBAAiB;MACzByL,OAAO,EAAEnF,uBAAwB;MACjCqF,cAAc,EAAEpF,oBAAqB;MACrClE,cAAc,EAAEzF,WAAW,CAAC0F,iBAAiB,CAAC,CAAE;MAChDoJ,gBAAgB,EAAE,CAAAxN,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE+D,IAAI,KAAI;IAAe;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,GACA,CAAA1I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuL,WAAW,CAAC,CAAC,MAAK,OAAO,gBACrCtM,OAAA,CAACJ,sBAAsB;MACrBoO,MAAM,EAAExL,gBAAiB;MACzByL,OAAO,EAAEnF,uBAAwB;MACjCsF,QAAQ,EAAEnF;IAAwB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,GACA,IAAI;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvJ,EAAA,CA5wBID,kBAAkB;EAAA,QACLtB,WAAW,EAQxBmB,cAAc;AAAA;AAAAuO,EAAA,GATdpO,kBAAkB;AA8wBxB,eAAeA,kBAAkB;AAAC,IAAAoO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}