{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\project\\\\WorkflowManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport Select from '../ui/Select';\nimport Modal from '../ui/Modal';\nimport Icon from '../AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkflowManagement = ({\n  onNext,\n  onBack,\n  initialData = {},\n  className\n}) => {\n  _s();\n  const [workflow, setWorkflow] = useState({\n    phases: [{\n      id: 'planning',\n      name: 'Planning & Analysis',\n      duration: 2,\n      tasks: [{\n        id: 'req-gathering',\n        name: 'Requirements Gathering',\n        duration: 3,\n        dependencies: [],\n        priority: 'high'\n      }, {\n        id: 'analysis',\n        name: 'System Analysis',\n        duration: 5,\n        dependencies: ['req-gathering'],\n        priority: 'high'\n      }, {\n        id: 'architecture',\n        name: 'Architecture Design',\n        duration: 4,\n        dependencies: ['analysis'],\n        priority: 'medium'\n      }]\n    }, {\n      id: 'design',\n      name: 'Design & Prototyping',\n      duration: 3,\n      tasks: [{\n        id: 'ui-design',\n        name: 'UI/UX Design',\n        duration: 7,\n        dependencies: ['architecture'],\n        priority: 'high'\n      }, {\n        id: 'prototype',\n        name: 'Interactive Prototype',\n        duration: 5,\n        dependencies: ['ui-design'],\n        priority: 'medium'\n      }, {\n        id: 'design-review',\n        name: 'Design Review',\n        duration: 2,\n        dependencies: ['prototype'],\n        priority: 'low'\n      }]\n    }, {\n      id: 'development',\n      name: 'Development',\n      duration: 8,\n      tasks: [{\n        id: 'setup',\n        name: 'Development Setup',\n        duration: 2,\n        dependencies: ['design-review'],\n        priority: 'high'\n      }, {\n        id: 'backend-dev',\n        name: 'Backend Development',\n        duration: 10,\n        dependencies: ['setup'],\n        priority: 'high'\n      }, {\n        id: 'frontend-dev',\n        name: 'Frontend Development',\n        duration: 8,\n        dependencies: ['setup'],\n        priority: 'high'\n      }, {\n        id: 'integration',\n        name: 'System Integration',\n        duration: 5,\n        dependencies: ['backend-dev', 'frontend-dev'],\n        priority: 'high'\n      }]\n    }, {\n      id: 'testing',\n      name: 'Testing & QA',\n      duration: 3,\n      tasks: [{\n        id: 'unit-testing',\n        name: 'Unit Testing',\n        duration: 4,\n        dependencies: ['integration'],\n        priority: 'high'\n      }, {\n        id: 'integration-testing',\n        name: 'Integration Testing',\n        duration: 3,\n        dependencies: ['unit-testing'],\n        priority: 'high'\n      }, {\n        id: 'user-testing',\n        name: 'User Acceptance Testing',\n        duration: 5,\n        dependencies: ['integration-testing'],\n        priority: 'medium'\n      }]\n    }, {\n      id: 'deployment',\n      name: 'Deployment & Launch',\n      duration: 2,\n      tasks: [{\n        id: 'deployment-prep',\n        name: 'Deployment Preparation',\n        duration: 3,\n        dependencies: ['user-testing'],\n        priority: 'high'\n      }, {\n        id: 'production-deploy',\n        name: 'Production Deployment',\n        duration: 1,\n        dependencies: ['deployment-prep'],\n        priority: 'urgent'\n      }, {\n        id: 'monitoring',\n        name: 'Post-Launch Monitoring',\n        duration: 7,\n        dependencies: ['production-deploy'],\n        priority: 'medium'\n      }]\n    }],\n    ...initialData\n  });\n  const [viewMode, setViewMode] = useState('gantt'); // 'gantt', 'flowchart', 'timeline'\n  const [selectedTask, setSelectedTask] = useState(null);\n  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);\n  const [draggedTask, setDraggedTask] = useState(null);\n  const [draggedPhase, setDraggedPhase] = useState(null);\n  const viewModeOptions = [{\n    value: 'gantt',\n    label: 'Gantt Chart'\n  }, {\n    value: 'flowchart',\n    label: 'Flow Diagram'\n  }, {\n    value: 'timeline',\n    label: 'Timeline View'\n  }];\n  const priorityColors = {\n    low: 'bg-blue-100 text-blue-800 border-blue-200',\n    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    high: 'bg-orange-100 text-orange-800 border-orange-200',\n    urgent: 'bg-red-100 text-red-800 border-red-200'\n  };\n  const calculateTaskPosition = (task, phaseIndex) => {\n    let startDay = 0;\n\n    // Calculate start position based on dependencies\n    if (task.dependencies.length > 0) {\n      const maxEndDay = Math.max(...task.dependencies.map(depId => {\n        // Find the dependent task across all phases\n        for (const phase of workflow.phases) {\n          const depTask = phase.tasks.find(t => t.id === depId);\n          if (depTask) {\n            return calculateTaskPosition(depTask, workflow.phases.indexOf(phase)).endDay;\n          }\n        }\n        return 0;\n      }));\n      startDay = maxEndDay;\n    } else {\n      // Calculate based on phase start\n      for (let i = 0; i < phaseIndex; i++) {\n        startDay += workflow.phases[i].duration * 7; // Convert weeks to days\n      }\n    }\n    return {\n      startDay,\n      endDay: startDay + task.duration,\n      duration: task.duration\n    };\n  };\n  const getTotalProjectDuration = () => {\n    let maxEndDay = 0;\n    workflow.phases.forEach((phase, phaseIndex) => {\n      phase.tasks.forEach(task => {\n        const position = calculateTaskPosition(task, phaseIndex);\n        maxEndDay = Math.max(maxEndDay, position.endDay);\n      });\n    });\n    return maxEndDay;\n  };\n  const handleTaskDragStart = (e, task, phaseId) => {\n    setDraggedTask({\n      task,\n      phaseId\n    });\n    e.dataTransfer.effectAllowed = 'move';\n  };\n  const handleTaskDragOver = e => {\n    e.preventDefault();\n    e.dataTransfer.dropEffect = 'move';\n  };\n  const handleTaskDrop = (e, targetPhaseId, targetIndex) => {\n    e.preventDefault();\n    if (!draggedTask) return;\n    const {\n      task,\n      phaseId: sourcePhaseId\n    } = draggedTask;\n    if (sourcePhaseId === targetPhaseId) {\n      // Reorder within same phase\n      setWorkflow(prev => ({\n        ...prev,\n        phases: prev.phases.map(phase => {\n          if (phase.id === sourcePhaseId) {\n            const tasks = [...phase.tasks];\n            const sourceIndex = tasks.findIndex(t => t.id === task.id);\n            tasks.splice(sourceIndex, 1);\n            tasks.splice(targetIndex, 0, task);\n            return {\n              ...phase,\n              tasks\n            };\n          }\n          return phase;\n        })\n      }));\n    } else {\n      // Move between phases\n      setWorkflow(prev => ({\n        ...prev,\n        phases: prev.phases.map(phase => {\n          if (phase.id === sourcePhaseId) {\n            return {\n              ...phase,\n              tasks: phase.tasks.filter(t => t.id !== task.id)\n            };\n          }\n          if (phase.id === targetPhaseId) {\n            const tasks = [...phase.tasks];\n            tasks.splice(targetIndex, 0, task);\n            return {\n              ...phase,\n              tasks\n            };\n          }\n          return phase;\n        })\n      }));\n    }\n    setDraggedTask(null);\n  };\n  const addNewTask = phaseId => {\n    const newTask = {\n      id: `task-${Date.now()}`,\n      name: 'New Task',\n      duration: 3,\n      dependencies: [],\n      priority: 'medium'\n    };\n    setWorkflow(prev => ({\n      ...prev,\n      phases: prev.phases.map(phase => phase.id === phaseId ? {\n        ...phase,\n        tasks: [...phase.tasks, newTask]\n      } : phase)\n    }));\n  };\n  const updateTask = (phaseId, taskId, updates) => {\n    setWorkflow(prev => ({\n      ...prev,\n      phases: prev.phases.map(phase => phase.id === phaseId ? {\n        ...phase,\n        tasks: phase.tasks.map(task => task.id === taskId ? {\n          ...task,\n          ...updates\n        } : task)\n      } : phase)\n    }));\n  };\n  const removeTask = (phaseId, taskId) => {\n    setWorkflow(prev => ({\n      ...prev,\n      phases: prev.phases.map(phase => phase.id === phaseId ? {\n        ...phase,\n        tasks: phase.tasks.filter(task => task.id !== taskId)\n      } : phase)\n    }));\n  };\n  const openTaskModal = (task, phaseId) => {\n    setSelectedTask({\n      ...task,\n      phaseId\n    });\n    setIsTaskModalOpen(true);\n  };\n  const renderGanttChart = () => {\n    const totalDuration = getTotalProjectDuration();\n    const dayWidth = 800 / totalDuration; // 800px total width\n\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-foreground\",\n          children: \"Gantt Chart View\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-muted-foreground\",\n          children: [\"Total Duration: \", Math.ceil(totalDuration / 7), \" weeks (\", totalDuration, \" days)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"min-w-[800px]\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-64 flex-shrink-0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 relative\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex border-b border-border pb-2\",\n                children: Array.from({\n                  length: Math.ceil(totalDuration / 7)\n                }, (_, weekIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-muted-foreground text-center\",\n                  style: {\n                    width: `${dayWidth * 7}px`\n                  },\n                  children: [\"Week \", weekIndex + 1]\n                }, weekIndex, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: workflow.phases.map((phase, phaseIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-64 flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-medium text-foreground\",\n                    children: phase.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this), phase.tasks.map(task => {\n                const position = calculateTaskPosition(task, phaseIndex);\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-64 flex-shrink-0 pr-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-muted-foreground truncate\",\n                      children: task.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 relative h-8\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: cn(\"absolute h-6 rounded cursor-pointer transition-all duration-200\", \"hover:shadow-md flex items-center px-2\", priorityColors[task.priority]),\n                      style: {\n                        left: `${position.startDay * dayWidth}px`,\n                        width: `${position.duration * dayWidth}px`\n                      },\n                      onClick: () => openTaskModal(task, phase.id),\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs font-medium truncate\",\n                        children: task.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 25\n                  }, this)]\n                }, task.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 23\n                }, this);\n              })]\n            }, phase.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFlowChart = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-foreground mb-6\",\n        children: \"Flow Diagram\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-8\",\n        children: workflow.phases.map((phase, phaseIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-4 h-4 rounded-full bg-primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-lg font-medium text-foreground\",\n              children: phase.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n            children: [phase.tasks.map((task, taskIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: cn(\"p-4 rounded-lg border-2 cursor-pointer transition-all duration-200\", \"hover:shadow-md hover:scale-105\", priorityColors[task.priority]),\n              draggable: true,\n              onDragStart: e => handleTaskDragStart(e, task, phase.id),\n              onDragOver: handleTaskDragOver,\n              onDrop: e => handleTaskDrop(e, phase.id, taskIndex),\n              onClick: () => openTaskModal(task, phase.id),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"font-medium text-sm\",\n                  children: task.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"GripVertical\",\n                  className: \"h-4 w-4 text-muted-foreground\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-muted-foreground mb-2\",\n                children: [\"Duration: \", task.duration, \" days\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 21\n              }, this), task.dependencies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-muted-foreground\",\n                children: [\"Depends on: \", task.dependencies.length, \" task(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 23\n              }, this)]\n            }, task.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 19\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => addNewTask(phase.id),\n              className: \"p-4 rounded-lg border-2 border-dashed border-border hover:border-primary transition-colors duration-200 flex items-center justify-center text-muted-foreground hover:text-primary\",\n              children: /*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Plus\",\n                className: \"h-6 w-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), phaseIndex < workflow.phases.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"ArrowDown\",\n              className: \"h-6 w-6 text-muted-foreground\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 17\n          }, this)]\n        }, phase.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this);\n  };\n  const renderTimelineView = () => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg p-6 border border-border\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-foreground mb-6\",\n        children: \"Timeline View\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute left-8 top-0 bottom-0 w-0.5 bg-border\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: workflow.phases.map((phase, phaseIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute left-6 w-4 h-4 bg-primary rounded-full border-4 border-background\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-16\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-medium text-foreground mb-2\",\n                children: phase.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-muted-foreground mb-4\",\n                children: [\"Duration: \", phase.duration, \" weeks\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: phase.tasks.map(task => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3 p-3 rounded-lg bg-secondary/30 hover:bg-secondary/50 cursor-pointer transition-colors duration-200\",\n                  onClick: () => openTaskModal(task, phase.id),\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: cn(\"w-2 h-2 rounded-full\", task.priority === 'urgent' && \"bg-red-500\", task.priority === 'high' && \"bg-orange-500\", task.priority === 'medium' && \"bg-yellow-500\", task.priority === 'low' && \"bg-blue-500\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-sm\",\n                      children: task.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-muted-foreground\",\n                      children: [task.duration, \" days \\u2022 \", task.priority, \" priority\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 25\n                  }, this), task.dependencies.length > 0 && /*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Link\",\n                    className: \"h-4 w-4 text-muted-foreground\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 27\n                  }, this)]\n                }, task.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this)]\n          }, phase.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this);\n  };\n  const handleNext = () => {\n    onNext === null || onNext === void 0 ? void 0 : onNext(workflow);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn(\"max-w-6xl mx-auto p-6 space-y-8\", className),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-3xl font-bold text-foreground\",\n        children: \"Project Workflow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-muted-foreground\",\n        children: \"Visualize and organize your project tasks and dependencies\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(Select, {\n        value: viewMode,\n        onValueChange: setViewMode,\n        options: viewModeOptions,\n        className: \"w-48\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-muted-foreground\",\n        children: [workflow.phases.reduce((total, phase) => total + phase.tasks.length, 0), \" tasks across \", workflow.phases.length, \" phases\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 7\n    }, this), viewMode === 'gantt' && renderGanttChart(), viewMode === 'flowchart' && renderFlowChart(), viewMode === 'timeline' && renderTimelineView(), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center pt-6\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outline\",\n        onClick: onBack,\n        iconName: \"ArrowLeft\",\n        iconPosition: \"left\",\n        children: \"Back to Tech Stack\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-muted-foreground\",\n          children: \"Step 4 of 6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          iconName: \"ArrowRight\",\n          iconPosition: \"right\",\n          children: \"Continue to Tasks\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      isOpen: isTaskModalOpen,\n      onClose: () => setIsTaskModalOpen(false),\n      title: \"Task Details\",\n      size: \"lg\",\n      children: selectedTask && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            label: \"Task Name\",\n            value: selectedTask.name,\n            onChange: e => setSelectedTask(prev => ({\n              ...prev,\n              name: e.target.value\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Duration (days)\",\n            type: \"number\",\n            value: selectedTask.duration,\n            onChange: e => setSelectedTask(prev => ({\n              ...prev,\n              duration: parseInt(e.target.value) || 1\n            })),\n            min: \"1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          label: \"Priority\",\n          value: selectedTask.priority,\n          onValueChange: value => setSelectedTask(prev => ({\n            ...prev,\n            priority: value\n          })),\n          options: [{\n            value: 'low',\n            label: 'Low Priority'\n          }, {\n            value: 'medium',\n            label: 'Medium Priority'\n          }, {\n            value: 'high',\n            label: 'High Priority'\n          }, {\n            value: 'urgent',\n            label: 'Urgent Priority'\n          }]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"text-sm font-medium text-foreground mb-2 block\",\n            children: \"Dependencies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: workflow.phases.map(phase => phase.tasks.filter(task => task.id !== selectedTask.id).map(task => /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: selectedTask.dependencies.includes(task.id),\n                onChange: e => {\n                  const isChecked = e.target.checked;\n                  setSelectedTask(prev => ({\n                    ...prev,\n                    dependencies: isChecked ? [...prev.dependencies, task.id] : prev.dependencies.filter(id => id !== task.id)\n                  }));\n                },\n                className: \"rounded border-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: task.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 25\n              }, this)]\n            }, task.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 23\n            }, this)))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"destructive\",\n            onClick: () => {\n              removeTask(selectedTask.phaseId, selectedTask.id);\n              setIsTaskModalOpen(false);\n            },\n            iconName: \"Trash2\",\n            iconPosition: \"left\",\n            children: \"Delete Task\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              onClick: () => setIsTaskModalOpen(false),\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => {\n                updateTask(selectedTask.phaseId, selectedTask.id, {\n                  name: selectedTask.name,\n                  duration: selectedTask.duration,\n                  priority: selectedTask.priority,\n                  dependencies: selectedTask.dependencies\n                });\n                setIsTaskModalOpen(false);\n              },\n              children: \"Save Changes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 450,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkflowManagement, \"Pd+MYi6vEStzrIEVEPM0EF+CSoE=\");\n_c = WorkflowManagement;\nexport default WorkflowManagement;\nvar _c;\n$RefreshReg$(_c, \"WorkflowManagement\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "cn", "<PERSON><PERSON>", "Input", "Select", "Modal", "Icon", "jsxDEV", "_jsxDEV", "WorkflowManagement", "onNext", "onBack", "initialData", "className", "_s", "workflow", "setWorkflow", "phases", "id", "name", "duration", "tasks", "dependencies", "priority", "viewMode", "setViewMode", "selectedTask", "setSelectedTask", "isTaskModalOpen", "setIsTaskModalOpen", "draggedTask", "setDraggedTask", "draggedPhase", "setDraggedPhase", "viewModeOptions", "value", "label", "priorityColors", "low", "medium", "high", "urgent", "calculateTaskPosition", "task", "phaseIndex", "startDay", "length", "maxEndDay", "Math", "max", "map", "depId", "phase", "depTask", "find", "t", "indexOf", "endDay", "i", "getTotalProjectDuration", "for<PERSON>ach", "position", "handleTaskDragStart", "e", "phaseId", "dataTransfer", "effectAllowed", "handleTaskDragOver", "preventDefault", "dropEffect", "handleTaskDrop", "targetPhaseId", "targetIndex", "sourcePhaseId", "prev", "sourceIndex", "findIndex", "splice", "filter", "addNewTask", "newTask", "Date", "now", "updateTask", "taskId", "updates", "removeTask", "openTaskModal", "renderGanttChart", "totalDuration", "dayWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ceil", "Array", "from", "_", "weekIndex", "style", "width", "left", "onClick", "render<PERSON>low<PERSON>hart", "taskIndex", "draggable", "onDragStart", "onDragOver", "onDrop", "renderTimelineView", "handleNext", "onValueChange", "options", "reduce", "total", "variant", "iconName", "iconPosition", "isOpen", "onClose", "title", "size", "onChange", "target", "type", "parseInt", "min", "checked", "includes", "isChecked", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/project/WorkflowManagement.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { cn } from '../../utils/cn';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport Select from '../ui/Select';\nimport Modal from '../ui/Modal';\nimport Icon from '../AppIcon';\n\nconst WorkflowManagement = ({ \n  onNext, \n  onBack, \n  initialData = {}, \n  className \n}) => {\n  const [workflow, setWorkflow] = useState({\n    phases: [\n      {\n        id: 'planning',\n        name: 'Planning & Analysis',\n        duration: 2,\n        tasks: [\n          { id: 'req-gathering', name: 'Requirements Gathering', duration: 3, dependencies: [], priority: 'high' },\n          { id: 'analysis', name: 'System Analysis', duration: 5, dependencies: ['req-gathering'], priority: 'high' },\n          { id: 'architecture', name: 'Architecture Design', duration: 4, dependencies: ['analysis'], priority: 'medium' }\n        ]\n      },\n      {\n        id: 'design',\n        name: 'Design & Prototyping',\n        duration: 3,\n        tasks: [\n          { id: 'ui-design', name: 'UI/UX Design', duration: 7, dependencies: ['architecture'], priority: 'high' },\n          { id: 'prototype', name: 'Interactive Prototype', duration: 5, dependencies: ['ui-design'], priority: 'medium' },\n          { id: 'design-review', name: 'Design Review', duration: 2, dependencies: ['prototype'], priority: 'low' }\n        ]\n      },\n      {\n        id: 'development',\n        name: 'Development',\n        duration: 8,\n        tasks: [\n          { id: 'setup', name: 'Development Setup', duration: 2, dependencies: ['design-review'], priority: 'high' },\n          { id: 'backend-dev', name: 'Backend Development', duration: 10, dependencies: ['setup'], priority: 'high' },\n          { id: 'frontend-dev', name: 'Frontend Development', duration: 8, dependencies: ['setup'], priority: 'high' },\n          { id: 'integration', name: 'System Integration', duration: 5, dependencies: ['backend-dev', 'frontend-dev'], priority: 'high' }\n        ]\n      },\n      {\n        id: 'testing',\n        name: 'Testing & QA',\n        duration: 3,\n        tasks: [\n          { id: 'unit-testing', name: 'Unit Testing', duration: 4, dependencies: ['integration'], priority: 'high' },\n          { id: 'integration-testing', name: 'Integration Testing', duration: 3, dependencies: ['unit-testing'], priority: 'high' },\n          { id: 'user-testing', name: 'User Acceptance Testing', duration: 5, dependencies: ['integration-testing'], priority: 'medium' }\n        ]\n      },\n      {\n        id: 'deployment',\n        name: 'Deployment & Launch',\n        duration: 2,\n        tasks: [\n          { id: 'deployment-prep', name: 'Deployment Preparation', duration: 3, dependencies: ['user-testing'], priority: 'high' },\n          { id: 'production-deploy', name: 'Production Deployment', duration: 1, dependencies: ['deployment-prep'], priority: 'urgent' },\n          { id: 'monitoring', name: 'Post-Launch Monitoring', duration: 7, dependencies: ['production-deploy'], priority: 'medium' }\n        ]\n      }\n    ],\n    ...initialData\n  });\n\n  const [viewMode, setViewMode] = useState('gantt'); // 'gantt', 'flowchart', 'timeline'\n  const [selectedTask, setSelectedTask] = useState(null);\n  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);\n  const [draggedTask, setDraggedTask] = useState(null);\n  const [draggedPhase, setDraggedPhase] = useState(null);\n\n  const viewModeOptions = [\n    { value: 'gantt', label: 'Gantt Chart' },\n    { value: 'flowchart', label: 'Flow Diagram' },\n    { value: 'timeline', label: 'Timeline View' }\n  ];\n\n  const priorityColors = {\n    low: 'bg-blue-100 text-blue-800 border-blue-200',\n    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    high: 'bg-orange-100 text-orange-800 border-orange-200',\n    urgent: 'bg-red-100 text-red-800 border-red-200'\n  };\n\n  const calculateTaskPosition = (task, phaseIndex) => {\n    let startDay = 0;\n    \n    // Calculate start position based on dependencies\n    if (task.dependencies.length > 0) {\n      const maxEndDay = Math.max(...task.dependencies.map(depId => {\n        // Find the dependent task across all phases\n        for (const phase of workflow.phases) {\n          const depTask = phase.tasks.find(t => t.id === depId);\n          if (depTask) {\n            return calculateTaskPosition(depTask, workflow.phases.indexOf(phase)).endDay;\n          }\n        }\n        return 0;\n      }));\n      startDay = maxEndDay;\n    } else {\n      // Calculate based on phase start\n      for (let i = 0; i < phaseIndex; i++) {\n        startDay += workflow.phases[i].duration * 7; // Convert weeks to days\n      }\n    }\n\n    return {\n      startDay,\n      endDay: startDay + task.duration,\n      duration: task.duration\n    };\n  };\n\n  const getTotalProjectDuration = () => {\n    let maxEndDay = 0;\n    workflow.phases.forEach((phase, phaseIndex) => {\n      phase.tasks.forEach(task => {\n        const position = calculateTaskPosition(task, phaseIndex);\n        maxEndDay = Math.max(maxEndDay, position.endDay);\n      });\n    });\n    return maxEndDay;\n  };\n\n  const handleTaskDragStart = (e, task, phaseId) => {\n    setDraggedTask({ task, phaseId });\n    e.dataTransfer.effectAllowed = 'move';\n  };\n\n  const handleTaskDragOver = (e) => {\n    e.preventDefault();\n    e.dataTransfer.dropEffect = 'move';\n  };\n\n  const handleTaskDrop = (e, targetPhaseId, targetIndex) => {\n    e.preventDefault();\n    \n    if (!draggedTask) return;\n\n    const { task, phaseId: sourcePhaseId } = draggedTask;\n    \n    if (sourcePhaseId === targetPhaseId) {\n      // Reorder within same phase\n      setWorkflow(prev => ({\n        ...prev,\n        phases: prev.phases.map(phase => {\n          if (phase.id === sourcePhaseId) {\n            const tasks = [...phase.tasks];\n            const sourceIndex = tasks.findIndex(t => t.id === task.id);\n            tasks.splice(sourceIndex, 1);\n            tasks.splice(targetIndex, 0, task);\n            return { ...phase, tasks };\n          }\n          return phase;\n        })\n      }));\n    } else {\n      // Move between phases\n      setWorkflow(prev => ({\n        ...prev,\n        phases: prev.phases.map(phase => {\n          if (phase.id === sourcePhaseId) {\n            return {\n              ...phase,\n              tasks: phase.tasks.filter(t => t.id !== task.id)\n            };\n          }\n          if (phase.id === targetPhaseId) {\n            const tasks = [...phase.tasks];\n            tasks.splice(targetIndex, 0, task);\n            return { ...phase, tasks };\n          }\n          return phase;\n        })\n      }));\n    }\n    \n    setDraggedTask(null);\n  };\n\n  const addNewTask = (phaseId) => {\n    const newTask = {\n      id: `task-${Date.now()}`,\n      name: 'New Task',\n      duration: 3,\n      dependencies: [],\n      priority: 'medium'\n    };\n\n    setWorkflow(prev => ({\n      ...prev,\n      phases: prev.phases.map(phase => \n        phase.id === phaseId \n          ? { ...phase, tasks: [...phase.tasks, newTask] }\n          : phase\n      )\n    }));\n  };\n\n  const updateTask = (phaseId, taskId, updates) => {\n    setWorkflow(prev => ({\n      ...prev,\n      phases: prev.phases.map(phase => \n        phase.id === phaseId \n          ? {\n              ...phase,\n              tasks: phase.tasks.map(task => \n                task.id === taskId ? { ...task, ...updates } : task\n              )\n            }\n          : phase\n      )\n    }));\n  };\n\n  const removeTask = (phaseId, taskId) => {\n    setWorkflow(prev => ({\n      ...prev,\n      phases: prev.phases.map(phase => \n        phase.id === phaseId \n          ? { ...phase, tasks: phase.tasks.filter(task => task.id !== taskId) }\n          : phase\n      )\n    }));\n  };\n\n  const openTaskModal = (task, phaseId) => {\n    setSelectedTask({ ...task, phaseId });\n    setIsTaskModalOpen(true);\n  };\n\n  const renderGanttChart = () => {\n    const totalDuration = getTotalProjectDuration();\n    const dayWidth = 800 / totalDuration; // 800px total width\n\n    return (\n      <div className=\"bg-card rounded-lg p-6 border border-border\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-foreground\">Gantt Chart View</h3>\n          <div className=\"text-sm text-muted-foreground\">\n            Total Duration: {Math.ceil(totalDuration / 7)} weeks ({totalDuration} days)\n          </div>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <div className=\"min-w-[800px]\">\n            {/* Timeline header */}\n            <div className=\"flex mb-4\">\n              <div className=\"w-64 flex-shrink-0\"></div>\n              <div className=\"flex-1 relative\">\n                <div className=\"flex border-b border-border pb-2\">\n                  {Array.from({ length: Math.ceil(totalDuration / 7) }, (_, weekIndex) => (\n                    <div \n                      key={weekIndex}\n                      className=\"text-xs text-muted-foreground text-center\"\n                      style={{ width: `${dayWidth * 7}px` }}\n                    >\n                      Week {weekIndex + 1}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Tasks */}\n            <div className=\"space-y-2\">\n              {workflow.phases.map((phase, phaseIndex) => (\n                <div key={phase.id}>\n                  <div className=\"flex items-center mb-2\">\n                    <div className=\"w-64 flex-shrink-0\">\n                      <h4 className=\"font-medium text-foreground\">{phase.name}</h4>\n                    </div>\n                  </div>\n                  \n                  {phase.tasks.map((task) => {\n                    const position = calculateTaskPosition(task, phaseIndex);\n                    \n                    return (\n                      <div key={task.id} className=\"flex items-center mb-1\">\n                        <div className=\"w-64 flex-shrink-0 pr-4\">\n                          <div className=\"text-sm text-muted-foreground truncate\">\n                            {task.name}\n                          </div>\n                        </div>\n                        <div className=\"flex-1 relative h-8\">\n                          <div\n                            className={cn(\n                              \"absolute h-6 rounded cursor-pointer transition-all duration-200\",\n                              \"hover:shadow-md flex items-center px-2\",\n                              priorityColors[task.priority]\n                            )}\n                            style={{\n                              left: `${position.startDay * dayWidth}px`,\n                              width: `${position.duration * dayWidth}px`\n                            }}\n                            onClick={() => openTaskModal(task, phase.id)}\n                          >\n                            <span className=\"text-xs font-medium truncate\">\n                              {task.name}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const renderFlowChart = () => {\n    return (\n      <div className=\"bg-card rounded-lg p-6 border border-border\">\n        <h3 className=\"text-lg font-semibold text-foreground mb-6\">Flow Diagram</h3>\n        \n        <div className=\"space-y-8\">\n          {workflow.phases.map((phase, phaseIndex) => (\n            <div key={phase.id} className=\"space-y-4\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-4 h-4 rounded-full bg-primary\"></div>\n                <h4 className=\"text-lg font-medium text-foreground\">{phase.name}</h4>\n              </div>\n              \n              <div className=\"ml-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                {phase.tasks.map((task, taskIndex) => (\n                  <div\n                    key={task.id}\n                    className={cn(\n                      \"p-4 rounded-lg border-2 cursor-pointer transition-all duration-200\",\n                      \"hover:shadow-md hover:scale-105\",\n                      priorityColors[task.priority]\n                    )}\n                    draggable\n                    onDragStart={(e) => handleTaskDragStart(e, task, phase.id)}\n                    onDragOver={handleTaskDragOver}\n                    onDrop={(e) => handleTaskDrop(e, phase.id, taskIndex)}\n                    onClick={() => openTaskModal(task, phase.id)}\n                  >\n                    <div className=\"flex items-start justify-between mb-2\">\n                      <h5 className=\"font-medium text-sm\">{task.name}</h5>\n                      <Icon name=\"GripVertical\" className=\"h-4 w-4 text-muted-foreground\" />\n                    </div>\n                    \n                    <div className=\"text-xs text-muted-foreground mb-2\">\n                      Duration: {task.duration} days\n                    </div>\n                    \n                    {task.dependencies.length > 0 && (\n                      <div className=\"text-xs text-muted-foreground\">\n                        Depends on: {task.dependencies.length} task(s)\n                      </div>\n                    )}\n                  </div>\n                ))}\n                \n                <button\n                  onClick={() => addNewTask(phase.id)}\n                  className=\"p-4 rounded-lg border-2 border-dashed border-border hover:border-primary transition-colors duration-200 flex items-center justify-center text-muted-foreground hover:text-primary\"\n                >\n                  <Icon name=\"Plus\" className=\"h-6 w-6\" />\n                </button>\n              </div>\n              \n              {phaseIndex < workflow.phases.length - 1 && (\n                <div className=\"flex justify-center\">\n                  <Icon name=\"ArrowDown\" className=\"h-6 w-6 text-muted-foreground\" />\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  };\n\n  const renderTimelineView = () => {\n    return (\n      <div className=\"bg-card rounded-lg p-6 border border-border\">\n        <h3 className=\"text-lg font-semibold text-foreground mb-6\">Timeline View</h3>\n        \n        <div className=\"relative\">\n          {/* Timeline line */}\n          <div className=\"absolute left-8 top-0 bottom-0 w-0.5 bg-border\"></div>\n          \n          <div className=\"space-y-8\">\n            {workflow.phases.map((phase, phaseIndex) => (\n              <div key={phase.id} className=\"relative\">\n                {/* Phase marker */}\n                <div className=\"absolute left-6 w-4 h-4 bg-primary rounded-full border-4 border-background\"></div>\n                \n                <div className=\"ml-16\">\n                  <h4 className=\"text-lg font-medium text-foreground mb-2\">{phase.name}</h4>\n                  <div className=\"text-sm text-muted-foreground mb-4\">\n                    Duration: {phase.duration} weeks\n                  </div>\n                  \n                  <div className=\"space-y-2\">\n                    {phase.tasks.map((task) => (\n                      <div\n                        key={task.id}\n                        className=\"flex items-center gap-3 p-3 rounded-lg bg-secondary/30 hover:bg-secondary/50 cursor-pointer transition-colors duration-200\"\n                        onClick={() => openTaskModal(task, phase.id)}\n                      >\n                        <div className={cn(\n                          \"w-2 h-2 rounded-full\",\n                          task.priority === 'urgent' && \"bg-red-500\",\n                          task.priority === 'high' && \"bg-orange-500\",\n                          task.priority === 'medium' && \"bg-yellow-500\",\n                          task.priority === 'low' && \"bg-blue-500\"\n                        )}></div>\n                        \n                        <div className=\"flex-1\">\n                          <div className=\"font-medium text-sm\">{task.name}</div>\n                          <div className=\"text-xs text-muted-foreground\">\n                            {task.duration} days • {task.priority} priority\n                          </div>\n                        </div>\n                        \n                        {task.dependencies.length > 0 && (\n                          <Icon name=\"Link\" className=\"h-4 w-4 text-muted-foreground\" />\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const handleNext = () => {\n    onNext?.(workflow);\n  };\n\n  return (\n    <div className={cn(\"max-w-6xl mx-auto p-6 space-y-8\", className)}>\n      {/* Header */}\n      <div className=\"text-center space-y-2\">\n        <h2 className=\"text-3xl font-bold text-foreground\">Project Workflow</h2>\n        <p className=\"text-muted-foreground\">\n          Visualize and organize your project tasks and dependencies\n        </p>\n      </div>\n\n      {/* View Mode Selector */}\n      <div className=\"flex items-center justify-between\">\n        <Select\n          value={viewMode}\n          onValueChange={setViewMode}\n          options={viewModeOptions}\n          className=\"w-48\"\n        />\n        \n        <div className=\"text-sm text-muted-foreground\">\n          {workflow.phases.reduce((total, phase) => total + phase.tasks.length, 0)} tasks across {workflow.phases.length} phases\n        </div>\n      </div>\n\n      {/* Workflow Visualization */}\n      {viewMode === 'gantt' && renderGanttChart()}\n      {viewMode === 'flowchart' && renderFlowChart()}\n      {viewMode === 'timeline' && renderTimelineView()}\n\n      {/* Navigation */}\n      <div className=\"flex justify-between items-center pt-6\">\n        <Button\n          variant=\"outline\"\n          onClick={onBack}\n          iconName=\"ArrowLeft\"\n          iconPosition=\"left\"\n        >\n          Back to Tech Stack\n        </Button>\n\n        <div className=\"flex items-center gap-2\">\n          <div className=\"text-sm text-muted-foreground\">\n            Step 4 of 6\n          </div>\n          <Button\n            onClick={handleNext}\n            iconName=\"ArrowRight\"\n            iconPosition=\"right\"\n          >\n            Continue to Tasks\n          </Button>\n        </div>\n      </div>\n\n      {/* Task Details Modal */}\n      <Modal\n        isOpen={isTaskModalOpen}\n        onClose={() => setIsTaskModalOpen(false)}\n        title=\"Task Details\"\n        size=\"lg\"\n      >\n        {selectedTask && (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <Input\n                label=\"Task Name\"\n                value={selectedTask.name}\n                onChange={(e) => setSelectedTask(prev => ({ ...prev, name: e.target.value }))}\n              />\n\n              <Input\n                label=\"Duration (days)\"\n                type=\"number\"\n                value={selectedTask.duration}\n                onChange={(e) => setSelectedTask(prev => ({ ...prev, duration: parseInt(e.target.value) || 1 }))}\n                min=\"1\"\n              />\n            </div>\n\n            <Select\n              label=\"Priority\"\n              value={selectedTask.priority}\n              onValueChange={(value) => setSelectedTask(prev => ({ ...prev, priority: value }))}\n              options={[\n                { value: 'low', label: 'Low Priority' },\n                { value: 'medium', label: 'Medium Priority' },\n                { value: 'high', label: 'High Priority' },\n                { value: 'urgent', label: 'Urgent Priority' }\n              ]}\n            />\n\n            <div>\n              <label className=\"text-sm font-medium text-foreground mb-2 block\">\n                Dependencies\n              </label>\n              <div className=\"space-y-2\">\n                {workflow.phases.map(phase =>\n                  phase.tasks\n                    .filter(task => task.id !== selectedTask.id)\n                    .map(task => (\n                      <label key={task.id} className=\"flex items-center gap-2\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedTask.dependencies.includes(task.id)}\n                          onChange={(e) => {\n                            const isChecked = e.target.checked;\n                            setSelectedTask(prev => ({\n                              ...prev,\n                              dependencies: isChecked\n                                ? [...prev.dependencies, task.id]\n                                : prev.dependencies.filter(id => id !== task.id)\n                            }));\n                          }}\n                          className=\"rounded border-input\"\n                        />\n                        <span className=\"text-sm\">{task.name}</span>\n                      </label>\n                    ))\n                )}\n              </div>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <Button\n                variant=\"destructive\"\n                onClick={() => {\n                  removeTask(selectedTask.phaseId, selectedTask.id);\n                  setIsTaskModalOpen(false);\n                }}\n                iconName=\"Trash2\"\n                iconPosition=\"left\"\n              >\n                Delete Task\n              </Button>\n\n              <div className=\"flex gap-2\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setIsTaskModalOpen(false)}\n                >\n                  Cancel\n                </Button>\n                <Button\n                  onClick={() => {\n                    updateTask(selectedTask.phaseId, selectedTask.id, {\n                      name: selectedTask.name,\n                      duration: selectedTask.duration,\n                      priority: selectedTask.priority,\n                      dependencies: selectedTask.dependencies\n                    });\n                    setIsTaskModalOpen(false);\n                  }}\n                >\n                  Save Changes\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default WorkflowManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,EAAE,QAAQ,gBAAgB;AACnC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,MAAM;EACNC,MAAM;EACNC,WAAW,GAAG,CAAC,CAAC;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACvCmB,MAAM,EAAE,CACN;MACEC,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,qBAAqB;MAC3BC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,CACL;QAAEH,EAAE,EAAE,eAAe;QAAEC,IAAI,EAAE,wBAAwB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACxG;QAAEL,EAAE,EAAE,UAAU;QAAEC,IAAI,EAAE,iBAAiB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,eAAe,CAAC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAC3G;QAAEL,EAAE,EAAE,cAAc;QAAEC,IAAI,EAAE,qBAAqB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,UAAU,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAEpH,CAAC,EACD;MACEL,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,sBAAsB;MAC5BC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,CACL;QAAEH,EAAE,EAAE,WAAW;QAAEC,IAAI,EAAE,cAAc;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACxG;QAAEL,EAAE,EAAE,WAAW;QAAEC,IAAI,EAAE,uBAAuB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,WAAW,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,EAChH;QAAEL,EAAE,EAAE,eAAe;QAAEC,IAAI,EAAE,eAAe;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,WAAW,CAAC;QAAEC,QAAQ,EAAE;MAAM,CAAC;IAE7G,CAAC,EACD;MACEL,EAAE,EAAE,aAAa;MACjBC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,CACL;QAAEH,EAAE,EAAE,OAAO;QAAEC,IAAI,EAAE,mBAAmB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,eAAe,CAAC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAC1G;QAAEL,EAAE,EAAE,aAAa;QAAEC,IAAI,EAAE,qBAAqB;QAAEC,QAAQ,EAAE,EAAE;QAAEE,YAAY,EAAE,CAAC,OAAO,CAAC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAC3G;QAAEL,EAAE,EAAE,cAAc;QAAEC,IAAI,EAAE,sBAAsB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,OAAO,CAAC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAC5G;QAAEL,EAAE,EAAE,aAAa;QAAEC,IAAI,EAAE,oBAAoB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,aAAa,EAAE,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAO,CAAC;IAEnI,CAAC,EACD;MACEL,EAAE,EAAE,SAAS;MACbC,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,CACL;QAAEH,EAAE,EAAE,cAAc;QAAEC,IAAI,EAAE,cAAc;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,aAAa,CAAC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EAC1G;QAAEL,EAAE,EAAE,qBAAqB;QAAEC,IAAI,EAAE,qBAAqB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACzH;QAAEL,EAAE,EAAE,cAAc;QAAEC,IAAI,EAAE,yBAAyB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,qBAAqB,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAEnI,CAAC,EACD;MACEL,EAAE,EAAE,YAAY;MAChBC,IAAI,EAAE,qBAAqB;MAC3BC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,CACL;QAAEH,EAAE,EAAE,iBAAiB;QAAEC,IAAI,EAAE,wBAAwB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAO,CAAC,EACxH;QAAEL,EAAE,EAAE,mBAAmB;QAAEC,IAAI,EAAE,uBAAuB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,iBAAiB,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,EAC9H;QAAEL,EAAE,EAAE,YAAY;QAAEC,IAAI,EAAE,wBAAwB;QAAEC,QAAQ,EAAE,CAAC;QAAEE,YAAY,EAAE,CAAC,mBAAmB,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAE9H,CAAC,CACF;IACD,GAAGX;EACL,CAAC,CAAC;EAEF,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMoC,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAc,CAAC,EACxC;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC7C;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAgB,CAAC,CAC9C;EAED,MAAMC,cAAc,GAAG;IACrBC,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,iDAAiD;IACzDC,IAAI,EAAE,iDAAiD;IACvDC,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,qBAAqB,GAAGA,CAACC,IAAI,EAAEC,UAAU,KAAK;IAClD,IAAIC,QAAQ,GAAG,CAAC;;IAEhB;IACA,IAAIF,IAAI,CAACrB,YAAY,CAACwB,MAAM,GAAG,CAAC,EAAE;MAChC,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGN,IAAI,CAACrB,YAAY,CAAC4B,GAAG,CAACC,KAAK,IAAI;QAC3D;QACA,KAAK,MAAMC,KAAK,IAAIrC,QAAQ,CAACE,MAAM,EAAE;UACnC,MAAMoC,OAAO,GAAGD,KAAK,CAAC/B,KAAK,CAACiC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrC,EAAE,KAAKiC,KAAK,CAAC;UACrD,IAAIE,OAAO,EAAE;YACX,OAAOX,qBAAqB,CAACW,OAAO,EAAEtC,QAAQ,CAACE,MAAM,CAACuC,OAAO,CAACJ,KAAK,CAAC,CAAC,CAACK,MAAM;UAC9E;QACF;QACA,OAAO,CAAC;MACV,CAAC,CAAC,CAAC;MACHZ,QAAQ,GAAGE,SAAS;IACtB,CAAC,MAAM;MACL;MACA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,UAAU,EAAEc,CAAC,EAAE,EAAE;QACnCb,QAAQ,IAAI9B,QAAQ,CAACE,MAAM,CAACyC,CAAC,CAAC,CAACtC,QAAQ,GAAG,CAAC,CAAC,CAAC;MAC/C;IACF;IAEA,OAAO;MACLyB,QAAQ;MACRY,MAAM,EAAEZ,QAAQ,GAAGF,IAAI,CAACvB,QAAQ;MAChCA,QAAQ,EAAEuB,IAAI,CAACvB;IACjB,CAAC;EACH,CAAC;EAED,MAAMuC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAIZ,SAAS,GAAG,CAAC;IACjBhC,QAAQ,CAACE,MAAM,CAAC2C,OAAO,CAAC,CAACR,KAAK,EAAER,UAAU,KAAK;MAC7CQ,KAAK,CAAC/B,KAAK,CAACuC,OAAO,CAACjB,IAAI,IAAI;QAC1B,MAAMkB,QAAQ,GAAGnB,qBAAqB,CAACC,IAAI,EAAEC,UAAU,CAAC;QACxDG,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACF,SAAS,EAAEc,QAAQ,CAACJ,MAAM,CAAC;MAClD,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOV,SAAS;EAClB,CAAC;EAED,MAAMe,mBAAmB,GAAGA,CAACC,CAAC,EAAEpB,IAAI,EAAEqB,OAAO,KAAK;IAChDjC,cAAc,CAAC;MAAEY,IAAI;MAAEqB;IAAQ,CAAC,CAAC;IACjCD,CAAC,CAACE,YAAY,CAACC,aAAa,GAAG,MAAM;EACvC,CAAC;EAED,MAAMC,kBAAkB,GAAIJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBL,CAAC,CAACE,YAAY,CAACI,UAAU,GAAG,MAAM;EACpC,CAAC;EAED,MAAMC,cAAc,GAAGA,CAACP,CAAC,EAAEQ,aAAa,EAAEC,WAAW,KAAK;IACxDT,CAAC,CAACK,cAAc,CAAC,CAAC;IAElB,IAAI,CAACtC,WAAW,EAAE;IAElB,MAAM;MAAEa,IAAI;MAAEqB,OAAO,EAAES;IAAc,CAAC,GAAG3C,WAAW;IAEpD,IAAI2C,aAAa,KAAKF,aAAa,EAAE;MACnC;MACAvD,WAAW,CAAC0D,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPzD,MAAM,EAAEyD,IAAI,CAACzD,MAAM,CAACiC,GAAG,CAACE,KAAK,IAAI;UAC/B,IAAIA,KAAK,CAAClC,EAAE,KAAKuD,aAAa,EAAE;YAC9B,MAAMpD,KAAK,GAAG,CAAC,GAAG+B,KAAK,CAAC/B,KAAK,CAAC;YAC9B,MAAMsD,WAAW,GAAGtD,KAAK,CAACuD,SAAS,CAACrB,CAAC,IAAIA,CAAC,CAACrC,EAAE,KAAKyB,IAAI,CAACzB,EAAE,CAAC;YAC1DG,KAAK,CAACwD,MAAM,CAACF,WAAW,EAAE,CAAC,CAAC;YAC5BtD,KAAK,CAACwD,MAAM,CAACL,WAAW,EAAE,CAAC,EAAE7B,IAAI,CAAC;YAClC,OAAO;cAAE,GAAGS,KAAK;cAAE/B;YAAM,CAAC;UAC5B;UACA,OAAO+B,KAAK;QACd,CAAC;MACH,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACApC,WAAW,CAAC0D,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPzD,MAAM,EAAEyD,IAAI,CAACzD,MAAM,CAACiC,GAAG,CAACE,KAAK,IAAI;UAC/B,IAAIA,KAAK,CAAClC,EAAE,KAAKuD,aAAa,EAAE;YAC9B,OAAO;cACL,GAAGrB,KAAK;cACR/B,KAAK,EAAE+B,KAAK,CAAC/B,KAAK,CAACyD,MAAM,CAACvB,CAAC,IAAIA,CAAC,CAACrC,EAAE,KAAKyB,IAAI,CAACzB,EAAE;YACjD,CAAC;UACH;UACA,IAAIkC,KAAK,CAAClC,EAAE,KAAKqD,aAAa,EAAE;YAC9B,MAAMlD,KAAK,GAAG,CAAC,GAAG+B,KAAK,CAAC/B,KAAK,CAAC;YAC9BA,KAAK,CAACwD,MAAM,CAACL,WAAW,EAAE,CAAC,EAAE7B,IAAI,CAAC;YAClC,OAAO;cAAE,GAAGS,KAAK;cAAE/B;YAAM,CAAC;UAC5B;UACA,OAAO+B,KAAK;QACd,CAAC;MACH,CAAC,CAAC,CAAC;IACL;IAEArB,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMgD,UAAU,GAAIf,OAAO,IAAK;IAC9B,MAAMgB,OAAO,GAAG;MACd9D,EAAE,EAAE,QAAQ+D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACxB/D,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE,CAAC;MACXE,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE;IACZ,CAAC;IAEDP,WAAW,CAAC0D,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPzD,MAAM,EAAEyD,IAAI,CAACzD,MAAM,CAACiC,GAAG,CAACE,KAAK,IAC3BA,KAAK,CAAClC,EAAE,KAAK8C,OAAO,GAChB;QAAE,GAAGZ,KAAK;QAAE/B,KAAK,EAAE,CAAC,GAAG+B,KAAK,CAAC/B,KAAK,EAAE2D,OAAO;MAAE,CAAC,GAC9C5B,KACN;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM+B,UAAU,GAAGA,CAACnB,OAAO,EAAEoB,MAAM,EAAEC,OAAO,KAAK;IAC/CrE,WAAW,CAAC0D,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPzD,MAAM,EAAEyD,IAAI,CAACzD,MAAM,CAACiC,GAAG,CAACE,KAAK,IAC3BA,KAAK,CAAClC,EAAE,KAAK8C,OAAO,GAChB;QACE,GAAGZ,KAAK;QACR/B,KAAK,EAAE+B,KAAK,CAAC/B,KAAK,CAAC6B,GAAG,CAACP,IAAI,IACzBA,IAAI,CAACzB,EAAE,KAAKkE,MAAM,GAAG;UAAE,GAAGzC,IAAI;UAAE,GAAG0C;QAAQ,CAAC,GAAG1C,IACjD;MACF,CAAC,GACDS,KACN;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMkC,UAAU,GAAGA,CAACtB,OAAO,EAAEoB,MAAM,KAAK;IACtCpE,WAAW,CAAC0D,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPzD,MAAM,EAAEyD,IAAI,CAACzD,MAAM,CAACiC,GAAG,CAACE,KAAK,IAC3BA,KAAK,CAAClC,EAAE,KAAK8C,OAAO,GAChB;QAAE,GAAGZ,KAAK;QAAE/B,KAAK,EAAE+B,KAAK,CAAC/B,KAAK,CAACyD,MAAM,CAACnC,IAAI,IAAIA,IAAI,CAACzB,EAAE,KAAKkE,MAAM;MAAE,CAAC,GACnEhC,KACN;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMmC,aAAa,GAAGA,CAAC5C,IAAI,EAAEqB,OAAO,KAAK;IACvCrC,eAAe,CAAC;MAAE,GAAGgB,IAAI;MAAEqB;IAAQ,CAAC,CAAC;IACrCnC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM2D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,aAAa,GAAG9B,uBAAuB,CAAC,CAAC;IAC/C,MAAM+B,QAAQ,GAAG,GAAG,GAAGD,aAAa,CAAC,CAAC;;IAEtC,oBACEjF,OAAA;MAAKK,SAAS,EAAC,6CAA6C;MAAA8E,QAAA,gBAC1DnF,OAAA;QAAKK,SAAS,EAAC,wCAAwC;QAAA8E,QAAA,gBACrDnF,OAAA;UAAIK,SAAS,EAAC,uCAAuC;UAAA8E,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EvF,OAAA;UAAKK,SAAS,EAAC,+BAA+B;UAAA8E,QAAA,GAAC,kBAC7B,EAAC3C,IAAI,CAACgD,IAAI,CAACP,aAAa,GAAG,CAAC,CAAC,EAAC,UAAQ,EAACA,aAAa,EAAC,QACvE;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvF,OAAA;QAAKK,SAAS,EAAC,iBAAiB;QAAA8E,QAAA,eAC9BnF,OAAA;UAAKK,SAAS,EAAC,eAAe;UAAA8E,QAAA,gBAE5BnF,OAAA;YAAKK,SAAS,EAAC,WAAW;YAAA8E,QAAA,gBACxBnF,OAAA;cAAKK,SAAS,EAAC;YAAoB;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1CvF,OAAA;cAAKK,SAAS,EAAC,iBAAiB;cAAA8E,QAAA,eAC9BnF,OAAA;gBAAKK,SAAS,EAAC,kCAAkC;gBAAA8E,QAAA,EAC9CM,KAAK,CAACC,IAAI,CAAC;kBAAEpD,MAAM,EAAEE,IAAI,CAACgD,IAAI,CAACP,aAAa,GAAG,CAAC;gBAAE,CAAC,EAAE,CAACU,CAAC,EAAEC,SAAS,kBACjE5F,OAAA;kBAEEK,SAAS,EAAC,2CAA2C;kBACrDwF,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAGZ,QAAQ,GAAG,CAAC;kBAAK,CAAE;kBAAAC,QAAA,GACvC,OACM,EAACS,SAAS,GAAG,CAAC;gBAAA,GAJdA,SAAS;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKX,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvF,OAAA;YAAKK,SAAS,EAAC,WAAW;YAAA8E,QAAA,EACvB5E,QAAQ,CAACE,MAAM,CAACiC,GAAG,CAAC,CAACE,KAAK,EAAER,UAAU,kBACrCpC,OAAA;cAAAmF,QAAA,gBACEnF,OAAA;gBAAKK,SAAS,EAAC,wBAAwB;gBAAA8E,QAAA,eACrCnF,OAAA;kBAAKK,SAAS,EAAC,oBAAoB;kBAAA8E,QAAA,eACjCnF,OAAA;oBAAIK,SAAS,EAAC,6BAA6B;oBAAA8E,QAAA,EAAEvC,KAAK,CAACjC;kBAAI;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL3C,KAAK,CAAC/B,KAAK,CAAC6B,GAAG,CAAEP,IAAI,IAAK;gBACzB,MAAMkB,QAAQ,GAAGnB,qBAAqB,CAACC,IAAI,EAAEC,UAAU,CAAC;gBAExD,oBACEpC,OAAA;kBAAmBK,SAAS,EAAC,wBAAwB;kBAAA8E,QAAA,gBACnDnF,OAAA;oBAAKK,SAAS,EAAC,yBAAyB;oBAAA8E,QAAA,eACtCnF,OAAA;sBAAKK,SAAS,EAAC,wCAAwC;sBAAA8E,QAAA,EACpDhD,IAAI,CAACxB;oBAAI;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNvF,OAAA;oBAAKK,SAAS,EAAC,qBAAqB;oBAAA8E,QAAA,eAClCnF,OAAA;sBACEK,SAAS,EAAEZ,EAAE,CACX,iEAAiE,EACjE,wCAAwC,EACxCoC,cAAc,CAACM,IAAI,CAACpB,QAAQ,CAC9B,CAAE;sBACF8E,KAAK,EAAE;wBACLE,IAAI,EAAE,GAAG1C,QAAQ,CAAChB,QAAQ,GAAG6C,QAAQ,IAAI;wBACzCY,KAAK,EAAE,GAAGzC,QAAQ,CAACzC,QAAQ,GAAGsE,QAAQ;sBACxC,CAAE;sBACFc,OAAO,EAAEA,CAAA,KAAMjB,aAAa,CAAC5C,IAAI,EAAES,KAAK,CAAClC,EAAE,CAAE;sBAAAyE,QAAA,eAE7CnF,OAAA;wBAAMK,SAAS,EAAC,8BAA8B;wBAAA8E,QAAA,EAC3ChD,IAAI,CAACxB;sBAAI;wBAAAyE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAvBEpD,IAAI,CAACzB,EAAE;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwBZ,CAAC;cAEV,CAAC,CAAC;YAAA,GArCM3C,KAAK,CAAClC,EAAE;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5B,oBACEjG,OAAA;MAAKK,SAAS,EAAC,6CAA6C;MAAA8E,QAAA,gBAC1DnF,OAAA;QAAIK,SAAS,EAAC,4CAA4C;QAAA8E,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE5EvF,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAA8E,QAAA,EACvB5E,QAAQ,CAACE,MAAM,CAACiC,GAAG,CAAC,CAACE,KAAK,EAAER,UAAU,kBACrCpC,OAAA;UAAoBK,SAAS,EAAC,WAAW;UAAA8E,QAAA,gBACvCnF,OAAA;YAAKK,SAAS,EAAC,yBAAyB;YAAA8E,QAAA,gBACtCnF,OAAA;cAAKK,SAAS,EAAC;YAAiC;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvDvF,OAAA;cAAIK,SAAS,EAAC,qCAAqC;cAAA8E,QAAA,EAAEvC,KAAK,CAACjC;YAAI;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAENvF,OAAA;YAAKK,SAAS,EAAC,2DAA2D;YAAA8E,QAAA,GACvEvC,KAAK,CAAC/B,KAAK,CAAC6B,GAAG,CAAC,CAACP,IAAI,EAAE+D,SAAS,kBAC/BlG,OAAA;cAEEK,SAAS,EAAEZ,EAAE,CACX,oEAAoE,EACpE,iCAAiC,EACjCoC,cAAc,CAACM,IAAI,CAACpB,QAAQ,CAC9B,CAAE;cACFoF,SAAS;cACTC,WAAW,EAAG7C,CAAC,IAAKD,mBAAmB,CAACC,CAAC,EAAEpB,IAAI,EAAES,KAAK,CAAClC,EAAE,CAAE;cAC3D2F,UAAU,EAAE1C,kBAAmB;cAC/B2C,MAAM,EAAG/C,CAAC,IAAKO,cAAc,CAACP,CAAC,EAAEX,KAAK,CAAClC,EAAE,EAAEwF,SAAS,CAAE;cACtDF,OAAO,EAAEA,CAAA,KAAMjB,aAAa,CAAC5C,IAAI,EAAES,KAAK,CAAClC,EAAE,CAAE;cAAAyE,QAAA,gBAE7CnF,OAAA;gBAAKK,SAAS,EAAC,uCAAuC;gBAAA8E,QAAA,gBACpDnF,OAAA;kBAAIK,SAAS,EAAC,qBAAqB;kBAAA8E,QAAA,EAAEhD,IAAI,CAACxB;gBAAI;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpDvF,OAAA,CAACF,IAAI;kBAACa,IAAI,EAAC,cAAc;kBAACN,SAAS,EAAC;gBAA+B;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eAENvF,OAAA;gBAAKK,SAAS,EAAC,oCAAoC;gBAAA8E,QAAA,GAAC,YACxC,EAAChD,IAAI,CAACvB,QAAQ,EAAC,OAC3B;cAAA;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAELpD,IAAI,CAACrB,YAAY,CAACwB,MAAM,GAAG,CAAC,iBAC3BtC,OAAA;gBAAKK,SAAS,EAAC,+BAA+B;gBAAA8E,QAAA,GAAC,cACjC,EAAChD,IAAI,CAACrB,YAAY,CAACwB,MAAM,EAAC,UACxC;cAAA;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA,GAzBIpD,IAAI,CAACzB,EAAE;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BT,CACN,CAAC,eAEFvF,OAAA;cACEgG,OAAO,EAAEA,CAAA,KAAMzB,UAAU,CAAC3B,KAAK,CAAClC,EAAE,CAAE;cACpCL,SAAS,EAAC,mLAAmL;cAAA8E,QAAA,eAE7LnF,OAAA,CAACF,IAAI;gBAACa,IAAI,EAAC,MAAM;gBAACN,SAAS,EAAC;cAAS;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELnD,UAAU,GAAG7B,QAAQ,CAACE,MAAM,CAAC6B,MAAM,GAAG,CAAC,iBACtCtC,OAAA;YAAKK,SAAS,EAAC,qBAAqB;YAAA8E,QAAA,eAClCnF,OAAA,CAACF,IAAI;cAACa,IAAI,EAAC,WAAW;cAACN,SAAS,EAAC;YAA+B;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CACN;QAAA,GAlDO3C,KAAK,CAAClC,EAAE;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmDb,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMgB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,oBACEvG,OAAA;MAAKK,SAAS,EAAC,6CAA6C;MAAA8E,QAAA,gBAC1DnF,OAAA;QAAIK,SAAS,EAAC,4CAA4C;QAAA8E,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE7EvF,OAAA;QAAKK,SAAS,EAAC,UAAU;QAAA8E,QAAA,gBAEvBnF,OAAA;UAAKK,SAAS,EAAC;QAAgD;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEtEvF,OAAA;UAAKK,SAAS,EAAC,WAAW;UAAA8E,QAAA,EACvB5E,QAAQ,CAACE,MAAM,CAACiC,GAAG,CAAC,CAACE,KAAK,EAAER,UAAU,kBACrCpC,OAAA;YAAoBK,SAAS,EAAC,UAAU;YAAA8E,QAAA,gBAEtCnF,OAAA;cAAKK,SAAS,EAAC;YAA4E;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAElGvF,OAAA;cAAKK,SAAS,EAAC,OAAO;cAAA8E,QAAA,gBACpBnF,OAAA;gBAAIK,SAAS,EAAC,0CAA0C;gBAAA8E,QAAA,EAAEvC,KAAK,CAACjC;cAAI;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1EvF,OAAA;gBAAKK,SAAS,EAAC,oCAAoC;gBAAA8E,QAAA,GAAC,YACxC,EAACvC,KAAK,CAAChC,QAAQ,EAAC,QAC5B;cAAA;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENvF,OAAA;gBAAKK,SAAS,EAAC,WAAW;gBAAA8E,QAAA,EACvBvC,KAAK,CAAC/B,KAAK,CAAC6B,GAAG,CAAEP,IAAI,iBACpBnC,OAAA;kBAEEK,SAAS,EAAC,4HAA4H;kBACtI2F,OAAO,EAAEA,CAAA,KAAMjB,aAAa,CAAC5C,IAAI,EAAES,KAAK,CAAClC,EAAE,CAAE;kBAAAyE,QAAA,gBAE7CnF,OAAA;oBAAKK,SAAS,EAAEZ,EAAE,CAChB,sBAAsB,EACtB0C,IAAI,CAACpB,QAAQ,KAAK,QAAQ,IAAI,YAAY,EAC1CoB,IAAI,CAACpB,QAAQ,KAAK,MAAM,IAAI,eAAe,EAC3CoB,IAAI,CAACpB,QAAQ,KAAK,QAAQ,IAAI,eAAe,EAC7CoB,IAAI,CAACpB,QAAQ,KAAK,KAAK,IAAI,aAC7B;kBAAE;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAETvF,OAAA;oBAAKK,SAAS,EAAC,QAAQ;oBAAA8E,QAAA,gBACrBnF,OAAA;sBAAKK,SAAS,EAAC,qBAAqB;sBAAA8E,QAAA,EAAEhD,IAAI,CAACxB;oBAAI;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtDvF,OAAA;sBAAKK,SAAS,EAAC,+BAA+B;sBAAA8E,QAAA,GAC3ChD,IAAI,CAACvB,QAAQ,EAAC,eAAQ,EAACuB,IAAI,CAACpB,QAAQ,EAAC,WACxC;oBAAA;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAELpD,IAAI,CAACrB,YAAY,CAACwB,MAAM,GAAG,CAAC,iBAC3BtC,OAAA,CAACF,IAAI;oBAACa,IAAI,EAAC,MAAM;oBAACN,SAAS,EAAC;kBAA+B;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC9D;gBAAA,GArBIpD,IAAI,CAACzB,EAAE;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsBT,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAtCE3C,KAAK,CAAClC,EAAE;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuCb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMiB,UAAU,GAAGA,CAAA,KAAM;IACvBtG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGK,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEP,OAAA;IAAKK,SAAS,EAAEZ,EAAE,CAAC,iCAAiC,EAAEY,SAAS,CAAE;IAAA8E,QAAA,gBAE/DnF,OAAA;MAAKK,SAAS,EAAC,uBAAuB;MAAA8E,QAAA,gBACpCnF,OAAA;QAAIK,SAAS,EAAC,oCAAoC;QAAA8E,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxEvF,OAAA;QAAGK,SAAS,EAAC,uBAAuB;QAAA8E,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNvF,OAAA;MAAKK,SAAS,EAAC,mCAAmC;MAAA8E,QAAA,gBAChDnF,OAAA,CAACJ,MAAM;QACL+B,KAAK,EAAEX,QAAS;QAChByF,aAAa,EAAExF,WAAY;QAC3ByF,OAAO,EAAEhF,eAAgB;QACzBrB,SAAS,EAAC;MAAM;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAEFvF,OAAA;QAAKK,SAAS,EAAC,+BAA+B;QAAA8E,QAAA,GAC3C5E,QAAQ,CAACE,MAAM,CAACkG,MAAM,CAAC,CAACC,KAAK,EAAEhE,KAAK,KAAKgE,KAAK,GAAGhE,KAAK,CAAC/B,KAAK,CAACyB,MAAM,EAAE,CAAC,CAAC,EAAC,gBAAc,EAAC/B,QAAQ,CAACE,MAAM,CAAC6B,MAAM,EAAC,SACjH;MAAA;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLvE,QAAQ,KAAK,OAAO,IAAIgE,gBAAgB,CAAC,CAAC,EAC1ChE,QAAQ,KAAK,WAAW,IAAIiF,eAAe,CAAC,CAAC,EAC7CjF,QAAQ,KAAK,UAAU,IAAIuF,kBAAkB,CAAC,CAAC,eAGhDvG,OAAA;MAAKK,SAAS,EAAC,wCAAwC;MAAA8E,QAAA,gBACrDnF,OAAA,CAACN,MAAM;QACLmH,OAAO,EAAC,SAAS;QACjBb,OAAO,EAAE7F,MAAO;QAChB2G,QAAQ,EAAC,WAAW;QACpBC,YAAY,EAAC,MAAM;QAAA5B,QAAA,EACpB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETvF,OAAA;QAAKK,SAAS,EAAC,yBAAyB;QAAA8E,QAAA,gBACtCnF,OAAA;UAAKK,SAAS,EAAC,+BAA+B;UAAA8E,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvF,OAAA,CAACN,MAAM;UACLsG,OAAO,EAAEQ,UAAW;UACpBM,QAAQ,EAAC,YAAY;UACrBC,YAAY,EAAC,OAAO;UAAA5B,QAAA,EACrB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvF,OAAA,CAACH,KAAK;MACJmH,MAAM,EAAE5F,eAAgB;MACxB6F,OAAO,EAAEA,CAAA,KAAM5F,kBAAkB,CAAC,KAAK,CAAE;MACzC6F,KAAK,EAAC,cAAc;MACpBC,IAAI,EAAC,IAAI;MAAAhC,QAAA,EAERjE,YAAY,iBACXlB,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAA8E,QAAA,gBACxBnF,OAAA;UAAKK,SAAS,EAAC,uCAAuC;UAAA8E,QAAA,gBACpDnF,OAAA,CAACL,KAAK;YACJiC,KAAK,EAAC,WAAW;YACjBD,KAAK,EAAET,YAAY,CAACP,IAAK;YACzByG,QAAQ,EAAG7D,CAAC,IAAKpC,eAAe,CAAC+C,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEvD,IAAI,EAAE4C,CAAC,CAAC8D,MAAM,CAAC1F;YAAM,CAAC,CAAC;UAAE;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eAEFvF,OAAA,CAACL,KAAK;YACJiC,KAAK,EAAC,iBAAiB;YACvB0F,IAAI,EAAC,QAAQ;YACb3F,KAAK,EAAET,YAAY,CAACN,QAAS;YAC7BwG,QAAQ,EAAG7D,CAAC,IAAKpC,eAAe,CAAC+C,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEtD,QAAQ,EAAE2G,QAAQ,CAAChE,CAAC,CAAC8D,MAAM,CAAC1F,KAAK,CAAC,IAAI;YAAE,CAAC,CAAC,CAAE;YACjG6F,GAAG,EAAC;UAAG;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENvF,OAAA,CAACJ,MAAM;UACLgC,KAAK,EAAC,UAAU;UAChBD,KAAK,EAAET,YAAY,CAACH,QAAS;UAC7B0F,aAAa,EAAG9E,KAAK,IAAKR,eAAe,CAAC+C,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEnD,QAAQ,EAAEY;UAAM,CAAC,CAAC,CAAE;UAClF+E,OAAO,EAAE,CACP;YAAE/E,KAAK,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAe,CAAC,EACvC;YAAED,KAAK,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAkB,CAAC,EAC7C;YAAED,KAAK,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAgB,CAAC,EACzC;YAAED,KAAK,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAkB,CAAC;QAC7C;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFvF,OAAA;UAAAmF,QAAA,gBACEnF,OAAA;YAAOK,SAAS,EAAC,gDAAgD;YAAA8E,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRvF,OAAA;YAAKK,SAAS,EAAC,WAAW;YAAA8E,QAAA,EACvB5E,QAAQ,CAACE,MAAM,CAACiC,GAAG,CAACE,KAAK,IACxBA,KAAK,CAAC/B,KAAK,CACRyD,MAAM,CAACnC,IAAI,IAAIA,IAAI,CAACzB,EAAE,KAAKQ,YAAY,CAACR,EAAE,CAAC,CAC3CgC,GAAG,CAACP,IAAI,iBACPnC,OAAA;cAAqBK,SAAS,EAAC,yBAAyB;cAAA8E,QAAA,gBACtDnF,OAAA;gBACEsH,IAAI,EAAC,UAAU;gBACfG,OAAO,EAAEvG,YAAY,CAACJ,YAAY,CAAC4G,QAAQ,CAACvF,IAAI,CAACzB,EAAE,CAAE;gBACrD0G,QAAQ,EAAG7D,CAAC,IAAK;kBACf,MAAMoE,SAAS,GAAGpE,CAAC,CAAC8D,MAAM,CAACI,OAAO;kBAClCtG,eAAe,CAAC+C,IAAI,KAAK;oBACvB,GAAGA,IAAI;oBACPpD,YAAY,EAAE6G,SAAS,GACnB,CAAC,GAAGzD,IAAI,CAACpD,YAAY,EAAEqB,IAAI,CAACzB,EAAE,CAAC,GAC/BwD,IAAI,CAACpD,YAAY,CAACwD,MAAM,CAAC5D,EAAE,IAAIA,EAAE,KAAKyB,IAAI,CAACzB,EAAE;kBACnD,CAAC,CAAC,CAAC;gBACL,CAAE;gBACFL,SAAS,EAAC;cAAsB;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFvF,OAAA;gBAAMK,SAAS,EAAC,SAAS;gBAAA8E,QAAA,EAAEhD,IAAI,CAACxB;cAAI;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAflCpD,IAAI,CAACzB,EAAE;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBZ,CACR,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvF,OAAA;UAAKK,SAAS,EAAC,sBAAsB;UAAA8E,QAAA,gBACnCnF,OAAA,CAACN,MAAM;YACLmH,OAAO,EAAC,aAAa;YACrBb,OAAO,EAAEA,CAAA,KAAM;cACblB,UAAU,CAAC5D,YAAY,CAACsC,OAAO,EAAEtC,YAAY,CAACR,EAAE,CAAC;cACjDW,kBAAkB,CAAC,KAAK,CAAC;YAC3B,CAAE;YACFyF,QAAQ,EAAC,QAAQ;YACjBC,YAAY,EAAC,MAAM;YAAA5B,QAAA,EACpB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETvF,OAAA;YAAKK,SAAS,EAAC,YAAY;YAAA8E,QAAA,gBACzBnF,OAAA,CAACN,MAAM;cACLmH,OAAO,EAAC,SAAS;cACjBb,OAAO,EAAEA,CAAA,KAAM3E,kBAAkB,CAAC,KAAK,CAAE;cAAA8D,QAAA,EAC1C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvF,OAAA,CAACN,MAAM;cACLsG,OAAO,EAAEA,CAAA,KAAM;gBACbrB,UAAU,CAACzD,YAAY,CAACsC,OAAO,EAAEtC,YAAY,CAACR,EAAE,EAAE;kBAChDC,IAAI,EAAEO,YAAY,CAACP,IAAI;kBACvBC,QAAQ,EAAEM,YAAY,CAACN,QAAQ;kBAC/BG,QAAQ,EAAEG,YAAY,CAACH,QAAQ;kBAC/BD,YAAY,EAAEI,YAAY,CAACJ;gBAC7B,CAAC,CAAC;gBACFO,kBAAkB,CAAC,KAAK,CAAC;cAC3B,CAAE;cAAA8D,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACjF,EAAA,CA1lBIL,kBAAkB;AAAA2H,EAAA,GAAlB3H,kBAAkB;AA4lBxB,eAAeA,kBAAkB;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}