{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\ui\\\\Toggle.jsx\";\nimport React from 'react';\nimport { cn } from '../../utils/cn';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Toggle = /*#__PURE__*/React.forwardRef(_c = ({\n  className,\n  checked = false,\n  onChange,\n  label,\n  description,\n  disabled = false,\n  size = 'default',\n  ...props\n}, ref) => {\n  const handleToggle = () => {\n    if (!disabled) {\n      onChange === null || onChange === void 0 ? void 0 : onChange(!checked);\n    }\n  };\n  const sizeClasses = {\n    sm: 'h-4 w-8',\n    default: 'h-5 w-10',\n    lg: 'h-6 w-12'\n  };\n  const thumbSizeClasses = {\n    sm: 'h-3 w-3',\n    default: 'h-4 w-4',\n    lg: 'h-5 w-5'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn(\"flex items-center space-x-3\", className),\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      ref: ref,\n      type: \"button\",\n      role: \"switch\",\n      \"aria-checked\": checked,\n      onClick: handleToggle,\n      disabled: disabled,\n      className: cn(\"relative inline-flex shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out\", \"focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", \"disabled:cursor-not-allowed disabled:opacity-50\", sizeClasses[size], checked ? \"bg-primary\" : \"bg-secondary\"),\n      ...props,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: cn(\"pointer-events-none inline-block rounded-full bg-background shadow-lg ring-0 transition duration-200 ease-in-out\", thumbSizeClasses[size], checked ? size === 'sm' ? 'translate-x-4' : size === 'lg' ? 'translate-x-6' : 'translate-x-5' : 'translate-x-0')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), (label || description) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col\",\n      children: [label && /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"text-sm font-medium text-foreground cursor-pointer\",\n        onClick: handleToggle,\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 13\n      }, this), description && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-muted-foreground\",\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n});\n_c2 = Toggle;\nToggle.displayName = \"Toggle\";\nexport default Toggle;\nvar _c, _c2;\n$RefreshReg$(_c, \"Toggle$React.forwardRef\");\n$RefreshReg$(_c2, \"Toggle\");", "map": {"version": 3, "names": ["React", "cn", "jsxDEV", "_jsxDEV", "Toggle", "forwardRef", "_c", "className", "checked", "onChange", "label", "description", "disabled", "size", "props", "ref", "handleToggle", "sizeClasses", "sm", "default", "lg", "thumbSizeClasses", "children", "type", "role", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/ui/Toggle.jsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '../../utils/cn';\n\nconst Toggle = React.forwardRef(({\n  className,\n  checked = false,\n  onChange,\n  label,\n  description,\n  disabled = false,\n  size = 'default',\n  ...props\n}, ref) => {\n  const handleToggle = () => {\n    if (!disabled) {\n      onChange?.(!checked);\n    }\n  };\n\n  const sizeClasses = {\n    sm: 'h-4 w-8',\n    default: 'h-5 w-10',\n    lg: 'h-6 w-12'\n  };\n\n  const thumbSizeClasses = {\n    sm: 'h-3 w-3',\n    default: 'h-4 w-4',\n    lg: 'h-5 w-5'\n  };\n\n  return (\n    <div className={cn(\"flex items-center space-x-3\", className)}>\n      <button\n        ref={ref}\n        type=\"button\"\n        role=\"switch\"\n        aria-checked={checked}\n        onClick={handleToggle}\n        disabled={disabled}\n        className={cn(\n          \"relative inline-flex shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out\",\n          \"focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n          \"disabled:cursor-not-allowed disabled:opacity-50\",\n          sizeClasses[size],\n          checked ? \"bg-primary\" : \"bg-secondary\"\n        )}\n        {...props}\n      >\n        <span\n          className={cn(\n            \"pointer-events-none inline-block rounded-full bg-background shadow-lg ring-0 transition duration-200 ease-in-out\",\n            thumbSizeClasses[size],\n            checked \n              ? size === 'sm' ? 'translate-x-4' : size === 'lg' ? 'translate-x-6' : 'translate-x-5'\n              : 'translate-x-0'\n          )}\n        />\n      </button>\n      \n      {(label || description) && (\n        <div className=\"flex flex-col\">\n          {label && (\n            <label \n              className=\"text-sm font-medium text-foreground cursor-pointer\"\n              onClick={handleToggle}\n            >\n              {label}\n            </label>\n          )}\n          {description && (\n            <p className=\"text-xs text-muted-foreground\">\n              {description}\n            </p>\n          )}\n        </div>\n      )}\n    </div>\n  );\n});\n\nToggle.displayName = \"Toggle\";\n\nexport default Toggle;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,EAAE,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,MAAM,gBAAGJ,KAAK,CAACK,UAAU,CAAAC,EAAA,GAACA,CAAC;EAC/BC,SAAS;EACTC,OAAO,GAAG,KAAK;EACfC,QAAQ;EACRC,KAAK;EACLC,WAAW;EACXC,QAAQ,GAAG,KAAK;EAChBC,IAAI,GAAG,SAAS;EAChB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACJ,QAAQ,EAAE;MACbH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAG,CAACD,OAAO,CAAC;IACtB;EACF,CAAC;EAED,MAAMS,WAAW,GAAG;IAClBC,EAAE,EAAE,SAAS;IACbC,OAAO,EAAE,UAAU;IACnBC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,gBAAgB,GAAG;IACvBH,EAAE,EAAE,SAAS;IACbC,OAAO,EAAE,SAAS;IAClBC,EAAE,EAAE;EACN,CAAC;EAED,oBACEjB,OAAA;IAAKI,SAAS,EAAEN,EAAE,CAAC,6BAA6B,EAAEM,SAAS,CAAE;IAAAe,QAAA,gBAC3DnB,OAAA;MACEY,GAAG,EAAEA,GAAI;MACTQ,IAAI,EAAC,QAAQ;MACbC,IAAI,EAAC,QAAQ;MACb,gBAAchB,OAAQ;MACtBiB,OAAO,EAAET,YAAa;MACtBJ,QAAQ,EAAEA,QAAS;MACnBL,SAAS,EAAEN,EAAE,CACX,kIAAkI,EAClI,qEAAqE,EACrE,iDAAiD,EACjDgB,WAAW,CAACJ,IAAI,CAAC,EACjBL,OAAO,GAAG,YAAY,GAAG,cAC3B,CAAE;MAAA,GACEM,KAAK;MAAAQ,QAAA,eAETnB,OAAA;QACEI,SAAS,EAAEN,EAAE,CACX,kHAAkH,EAClHoB,gBAAgB,CAACR,IAAI,CAAC,EACtBL,OAAO,GACHK,IAAI,KAAK,IAAI,GAAG,eAAe,GAAGA,IAAI,KAAK,IAAI,GAAG,eAAe,GAAG,eAAe,GACnF,eACN;MAAE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAER,CAACnB,KAAK,IAAIC,WAAW,kBACpBR,OAAA;MAAKI,SAAS,EAAC,eAAe;MAAAe,QAAA,GAC3BZ,KAAK,iBACJP,OAAA;QACEI,SAAS,EAAC,oDAAoD;QAC9DkB,OAAO,EAAET,YAAa;QAAAM,QAAA,EAErBZ;MAAK;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EACAlB,WAAW,iBACVR,OAAA;QAAGI,SAAS,EAAC,+BAA+B;QAAAe,QAAA,EACzCX;MAAW;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC,CAAC;AAACC,GAAA,GA5EG1B,MAAM;AA8EZA,MAAM,CAAC2B,WAAW,GAAG,QAAQ;AAE7B,eAAe3B,MAAM;AAAC,IAAAE,EAAA,EAAAwB,GAAA;AAAAE,YAAA,CAAA1B,EAAA;AAAA0B,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}