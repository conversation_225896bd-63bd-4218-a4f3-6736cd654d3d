import React from "react";
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import Routes from "./Routes";
import { AuthProvider } from "./contexts/AuthContext";
import { ThemeProvider } from './contexts/ThemeContext';
import { KeyboardShortcutsProvider } from './contexts/KeyboardShortcutsContext';
import { AccessibilityProvider } from './components/accessibility/AccessibilityProvider';
import ErrorBoundary from './components/ErrorBoundary';
import './styles/accessibility.css';

function App() {
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <ThemeProvider>
          <AccessibilityProvider>
            <AuthProvider>
              <KeyboardShortcutsProvider>
                <div className="w-full">
                  <Routes />
                </div>
              </KeyboardShortcutsProvider>
            </AuthProvider>
          </AccessibilityProvider>
        </ThemeProvider>
      </BrowserRouter>
    </ErrorBoundary>
  );
}

export default App;