import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CardDescription = ({ card, onDescriptionChange, canEdit }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [description, setDescription] = useState(card.description || '');

  const handleSave = () => {
    onDescriptionChange(description);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setDescription(card.description || '');
    setIsEditing(false);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-3">
        <Icon name="AlignLeft" size={20} className="text-text-secondary" />
        <h3 className="text-lg font-semibold text-text-primary">Description</h3>
      </div>
      
      {isEditing ? (
        <div className="space-y-3 ml-8">
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Add a more detailed description..."
            className="w-full min-h-[120px] p-3 border border-border rounded-md resize-none focus:ring-2 focus:ring-primary focus:border-transparent"
            autoFocus
          />
          <div className="flex items-center space-x-2">
            <Button size="sm" onClick={handleSave}>
              Save
            </Button>
            <Button variant="ghost" size="sm" onClick={handleCancel}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div className="ml-8">
          {card.description ? (
            <div 
              className={`text-text-primary whitespace-pre-wrap ${canEdit ? 'cursor-pointer hover:bg-muted rounded p-2 -m-2' : ''}`}
              onClick={() => canEdit && setIsEditing(true)}
            >
              {card.description}
            </div>
          ) : (
            <div 
              className={`text-text-secondary italic ${canEdit ? 'cursor-pointer hover:bg-muted rounded p-2 -m-2' : ''}`}
              onClick={() => canEdit && setIsEditing(true)}
            >
              Add a more detailed description...
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CardDescription;