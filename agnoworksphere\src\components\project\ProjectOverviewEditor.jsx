import React, { useState, useEffect } from 'react';
import { cn } from '../../utils/cn';
import Button from '../ui/Button';
import Input from '../ui/Input';
import RichTextEditor from '../ui/RichTextEditor';
import DatePicker from '../ui/DatePicker';
import Icon from '../AppIcon';

const ProjectOverviewEditor = ({ 
  onNext, 
  onBack, 
  initialData = {}, 
  className 
}) => {
  const [overview, setOverview] = useState({
    title: '',
    description: '',
    objectives: [''],
    deliverables: [''],
    kpis: [{ name: '', target: '', measurement: '' }],
    timeline: {
      startDate: '',
      endDate: '',
      milestones: [{ name: '', date: '', description: '' }]
    },
    stakeholders: [''],
    riskAssessment: '',
    successCriteria: '',
    ...initialData
  });

  const [errors, setErrors] = useState({});
  const [isValid, setIsValid] = useState(false);

  useEffect(() => {
    validateOverview();
  }, [overview]);

  const validateOverview = () => {
    const newErrors = {};
    
    if (!overview.title.trim()) {
      newErrors.title = 'Project title is required';
    }
    
    if (!overview.description.trim()) {
      newErrors.description = 'Project description is required';
    }
    
    if (overview.description.length > 10000) {
      newErrors.description = 'Description must be under 10,000 characters';
    }
    
    if (overview.objectives.filter(obj => obj.trim()).length === 0) {
      newErrors.objectives = 'At least one objective is required';
    }
    
    if (overview.deliverables.filter(del => del.trim()).length === 0) {
      newErrors.deliverables = 'At least one deliverable is required';
    }

    setErrors(newErrors);
    setIsValid(Object.keys(newErrors).length === 0);
  };

  const updateOverview = (path, value) => {
    setOverview(prev => {
      const newOverview = { ...prev };
      const keys = path.split('.');
      let current = newOverview;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newOverview;
    });
  };

  const addArrayItem = (path, defaultValue = '') => {
    const current = path.split('.').reduce((obj, key) => obj[key], overview);
    const newArray = [...current, defaultValue];
    updateOverview(path, newArray);
  };

  const removeArrayItem = (path, index) => {
    const current = path.split('.').reduce((obj, key) => obj[key], overview);
    const newArray = current.filter((_, i) => i !== index);
    updateOverview(path, newArray);
  };

  const updateArrayItem = (path, index, value) => {
    const current = path.split('.').reduce((obj, key) => obj[key], overview);
    const newArray = [...current];
    newArray[index] = value;
    updateOverview(path, newArray);
  };

  const handleNext = () => {
    if (isValid) {
      onNext?.(overview);
    }
  };

  return (
    <div className={cn("max-w-4xl mx-auto p-6 space-y-8", className)}>
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold text-foreground">Project Overview</h2>
        <p className="text-muted-foreground">
          Define your project's vision, goals, and success metrics
        </p>
      </div>

      {/* Basic Information */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-6">
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Icon name="FileText" className="h-5 w-5" />
          Basic Information
        </h3>
        
        <div className="space-y-4">
          <Input
            label="Project Title"
            value={overview.title}
            onChange={(e) => updateOverview('title', e.target.value)}
            placeholder="Enter project title"
            error={errors.title}
            required
          />
          
          <RichTextEditor
            label="Project Description"
            value={overview.description}
            onChange={(value) => updateOverview('description', value)}
            placeholder="Provide a comprehensive description of your project..."
            maxLength={10000}
            error={errors.description}
            description="Describe the project scope, background, and context (up to 10,000 characters)"
            showWordCount
            allowMedia
          />
        </div>
      </div>

      {/* Objectives Section */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
            <Icon name="Target" className="h-5 w-5" />
            Project Objectives
          </h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => addArrayItem('objectives')}
            iconName="Plus"
            iconPosition="left"
          >
            Add Objective
          </Button>
        </div>
        
        <div className="space-y-3">
          {overview.objectives.map((objective, index) => (
            <div key={index} className="flex gap-2">
              <Input
                value={objective}
                onChange={(e) => updateArrayItem('objectives', index, e.target.value)}
                placeholder={`Objective ${index + 1}`}
                className="flex-1"
              />
              {overview.objectives.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeArrayItem('objectives', index)}
                  iconName="X"
                />
              )}
            </div>
          ))}
        </div>
        
        {errors.objectives && <p className="text-destructive text-sm">{errors.objectives}</p>}
      </div>

      {/* Deliverables Section */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
            <Icon name="Package" className="h-5 w-5" />
            Key Deliverables
          </h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => addArrayItem('deliverables')}
            iconName="Plus"
            iconPosition="left"
          >
            Add Deliverable
          </Button>
        </div>
        
        <div className="space-y-3">
          {overview.deliverables.map((deliverable, index) => (
            <div key={index} className="flex gap-2">
              <Input
                value={deliverable}
                onChange={(e) => updateArrayItem('deliverables', index, e.target.value)}
                placeholder={`Deliverable ${index + 1}`}
                className="flex-1"
              />
              {overview.deliverables.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeArrayItem('deliverables', index)}
                  iconName="X"
                />
              )}
            </div>
          ))}
        </div>
        
        {errors.deliverables && <p className="text-destructive text-sm">{errors.deliverables}</p>}
      </div>

      {/* KPIs Section */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
            <Icon name="BarChart3" className="h-5 w-5" />
            Key Performance Indicators
          </h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => addArrayItem('kpis', { name: '', target: '', measurement: '' })}
            iconName="Plus"
            iconPosition="left"
          >
            Add KPI
          </Button>
        </div>
        
        <div className="space-y-4">
          {overview.kpis.map((kpi, index) => (
            <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-3 p-4 border border-border rounded-lg">
              <Input
                label="KPI Name"
                value={kpi.name}
                onChange={(e) => updateArrayItem('kpis', index, { ...kpi, name: e.target.value })}
                placeholder="e.g., User Satisfaction"
              />
              <Input
                label="Target Value"
                value={kpi.target}
                onChange={(e) => updateArrayItem('kpis', index, { ...kpi, target: e.target.value })}
                placeholder="e.g., 95%"
              />
              <div className="flex gap-2">
                <Input
                  label="Measurement Method"
                  value={kpi.measurement}
                  onChange={(e) => updateArrayItem('kpis', index, { ...kpi, measurement: e.target.value })}
                  placeholder="e.g., Survey scores"
                  className="flex-1"
                />
                {overview.kpis.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeArrayItem('kpis', index)}
                    iconName="X"
                    className="mt-6"
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Timeline Section */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-6">
        <h3 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Icon name="Calendar" className="h-5 w-5" />
          Project Timeline
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <DatePicker
            label="Start Date"
            value={overview.timeline.startDate}
            onChange={(value) => updateOverview('timeline.startDate', value)}
            description="Project kickoff date"
          />
          <DatePicker
            label="End Date"
            value={overview.timeline.endDate}
            onChange={(value) => updateOverview('timeline.endDate', value)}
            description="Expected completion date"
            minDate={overview.timeline.startDate}
          />
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-medium text-foreground">Milestones</h4>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addArrayItem('timeline.milestones', { name: '', date: '', description: '' })}
              iconName="Plus"
              iconPosition="left"
            >
              Add Milestone
            </Button>
          </div>

          {overview.timeline.milestones.map((milestone, index) => (
            <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-3 p-4 border border-border rounded-lg">
              <Input
                label="Milestone Name"
                value={milestone.name}
                onChange={(e) => updateArrayItem('timeline.milestones', index, { ...milestone, name: e.target.value })}
                placeholder="e.g., MVP Release"
              />
              <DatePicker
                label="Target Date"
                value={milestone.date}
                onChange={(value) => updateArrayItem('timeline.milestones', index, { ...milestone, date: value })}
                minDate={overview.timeline.startDate}
                maxDate={overview.timeline.endDate}
              />
              <div className="flex gap-2">
                <Input
                  label="Description"
                  value={milestone.description}
                  onChange={(e) => updateArrayItem('timeline.milestones', index, { ...milestone, description: e.target.value })}
                  placeholder="Brief description"
                  className="flex-1"
                />
                {overview.timeline.milestones.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeArrayItem('timeline.milestones', index)}
                    iconName="X"
                    className="mt-6"
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Additional Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-card rounded-lg p-6 border border-border space-y-4">
          <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Icon name="Users" className="h-5 w-5" />
            Key Stakeholders
          </h3>

          <div className="space-y-3">
            {overview.stakeholders.map((stakeholder, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={stakeholder}
                  onChange={(e) => updateArrayItem('stakeholders', index, e.target.value)}
                  placeholder={`Stakeholder ${index + 1}`}
                  className="flex-1"
                />
                {overview.stakeholders.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeArrayItem('stakeholders', index)}
                    iconName="X"
                  />
                )}
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => addArrayItem('stakeholders')}
              iconName="Plus"
              iconPosition="left"
              className="w-full"
            >
              Add Stakeholder
            </Button>
          </div>
        </div>

        <div className="bg-card rounded-lg p-6 border border-border space-y-4">
          <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
            <Icon name="AlertTriangle" className="h-5 w-5" />
            Risk Assessment
          </h3>

          <RichTextEditor
            value={overview.riskAssessment}
            onChange={(value) => updateOverview('riskAssessment', value)}
            placeholder="Identify potential risks and mitigation strategies..."
            maxLength={2000}
            showToolbar={false}
            showWordCount
            className="min-h-[120px]"
          />
        </div>
      </div>

      {/* Success Criteria */}
      <div className="bg-card rounded-lg p-6 border border-border space-y-4">
        <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
          <Icon name="CheckCircle" className="h-5 w-5" />
          Success Criteria
        </h3>

        <RichTextEditor
          value={overview.successCriteria}
          onChange={(value) => updateOverview('successCriteria', value)}
          placeholder="Define what success looks like for this project..."
          maxLength={2000}
          showToolbar={false}
          showWordCount
        />
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6">
        <Button
          variant="outline"
          onClick={onBack}
          iconName="ArrowLeft"
          iconPosition="left"
        >
          Back to Configuration
        </Button>

        <div className="flex items-center gap-2">
          <div className="text-sm text-muted-foreground">
            Step 2 of 6
          </div>
          <Button
            onClick={handleNext}
            disabled={!isValid}
            iconName="ArrowRight"
            iconPosition="right"
          >
            Continue to Tech Stack
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProjectOverviewEditor;
