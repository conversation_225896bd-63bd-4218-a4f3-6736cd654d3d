[{"C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx": "1", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx": "2", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx": "3", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx": "4", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx": "5", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx": "6", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx": "7", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx": "8", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx": "9", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx": "10", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx": "11", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx": "12", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx": "13", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx": "14", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx": "15", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx": "16", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx": "17", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx": "18", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx": "19", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx": "20", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx": "21", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx": "22", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx": "23", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx": "24", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx": "25", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx": "26", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx": "27", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js": "28", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx": "29", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx": "30", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx": "31", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx": "32", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx": "33", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx": "34", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx": "35", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx": "36", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx": "37", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx": "38", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx": "39", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx": "40", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx": "41", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx": "42", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx": "43", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx": "44", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx": "45", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx": "46", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx": "47", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx": "48", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx": "49", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx": "50", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx": "51", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx": "52", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx": "53", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx": "54", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx": "55", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx": "56", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx": "57", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx": "58", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx": "59", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx": "60", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx": "61", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx": "62", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx": "63", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx": "64", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx": "65", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx": "66", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx": "67", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx": "68", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx": "69", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx": "70", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx": "71", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx": "72", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js": "73", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx": "74", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx": "75", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx": "76", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx": "77", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx": "78", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js": "79", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js": "80", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx": "81", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx": "82", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx": "83", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\KeyboardShortcutsContext.jsx": "84", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ThemeContext.jsx": "85", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\accessibility\\AccessibilityProvider.jsx": "86", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-overview\\index.jsx": "87", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\rolePermissions.js": "88", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiChecklistService.js": "89", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\errorHandling.js": "90", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateOrganizationModal.jsx": "91", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realApiService.js": "92", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\teamService.js": "93", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\notificationService.js": "94", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\analytics\\index.jsx": "95", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\billing\\index.jsx": "96", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realAuthService.js": "97", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ProtectedRoute.jsx": "98", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\hooks\\useUserProfile.js": "99", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\NotificationDropdown.jsx": "100", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateTaskModal.jsx": "101", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\AIReportModal.jsx": "102", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiReportService.js": "103", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\projectEventService.js": "104", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\charts\\ReportCharts.jsx": "105", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateAIProjectModal.jsx": "106", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\EnhancedCreateAIProjectModal.jsx": "107", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\ProjectExportButton.jsx": "108"}, {"size": 1396, "mtime": 1754034229833, "results": "109", "hashOfConfig": "110"}, {"size": 977, "mtime": 1754033081943, "results": "111", "hashOfConfig": "110"}, {"size": 2842, "mtime": 1754286869549, "results": "112", "hashOfConfig": "110"}, {"size": 2279, "mtime": 1754126212460, "results": "113", "hashOfConfig": "110"}, {"size": 263, "mtime": 1753662156000, "results": "114", "hashOfConfig": "110"}, {"size": 2597, "mtime": 1753662156000, "results": "115", "hashOfConfig": "110"}, {"size": 1443, "mtime": 1753896368000, "results": "116", "hashOfConfig": "110"}, {"size": 1851, "mtime": 1753662960000, "results": "117", "hashOfConfig": "110"}, {"size": 2221, "mtime": 1754033188241, "results": "118", "hashOfConfig": "110"}, {"size": 25379, "mtime": 1754289922662, "results": "119", "hashOfConfig": "110"}, {"size": 12772, "mtime": 1754200544518, "results": "120", "hashOfConfig": "110"}, {"size": 19565, "mtime": 1754126131895, "results": "121", "hashOfConfig": "110"}, {"size": 8178, "mtime": 1754043014618, "results": "122", "hashOfConfig": "110"}, {"size": 12073, "mtime": 1754036144487, "results": "123", "hashOfConfig": "110"}, {"size": 15520, "mtime": 1754316508370, "results": "124", "hashOfConfig": "110"}, {"size": 9015, "mtime": 1754287684311, "results": "125", "hashOfConfig": "110"}, {"size": 32913, "mtime": 1754326468461, "results": "126", "hashOfConfig": "110"}, {"size": 619, "mtime": 1753662156000, "results": "127", "hashOfConfig": "110"}, {"size": 3229, "mtime": 1753931434000, "results": "128", "hashOfConfig": "110"}, {"size": 15516, "mtime": 1754037223798, "results": "129", "hashOfConfig": "110"}, {"size": 3119, "mtime": 1753662156000, "results": "130", "hashOfConfig": "110"}, {"size": 9775, "mtime": 1753662156000, "results": "131", "hashOfConfig": "110"}, {"size": 1661, "mtime": 1753662960000, "results": "132", "hashOfConfig": "110"}, {"size": 8345, "mtime": 1754040316710, "results": "133", "hashOfConfig": "110"}, {"size": 2069, "mtime": 1753662960000, "results": "134", "hashOfConfig": "110"}, {"size": 4544, "mtime": 1754316602745, "results": "135", "hashOfConfig": "110"}, {"size": 1270, "mtime": 1753662960000, "results": "136", "hashOfConfig": "110"}, {"size": 12175, "mtime": 1754056135125, "results": "137", "hashOfConfig": "110"}, {"size": 14625, "mtime": 1754121145710, "results": "138", "hashOfConfig": "110"}, {"size": 3061, "mtime": 1753662960000, "results": "139", "hashOfConfig": "110"}, {"size": 14179, "mtime": 1754289123461, "results": "140", "hashOfConfig": "110"}, {"size": 11396, "mtime": 1754289682949, "results": "141", "hashOfConfig": "110"}, {"size": 7293, "mtime": 1754121209630, "results": "142", "hashOfConfig": "110"}, {"size": 4639, "mtime": 1754041421031, "results": "143", "hashOfConfig": "110"}, {"size": 4319, "mtime": 1753662960000, "results": "144", "hashOfConfig": "110"}, {"size": 3173, "mtime": 1754300540067, "results": "145", "hashOfConfig": "110"}, {"size": 11197, "mtime": 1754052040562, "results": "146", "hashOfConfig": "110"}, {"size": 8439, "mtime": 1754123709182, "results": "147", "hashOfConfig": "110"}, {"size": 2962, "mtime": 1753662960000, "results": "148", "hashOfConfig": "110"}, {"size": 4192, "mtime": 1753662960000, "results": "149", "hashOfConfig": "110"}, {"size": 2269, "mtime": 1753662960000, "results": "150", "hashOfConfig": "110"}, {"size": 6524, "mtime": 1754123731298, "results": "151", "hashOfConfig": "110"}, {"size": 5011, "mtime": 1753662960000, "results": "152", "hashOfConfig": "110"}, {"size": 5667, "mtime": 1753662960000, "results": "153", "hashOfConfig": "110"}, {"size": 6178, "mtime": 1753662960000, "results": "154", "hashOfConfig": "110"}, {"size": 3646, "mtime": 1754043560517, "results": "155", "hashOfConfig": "110"}, {"size": 8113, "mtime": 1754043500588, "results": "156", "hashOfConfig": "110"}, {"size": 6935, "mtime": 1753662960000, "results": "157", "hashOfConfig": "110"}, {"size": 5068, "mtime": 1753662960000, "results": "158", "hashOfConfig": "110"}, {"size": 3503, "mtime": 1754043686958, "results": "159", "hashOfConfig": "110"}, {"size": 11618, "mtime": 1754120839603, "results": "160", "hashOfConfig": "110"}, {"size": 12779, "mtime": 1753660406000, "results": "161", "hashOfConfig": "110"}, {"size": 18772, "mtime": 1753660406000, "results": "162", "hashOfConfig": "110"}, {"size": 16373, "mtime": 1753660406000, "results": "163", "hashOfConfig": "110"}, {"size": 20926, "mtime": 1754121648073, "results": "164", "hashOfConfig": "110"}, {"size": 40239, "mtime": 1754303788911, "results": "165", "hashOfConfig": "110"}, {"size": 5370, "mtime": 1753663554000, "results": "166", "hashOfConfig": "110"}, {"size": 5637, "mtime": 1753663554000, "results": "167", "hashOfConfig": "110"}, {"size": 1689, "mtime": 1753663554000, "results": "168", "hashOfConfig": "110"}, {"size": 5556, "mtime": 1753663554000, "results": "169", "hashOfConfig": "110"}, {"size": 15986, "mtime": 1754296296708, "results": "170", "hashOfConfig": "110"}, {"size": 14780, "mtime": 1754317072373, "results": "171", "hashOfConfig": "110"}, {"size": 19464, "mtime": 1753667086000, "results": "172", "hashOfConfig": "110"}, {"size": 9333, "mtime": 1754120749717, "results": "173", "hashOfConfig": "110"}, {"size": 13135, "mtime": 1754121313818, "results": "174", "hashOfConfig": "110"}, {"size": 13718, "mtime": 1753660406000, "results": "175", "hashOfConfig": "110"}, {"size": 2946, "mtime": 1753916458000, "results": "176", "hashOfConfig": "110"}, {"size": 5117, "mtime": 1753916458000, "results": "177", "hashOfConfig": "110"}, {"size": 3744, "mtime": 1753916090000, "results": "178", "hashOfConfig": "110"}, {"size": 8720, "mtime": 1754316375992, "results": "179", "hashOfConfig": "110"}, {"size": 7239, "mtime": 1754324730283, "results": "180", "hashOfConfig": "110"}, {"size": 5908, "mtime": 1754286269451, "results": "181", "hashOfConfig": "110"}, {"size": 139, "mtime": 1753662156000, "results": "182", "hashOfConfig": "110"}, {"size": 8156, "mtime": 1754123202462, "results": "183", "hashOfConfig": "110"}, {"size": 5802, "mtime": 1753916090000, "results": "184", "hashOfConfig": "110"}, {"size": 4753, "mtime": 1753662156000, "results": "185", "hashOfConfig": "110"}, {"size": 329, "mtime": 1753662156000, "results": "186", "hashOfConfig": "110"}, {"size": 6210, "mtime": 1754041438145, "results": "187", "hashOfConfig": "110"}, {"size": 7754, "mtime": 1754324345552, "results": "188", "hashOfConfig": "110"}, {"size": 13056, "mtime": 1754196793734, "results": "189", "hashOfConfig": "110"}, {"size": 10086, "mtime": 1754296075813, "results": "190", "hashOfConfig": "110"}, {"size": 623, "mtime": 1753979082425, "results": "191", "hashOfConfig": "110"}, {"size": 6960, "mtime": 1754290875021, "results": "192", "hashOfConfig": "110"}, {"size": 8078, "mtime": 1754026444720, "results": "193", "hashOfConfig": "110"}, {"size": 2645, "mtime": 1754026405389, "results": "194", "hashOfConfig": "110"}, {"size": 8796, "mtime": 1754030789819, "results": "195", "hashOfConfig": "110"}, {"size": 27243, "mtime": 1754290751151, "results": "196", "hashOfConfig": "110"}, {"size": 5668, "mtime": 1754050276943, "results": "197", "hashOfConfig": "110"}, {"size": 10660, "mtime": 1754318186933, "results": "198", "hashOfConfig": "110"}, {"size": 8055, "mtime": 1754050911094, "results": "199", "hashOfConfig": "110"}, {"size": 24107, "mtime": 1754055910235, "results": "200", "hashOfConfig": "110"}, {"size": 18642, "mtime": 1754289790236, "results": "201", "hashOfConfig": "110"}, {"size": 4832, "mtime": 1754284927004, "results": "202", "hashOfConfig": "110"}, {"size": 11155, "mtime": 1754289886219, "results": "203", "hashOfConfig": "110"}, {"size": 15845, "mtime": 1754195084404, "results": "204", "hashOfConfig": "110"}, {"size": 15642, "mtime": 1754195149865, "results": "205", "hashOfConfig": "110"}, {"size": 6775, "mtime": 1754324325311, "results": "206", "hashOfConfig": "110"}, {"size": 1009, "mtime": 1754286836782, "results": "207", "hashOfConfig": "110"}, {"size": 5819, "mtime": 1754302971651, "results": "208", "hashOfConfig": "110"}, {"size": 8994, "mtime": 1754289962090, "results": "209", "hashOfConfig": "110"}, {"size": 14588, "mtime": 1754318206343, "results": "210", "hashOfConfig": "110"}, {"size": 20788, "mtime": 1754301009875, "results": "211", "hashOfConfig": "110"}, {"size": 13482, "mtime": 1754300981568, "results": "212", "hashOfConfig": "110"}, {"size": 2628, "mtime": 1754298424105, "results": "213", "hashOfConfig": "110"}, {"size": 7571, "mtime": 1754301031662, "results": "214", "hashOfConfig": "110"}, {"size": 15107, "mtime": 1754326339751, "results": "215", "hashOfConfig": "110"}, {"size": 20183, "mtime": 1754326527281, "results": "216", "hashOfConfig": "110"}, {"size": 6593, "mtime": 1754326501301, "results": "217", "hashOfConfig": "110"}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7s4ywu", {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx", ["542", "543", "544", "545"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx", ["546", "547"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx", ["548"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx", ["549", "550", "551", "552"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx", ["553", "554", "555", "556", "557", "558"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx", ["559"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx", ["560", "561", "562"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx", ["563", "564", "565", "566"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx", ["567", "568", "569"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx", ["570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx", ["585", "586", "587"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx", ["588"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx", ["589"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\KeyboardShortcutsContext.jsx", ["590", "591"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ThemeContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\accessibility\\AccessibilityProvider.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-overview\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\rolePermissions.js", ["592", "593"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiChecklistService.js", ["594"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\errorHandling.js", ["595"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateOrganizationModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realApiService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\teamService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\notificationService.js", ["596"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\analytics\\index.jsx", ["597"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\billing\\index.jsx", ["598"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realAuthService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\hooks\\useUserProfile.js", ["599"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\NotificationDropdown.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateTaskModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\AIReportModal.jsx", ["600"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiReportService.js", ["601"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\projectEventService.js", ["602"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\charts\\ReportCharts.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateAIProjectModal.jsx", ["603", "604", "605", "606", "607"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\EnhancedCreateAIProjectModal.jsx", ["608"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\ProjectExportButton.jsx", [], [], {"ruleId": "609", "severity": 1, "message": "610", "line": 20, "column": 11, "nodeType": "611", "messageId": "612", "endLine": 20, "endColumn": 15}, {"ruleId": "609", "severity": 1, "message": "613", "line": 61, "column": 19, "nodeType": "611", "messageId": "612", "endLine": 61, "endColumn": 29}, {"ruleId": "609", "severity": 1, "message": "614", "line": 287, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 287, "endColumn": 21}, {"ruleId": "609", "severity": 1, "message": "615", "line": 292, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 292, "endColumn": 23}, {"ruleId": "609", "severity": 1, "message": "616", "line": 69, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 69, "endColumn": 17}, {"ruleId": "617", "severity": 1, "message": "618", "line": 191, "column": 6, "nodeType": "619", "endLine": 191, "endColumn": 52, "suggestions": "620"}, {"ruleId": "609", "severity": 1, "message": "621", "line": 22, "column": 27, "nodeType": "611", "messageId": "612", "endLine": 22, "endColumn": 45}, {"ruleId": "609", "severity": 1, "message": "622", "line": 8, "column": 8, "nodeType": "611", "messageId": "612", "endLine": 8, "endColumn": 19}, {"ruleId": "609", "severity": 1, "message": "623", "line": 19, "column": 5, "nodeType": "611", "messageId": "612", "endLine": 19, "endColumn": 22}, {"ruleId": "609", "severity": 1, "message": "624", "line": 23, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 23, "endColumn": 21}, {"ruleId": "609", "severity": 1, "message": "616", "line": 25, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 25, "endColumn": 17}, {"ruleId": "609", "severity": 1, "message": "625", "line": 28, "column": 5, "nodeType": "611", "messageId": "612", "endLine": 28, "endColumn": 16}, {"ruleId": "609", "severity": 1, "message": "626", "line": 30, "column": 5, "nodeType": "611", "messageId": "612", "endLine": 30, "endColumn": 27}, {"ruleId": "609", "severity": 1, "message": "627", "line": 31, "column": 14, "nodeType": "611", "messageId": "612", "endLine": 31, "endColumn": 28}, {"ruleId": "609", "severity": 1, "message": "628", "line": 35, "column": 23, "nodeType": "611", "messageId": "612", "endLine": 35, "endColumn": 37}, {"ruleId": "609", "severity": 1, "message": "629", "line": 36, "column": 23, "nodeType": "611", "messageId": "612", "endLine": 36, "endColumn": 37}, {"ruleId": "617", "severity": 1, "message": "630", "line": 192, "column": 6, "nodeType": "619", "endLine": 192, "endColumn": 22, "suggestions": "631"}, {"ruleId": "609", "severity": 1, "message": "632", "line": 8, "column": 49, "nodeType": "611", "messageId": "612", "endLine": 8, "endColumn": 60}, {"ruleId": "609", "severity": 1, "message": "633", "line": 4, "column": 31, "nodeType": "611", "messageId": "612", "endLine": 4, "endColumn": 48}, {"ruleId": "609", "severity": 1, "message": "634", "line": 12, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 12, "endColumn": 25}, {"ruleId": "609", "severity": 1, "message": "635", "line": 65, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 65, "endColumn": 31}, {"ruleId": "609", "severity": 1, "message": "616", "line": 11, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 11, "endColumn": 17}, {"ruleId": "609", "severity": 1, "message": "636", "line": 12, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 12, "endColumn": 16}, {"ruleId": "609", "severity": 1, "message": "637", "line": 12, "column": 18, "nodeType": "611", "messageId": "612", "endLine": 12, "endColumn": 27}, {"ruleId": "609", "severity": 1, "message": "638", "line": 57, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 57, "endColumn": 18}, {"ruleId": "609", "severity": 1, "message": "639", "line": 9, "column": 8, "nodeType": "611", "messageId": "612", "endLine": 9, "endColumn": 22}, {"ruleId": "640", "severity": 1, "message": "641", "line": 151, "column": 5, "nodeType": "642", "messageId": "643", "endLine": 169, "endColumn": 6}, {"ruleId": "609", "severity": 1, "message": "644", "line": 181, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 181, "endColumn": 21}, {"ruleId": "609", "severity": 1, "message": "645", "line": 11, "column": 34, "nodeType": "611", "messageId": "612", "endLine": 11, "endColumn": 57}, {"ruleId": "609", "severity": 1, "message": "646", "line": 18, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 18, "endColumn": 36}, {"ruleId": "617", "severity": 1, "message": "647", "line": 104, "column": 6, "nodeType": "619", "endLine": 104, "endColumn": 24, "suggestions": "648"}, {"ruleId": "617", "severity": 1, "message": "647", "line": 118, "column": 6, "nodeType": "619", "endLine": 118, "endColumn": 24, "suggestions": "649"}, {"ruleId": "609", "severity": 1, "message": "650", "line": 217, "column": 30, "nodeType": "611", "messageId": "612", "endLine": 217, "endColumn": 51}, {"ruleId": "609", "severity": 1, "message": "651", "line": 218, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 218, "endColumn": 19}, {"ruleId": "609", "severity": 1, "message": "652", "line": 223, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 223, "endColumn": 20}, {"ruleId": "609", "severity": 1, "message": "653", "line": 224, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 224, "endColumn": 26}, {"ruleId": "609", "severity": 1, "message": "654", "line": 227, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 227, "endColumn": 30}, {"ruleId": "609", "severity": 1, "message": "655", "line": 248, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 248, "endColumn": 22}, {"ruleId": "609", "severity": 1, "message": "656", "line": 254, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 254, "endColumn": 28}, {"ruleId": "609", "severity": 1, "message": "657", "line": 265, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 265, "endColumn": 25}, {"ruleId": "609", "severity": 1, "message": "658", "line": 274, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 274, "endColumn": 24}, {"ruleId": "609", "severity": 1, "message": "659", "line": 289, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 289, "endColumn": 34}, {"ruleId": "609", "severity": 1, "message": "660", "line": 312, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 312, "endColumn": 33}, {"ruleId": "609", "severity": 1, "message": "616", "line": 20, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 20, "endColumn": 17}, {"ruleId": "617", "severity": 1, "message": "661", "line": 27, "column": 6, "nodeType": "619", "endLine": 27, "endColumn": 19, "suggestions": "662"}, {"ruleId": "609", "severity": 1, "message": "663", "line": 76, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 76, "endColumn": 24}, {"ruleId": "609", "severity": 1, "message": "639", "line": 6, "column": 8, "nodeType": "611", "messageId": "612", "endLine": 6, "endColumn": 22}, {"ruleId": "617", "severity": 1, "message": "664", "line": 30, "column": 6, "nodeType": "619", "endLine": 30, "endColumn": 30, "suggestions": "665"}, {"ruleId": "617", "severity": 1, "message": "666", "line": 135, "column": 6, "nodeType": "619", "endLine": 135, "endColumn": 62, "suggestions": "667"}, {"ruleId": "617", "severity": 1, "message": "668", "line": 151, "column": 6, "nodeType": "619", "endLine": 151, "endColumn": 8, "suggestions": "669"}, {"ruleId": "609", "severity": 1, "message": "670", "line": 124, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 124, "endColumn": 20}, {"ruleId": "671", "severity": 1, "message": "672", "line": 185, "column": 1, "nodeType": "673", "endLine": 193, "endColumn": 3}, {"ruleId": "671", "severity": 1, "message": "672", "line": 338, "column": 1, "nodeType": "673", "endLine": 342, "endColumn": 3}, {"ruleId": "671", "severity": 1, "message": "672", "line": 250, "column": 1, "nodeType": "673", "endLine": 260, "endColumn": 3}, {"ruleId": "609", "severity": 1, "message": "639", "line": 1, "column": 8, "nodeType": "611", "messageId": "612", "endLine": 1, "endColumn": 22}, {"ruleId": "617", "severity": 1, "message": "674", "line": 34, "column": 6, "nodeType": "619", "endLine": 34, "endColumn": 18, "suggestions": "675"}, {"ruleId": "617", "severity": 1, "message": "676", "line": 36, "column": 6, "nodeType": "619", "endLine": 36, "endColumn": 8, "suggestions": "677"}, {"ruleId": "617", "severity": 1, "message": "678", "line": 166, "column": 6, "nodeType": "619", "endLine": 166, "endColumn": 33, "suggestions": "679"}, {"ruleId": "617", "severity": 1, "message": "680", "line": 23, "column": 6, "nodeType": "619", "endLine": 23, "endColumn": 23, "suggestions": "681"}, {"ruleId": "671", "severity": 1, "message": "672", "line": 467, "column": 1, "nodeType": "673", "endLine": 471, "endColumn": 3}, {"ruleId": "671", "severity": 1, "message": "672", "line": 82, "column": 1, "nodeType": "673", "endLine": 89, "endColumn": 3}, {"ruleId": "609", "severity": 1, "message": "682", "line": 1, "column": 27, "nodeType": "611", "messageId": "612", "endLine": 1, "endColumn": 36}, {"ruleId": "609", "severity": 1, "message": "683", "line": 17, "column": 10, "nodeType": "611", "messageId": "612", "endLine": 17, "endColumn": 24}, {"ruleId": "609", "severity": 1, "message": "684", "line": 33, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 33, "endColumn": 24}, {"ruleId": "609", "severity": 1, "message": "685", "line": 41, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 41, "endColumn": 26}, {"ruleId": "609", "severity": 1, "message": "686", "line": 217, "column": 9, "nodeType": "611", "messageId": "612", "endLine": 217, "endColumn": 19}, {"ruleId": "609", "severity": 1, "message": "682", "line": 1, "column": 27, "nodeType": "611", "messageId": "612", "endLine": 1, "endColumn": 36}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'setMembers' is assigned a value but never used.", "'canEditCards' is assigned a value but never used.", "'canDeleteCards' is assigned a value but never used.", "'loading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'currentUser?.email', 'currentUser?.firstName', 'currentUser?.lastName', and 'userRole'. Either include them or remove the dependency array.", "ArrayExpression", ["687"], "'setSidebarExpanded' is assigned a value but never used.", "'authService' is defined but never used.", "'updateUserProfile' is assigned a value but never used.", "'currentUser' is assigned a value but never used.", "'userProfile' is assigned a value but never used.", "'availableOrganizations' is assigned a value but never used.", "'profileLoading' is assigned a value but never used.", "'setSearchValue' is assigned a value but never used.", "'setFilterValue' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentUser' and 'organizations.length'. Either include them or remove the dependency array.", ["688"], "'handleError' is defined but never used.", "'getSuggestedItems' is defined but never used.", "'showSuggestions' is assigned a value but never used.", "'handleAddSuggestedItem' is assigned a value but never used.", "'saving' is assigned a value but never used.", "'setSaving' is assigned a value but never used.", "'logoFile' is assigned a value but never used.", "'realApiService' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'getRoleColor' is assigned a value but never used.", "'refreshProjectsGlobally' is defined but never used.", "'isNotificationDropdownOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadProjects'. Either include it or remove the dependency array.", ["689"], ["690"], "'setNotificationFilter' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'unreadCount' is assigned a value but never used.", "'highPriorityCount' is assigned a value but never used.", "'filteredNotifications' is assigned a value but never used.", "'markAllAsRead' is assigned a value but never used.", "'getNotificationIcon' is assigned a value but never used.", "'getPriorityColor' is assigned a value but never used.", "'formatTimestamp' is assigned a value but never used.", "'getRoleBasedNotifications' is assigned a value but never used.", "'handleNotificationAction' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadTasks'. Either include it or remove the dependency array.", ["691"], "'priorityOptions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadTeamMembers'. Either include it or remove the dependency array.", ["692"], "React Hook useCallback has a missing dependency: 'defaultShortcuts'. Either include it or remove the dependency array.", ["693"], "React Hook useEffect has a missing dependency: 'defaultShortcuts'. Either include it or remove the dependency array.", ["694"], "'permissions' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'loadAnalyticsData'. Either include it or remove the dependency array.", ["695"], "React Hook useEffect has a missing dependency: 'loadBillingData'. Either include it or remove the dependency array.", ["696"], "React Hook useEffect has a missing dependency: 'loadUserProfile'. Either include it or remove the dependency array.", ["697"], "React Hook useEffect has a missing dependency: 'generateReport'. Either include it or remove the dependency array.", ["698"], "'useEffect' is defined but never used.", "'projectPreview' is assigned a value but never used.", "'teamSizeOptions' is assigned a value but never used.", "'experienceOptions' is assigned a value but never used.", "'handleBack' is assigned a value but never used.", {"desc": "699", "fix": "700"}, {"desc": "701", "fix": "702"}, {"desc": "703", "fix": "704"}, {"desc": "705", "fix": "706"}, {"desc": "707", "fix": "708"}, {"desc": "709", "fix": "710"}, {"desc": "711", "fix": "712"}, {"desc": "713", "fix": "714"}, {"desc": "715", "fix": "716"}, {"desc": "717", "fix": "718"}, {"desc": "719", "fix": "720"}, {"desc": "721", "fix": "722"}, "Update the dependencies array to be: [currentOrganization, searchQuery, roleFilter, currentUser?.firstName, currentUser?.lastName, currentUser?.email, userRole]", {"range": "723", "text": "724"}, "Update the dependencies array to be: [currentUser, location.state, organizations.length]", {"range": "725", "text": "726"}, "Update the dependencies array to be: [loadProjects, organization.id]", {"range": "727", "text": "728"}, "Update the dependencies array to be: [loadProjects, organization?.id]", {"range": "729", "text": "730"}, "Update the dependencies array to be: [loadTasks, project?.id]", {"range": "731", "text": "732"}, "Update the dependencies array to be: [isOpen, loadTeamMembers, organizationId]", {"range": "733", "text": "734"}, "Update the dependencies array to be: [defaultShortcuts, sequenceTimeout, keySequence]", {"range": "735", "text": "736"}, "Update the dependencies array to be: [defaultShortcuts]", {"range": "737", "text": "738"}, "Update the dependencies array to be: [loadAnalyticsData, timePeriod]", {"range": "739", "text": "740"}, "Update the dependencies array to be: [loadBillingData]", {"range": "741", "text": "742"}, "Update the dependencies array to be: [isAuthenticated, authUser, loadUserProfile]", {"range": "743", "text": "744"}, "Update the dependencies array to be: [generateReport, isOpen, project]", {"range": "745", "text": "746"}, [6367, 6413], "[currentOrganization, searchQuery, roleFilter, currentUser?.firstName, currentUser?.lastName, currentUser?.email, userRole]", [7762, 7778], "[currentUser, location.state, organizations.length]", [3947, 3965], "[loadProjects, organization.id]", [4490, 4508], "[loadProjects, organization?.id]", [1142, 1155], "[loadTasks, project?.id]", [974, 998], "[isO<PERSON>, loadTeamMembers, organizationId]", [4586, 4642], "[defaultShortcuts, sequenceTimeout, keySequence]", [5058, 5060], "[defaultShortcuts]", [1168, 1180], "[loadAnalyticsData, timePeriod]", [1271, 1273], "[loadBillingData]", [5592, 5619], "[isAuthenticated, authUser, loadUserProfile]", [696, 713], "[generateReport, isOpen, project]"]